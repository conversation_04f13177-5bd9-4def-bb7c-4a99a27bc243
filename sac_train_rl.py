#!/usr/bin/env python3
# sac_train_rl.py – SAC + EvalCallback + early-stop + Optuna – v2025-05-20-debug

from __future__ import annotations
import os, re, argparse, datetime as dt, json, shutil, warnings, traceback, time, psutil, sys, gc
from pathlib import Path
import multiprocessing
from sac_environement import MultiHorizonEnv
from common.grafana_utils import GrafanaUtils
from common.mongo_utils import MongoUtils

# Détection du nombre de cœurs disponibles
NUM_CORES = multiprocessing.cpu_count()

# Configuration pour utiliser tous les cœurs disponibles
# Remplacer les limitations actuelles par une configuration optimisée
os.environ.update(
    # Utiliser tous les cœurs disponibles pour les opérations parallèles
    OMP_NUM_THREADS=str(NUM_CORES),
    MKL_NUM_THREADS=str(NUM_CORES),
    OPENBLAS_NUM_THREADS=str(NUM_CORES),
    NUMEXPR_NUM_THREADS=str(NUM_CORES),
    VECLIB_MAXIMUM_THREADS=str(NUM_CORES),
    # Supprimer la restriction d'affinité CPU
    GOMP_CPU_AFFINITY=""
)

import gymnasium as gym
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
import torch
# Configurer PyTorch pour utiliser tous les cœurs disponibles
torch.set_num_threads(NUM_CORES)
torch.set_num_interop_threads(max(1, NUM_CORES // 2))

from stable_baselines3 import SAC
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from stable_baselines3.common.callbacks import (
    EvalCallback, StopTrainingOnRewardThreshold,
    StopTrainingOnNoModelImprovement, CallbackList, BaseCallback
)

try:
    import optuna
except ImportError:
    optuna = None


# Configuration des chemins et constantes
CHECKPOINT_DIR = Path("/home/<USER>/cryptobot/models")
CHECKPOINT_DIR.mkdir(exist_ok=True)
DATA_DIR = Path("data/features")
DATA_DIR.mkdir(exist_ok=True)
CHUNK = 10_000
REQUIRED_COLS = {"uid", "run_at", "horizon_h", "expected_ret"}

# Configuration de l'évaluation et de l'arrêt précoce
EVAL_FREQ = 30_000
EVAL_EPISODES = 3
EARLY_REWARD = 0.02
NO_IMPROVE_EVALS = 3

NORM_SUFFIX = "_z"

# Configuration du logger
SERVICE = "sac_train"
logger = GrafanaUtils(service=SERVICE)
warnings.filterwarnings("ignore", category=FutureWarning)

def debug(msg):
    """Affiche un message de débogage."""
    print(f"\033[96m[DEBUG] {msg}\033[0m")

def log_exception(func):
    """Décorateur pour journaliser les exceptions."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_msg = f"Exception dans {func.__name__}: {str(e)}"
            print(f"\033[91m[ERROR] {error_msg}\033[0m")
            logger.send_log(error_msg, "error", extra_labels={
                "function": func.__name__,
                "error": str(e),
                "traceback": traceback.format_exc()[:500]
            })
            raise
    return wrapper

def _apply_norm(df: pd.DataFrame, stats: dict) -> pd.DataFrame:
    """Applique la normalisation aux colonnes du DataFrame."""
    for col, s in stats.items():
        zcol = f"{col}{NORM_SUFFIX}"
        if zcol in df.columns or col not in df.columns:
            continue
        mu, sd = s.get("mean", 0.0), s.get("std", 1.0) or 1.0
        df[zcol] = (df[col] - mu) / sd
    return df

class MetricLoggerCallback(BaseCallback):
    """Callback pour enregistrer les métriques d'entraînement."""
    def __init__(self, asset: str, mongo: MongoUtils, verbose: int = 0):
        super().__init__(verbose)
        self.asset, self.mongo = asset, mongo
        self.rew, self.length = [], []

    def _on_step(self) -> bool:
        for info in self.locals.get("infos", []):
            if "episode" in info:
                self.rew.append(info["episode"]["r"])
                self.length.append(info["episode"]["l"])
        return True

    def _on_rollout_end(self) -> None:
        if not self.rew:
            return
        now = dt.datetime.utcnow()
        logs = {
            "reward_mean": float(np.mean(self.rew)),
            "reward_std": float(np.std(self.rew)),
            "episode_len_mean": float(np.mean(self.length)),
        }
        if hasattr(self.model, "logger"):
            for k, v in self.model.logger.name_to_value.items():
                if k.startswith("train/"):
                    logs[k.replace("train/", "loss_")] = v
        for k, v in logs.items():
            logger.send_log(k, "info", extra_labels={"asset": self.asset, "value": round(v, 5)})
            self.mongo.db["train_metrics"].insert_one(
                {"asset": self.asset, "metric": k, "value": v, "timestamp": now}
            )
        self.rew.clear()
        self.length.clear()

def _valid_parquet(p: Path) -> bool:
    """Vérifie si un fichier parquet est valide."""
    try:
        pq.ParquetFile(p)
        return True
    except Exception:
        logger.send_log("skip_bad_parquet", "warning", extra_labels={"file": p.name})
        debug(f"Skip invalid parquet: {p.name}")
        return False

@log_exception
def _load_asset_df(asset: str, min_rows: int = 1000) -> pd.DataFrame:
    """Charge les données d'un asset depuis un fichier parquet."""
    start_time = time.time()
    logger.send_log("loading_asset_data", "info", extra_labels={"asset": asset, "min_rows": min_rows})
    
    try:
        # Chercher le fichier parquet
        parquet_path = DATA_DIR / f"features-{asset}.parquet"
        if not parquet_path.exists():
            # Chercher dans d'autres fichiers
            fallback_path = DATA_DIR / "features-FALLBACK.parquet"
            if fallback_path.exists():
                logger.send_log("using_fallback_data", "warning", extra_labels={
                    "asset": asset, 
                    "fallback_path": str(fallback_path)
                })
                parquet_path = fallback_path
            else:
                logger.send_log("no_data_found", "error", extra_labels={
                    "asset": asset,
                    "searched_paths": [str(DATA_DIR / f"features-{asset}.parquet"), str(fallback_path)]
                })
                return pd.DataFrame()
        
        # Lire le fichier parquet
        df = pd.read_parquet(parquet_path)
        
        # Filtrer pour l'asset spécifique si nécessaire
        if "uid" in df.columns:
            df = df[df["uid"] == asset]
        else:
            # Ajouter la colonne uid si elle n'existe pas
            df["uid"] = asset
        
        # Vérifier et ajouter les colonnes manquantes requises par MultiHorizonEnv
        if "horizon_h" not in df.columns:
            # Ajouter des horizons par défaut (3, 6, 9, 12, 15, 18, 21, 24)
            horizons = [3, 6, 9, 12, 15, 18, 21, 24]
            # Répliquer les données pour chaque horizon
            new_dfs = []
            for h in horizons:
                df_h = df.copy()
                df_h["horizon_h"] = h
                new_dfs.append(df_h)
            df = pd.concat(new_dfs, ignore_index=True)
            logger.send_log("added_horizons", "warning", extra_labels={
                "asset": asset,
                "horizons": horizons
            })
        
        if "expected_ret" not in df.columns:
            # Ajouter une colonne expected_ret avec des valeurs aléatoires
            df["expected_ret"] = np.random.normal(0, 0.01, size=len(df))
            logger.send_log("added_expected_ret", "warning", extra_labels={"asset": asset})
        
        # Vérifier le nombre de lignes
        if len(df) < min_rows:
            logger.send_log("insufficient_rows", "warning", extra_labels={
                "asset": asset,
                "rows": len(df),
                "min_rows": min_rows
            })
            if len(df) == 0:
                return pd.DataFrame()
        
        # Journaliser le temps de chargement
        load_time = time.time() - start_time
        logger.send_log("data_loaded", "info", extra_labels={
            "asset": asset,
            "rows": len(df),
            "load_time_s": round(load_time, 2)
        })
        
        return df
    except Exception as e:
        logger.send_log("load_error", "error", extra_labels={
            "asset": asset,
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        return pd.DataFrame()

def _prepare_df_for_env(df):
    """Prépare le DataFrame pour l'environnement en convertissant les colonnes de date/heure."""
    if df.empty:
        return df
    
    # Sauvegarder la colonne uid pour la restaurer plus tard
    uid_column = None
    if "uid" in df.columns:
        uid_column = df["uid"].copy()
    
    # Convertir les colonnes de date/heure en format numérique
    datetime_cols = ["time", "closeTime", "created_at", "updated_at", "date", "timestamp", "run_at"]
    
    for col in datetime_cols:
        if col in df.columns:
            try:
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    # Convertir en timestamp Unix (secondes depuis epoch)
                    df[f"{col}_ts"] = df[col].astype('int64') // 10**9
                    # Supprimer la colonne originale pour éviter l'erreur
                    df = df.drop(columns=[col])
                elif isinstance(df[col].iloc[0], str):
                    # Tenter de convertir les chaînes de date/heure
                    try:
                        df[f"{col}_ts"] = pd.to_datetime(df[col]).astype('int64') // 10**9
                        # Supprimer la colonne originale
                        df = df.drop(columns=[col])
                    except:
                        # Si la conversion échoue, supprimer la colonne
                        df = df.drop(columns=[col])
                        logger.send_log("column_dropped", "warning", extra_labels={
                            "column": col, "reason": "conversion_failed"
                        })
                else:
                    # Si ce n'est ni datetime ni string, supprimer la colonne
                    df = df.drop(columns=[col])
                    logger.send_log("column_dropped", "warning", extra_labels={
                        "column": col, "reason": "unknown_type"
                    })
            except Exception as e:
                # En cas d'erreur, supprimer la colonne
                if col in df.columns:
                    df = df.drop(columns=[col])
                logger.send_log("column_dropped", "warning", extra_labels={
                    "column": col, "error": str(e)
                })
    
    # Supprimer toutes les colonnes non numériques restantes, sauf uid
    non_numeric_cols = [col for col in df.select_dtypes(exclude=['number']).columns 
                        if col != "uid"]
    if non_numeric_cols:
        logger.send_log("dropping_non_numeric", "warning", extra_labels={"columns": non_numeric_cols})
        df = df.drop(columns=non_numeric_cols)
    
    # Restaurer la colonne uid si elle a été supprimée
    if uid_column is not None and "uid" not in df.columns:
        df["uid"] = uid_column
        logger.send_log("uid_column_restored", "info")
    
    # S'assurer que toutes les colonnes numériques sont de type float32
    numeric_cols = df.select_dtypes(include=['number']).columns
    for col in numeric_cols:
        df[col] = df[col].astype(np.float32)
    
    # Remplacer les valeurs infinies par des valeurs grandes mais finies
    df = df.replace([np.inf, -np.inf], [1e9, -1e9])
    
    # Remplacer les NaN par des zéros
    df = df.fillna(0)
    
    return df

def _load_model_json(asset: str) -> dict:
    """Charge le fichier JSON associé au modèle le plus récent."""
    try:
        # Trouver le modèle le plus récent
        models = list(CHECKPOINT_DIR.glob(f"sac_{asset}_*.zip"))
        if not models:
            logger.send_log("no_model_found", "warning", extra_labels={"asset": asset})
            return {}
        
        # Trier par date de modification (le plus récent en premier)
        latest_model = sorted(models, key=lambda p: p.stat().st_mtime, reverse=True)[0]
        
        # Charger le fichier JSON associé
        json_path = latest_model.with_suffix(".json")
        if not json_path.exists():
            logger.send_log("no_json_found", "warning", extra_labels={
                "asset": asset,
                "model": latest_model.name
            })
            return {}
        
        with open(json_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.send_log("json_load_error", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
        return {}

@log_exception
def train_model(asset: str, tune: bool = False, n_envs: int = 1) -> None:
    """Entraîne un modèle SAC pour un asset donné."""
    torch.set_num_threads(max(1, NUM_CORES // max(1, n_envs)))
    # Journaliser l'utilisation CPU au démarrage
    cpu_info = {
        "cores": NUM_CORES,
        "threads_per_core": psutil.cpu_count() // psutil.cpu_count(logical=False) if psutil.cpu_count(logical=False) else 1,
        "current_load": psutil.cpu_percent(interval=0.1),
        "torch_threads": torch.get_num_threads(),
        "torch_interop_threads": torch.get_num_interop_threads(),
        "omp_threads": os.environ.get("OMP_NUM_THREADS")
    }
    
    logger.send_log("training_started", "info", extra_labels={
        "asset": asset, 
        "tune": tune,
        "cpu_info": cpu_info
    })
    
    try:
        # Charger les données
        df = _load_asset_df(asset)
        if df.empty:
            logger.send_log("empty_dataframe", "error", extra_labels={"asset": asset})
            return
        
        # Charger les statistiques de normalisation depuis le JSON
        model_json = _load_model_json(asset)
        norm_stats = model_json.get("norm_stats", {})
        
        # Appliquer la normalisation si nécessaire
        if norm_stats:
            df = _apply_norm(df, norm_stats)
            logger.send_log("normalization_applied", "info", extra_labels={
                "asset": asset,
                "stats_count": len(norm_stats)
            })
        
        # Préparer les données pour l'environnement
        df = _prepare_df_for_env(df)
        
        # Vérifier qu'il n'y a pas de colonnes non numériques
        non_numeric_cols = df.select_dtypes(exclude=['number']).columns.tolist()
        if non_numeric_cols:
            logger.send_log("non_numeric_columns", "warning", extra_labels={
                "asset": asset,
                "columns": non_numeric_cols
            })
            # Supprimer les colonnes non numériques
            df = df.drop(columns=non_numeric_cols)
            logger.send_log("dropped_non_numeric", "info", extra_labels={
                "asset": asset,
                "columns_dropped": len(non_numeric_cols)
            })
        
        # Journaliser les informations sur les données
        logger.send_log("data_loaded", "info", extra_labels={
            "asset": asset,
            "rows": len(df),
            "columns": len(df.columns),
            "memory_usage_mb": round(df.memory_usage(deep=True).sum() / 1024 / 1024, 1),
            "loading_time_ms": round((time.time() - time.time()) * 1000, 2)
        })
        
        # Créer l'environnement
        try:
            # Création d'environnements vectorisés pour exploiter plusieurs cœurs CPU
            def _make_env():
                return MultiHorizonEnv(df)

            if n_envs > 1:
                env = SubprocVecEnv([_make_env for _ in range(n_envs)], start_method="fork")
                logger.send_log("subproc_env_created", "info", extra_labels={
                    "asset": asset,
                    "n_envs": n_envs
                })
            else:
                env = DummyVecEnv([_make_env])
                logger.send_log("dummy_env_created", "info", extra_labels={"asset": asset})
        except Exception as e:
            logger.send_log("env_creation_error", "error", extra_labels={
                "asset": asset,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            return
        
        # Configurer le modèle
        model_params = {
            "policy": "MlpPolicy",
            "learning_rate": 3e-4,
            "buffer_size": 50_000,
            "batch_size": 256,  # Augmenté pour mieux utiliser les cœurs multiples
            "tau": 0.005,
            "gamma": 0.99,
            "train_freq": (1, "episode"),
            "gradient_steps": -1,
            "action_noise": None,
            "learning_starts": 1000,
            "verbose": 1,
            "tensorboard_log": None,
            "policy_kwargs": {
                "net_arch": {
                    "pi": [64, 64],  # Augmenté pour mieux utiliser les cœurs multiples
                    "qf": [64, 64]   # Augmenté pour mieux utiliser les cœurs multiples
                }
            }
        }
        
        # Configurer les callbacks
        mongo = MongoUtils()
        mongo.connect(service=SERVICE)
        
        # Callback pour l'arrêt précoce
        stop_callback = StopTrainingOnRewardThreshold(
            reward_threshold=EARLY_REWARD,
            verbose=0
        )
        
        # Callback pour l'évaluation avec l'arrêt si pas d'amélioration intégré
        eval_callback = EvalCallback(
            env,
            callback_on_new_best=stop_callback,
            eval_freq=EVAL_FREQ,
            n_eval_episodes=EVAL_EPISODES,
            verbose=1,
            best_model_save_path=str(CHECKPOINT_DIR),
            log_path=str(CHECKPOINT_DIR),
            deterministic=True
        )
        
        # Ajouter StopTrainingOnNoModelImprovement comme attribut de EvalCallback
        # au lieu de l'utiliser comme un callback indépendant
        eval_callback.callback_on_no_improvement = StopTrainingOnNoModelImprovement(
            max_no_improvement_evals=NO_IMPROVE_EVALS,
            min_evals=5,
            verbose=0
        )
        
        # Callback pour la journalisation des métriques
        metric_logger = MetricLoggerCallback(asset, mongo)
        
        # Combiner les callbacks (sans no_improve_callback)
        callbacks = CallbackList([eval_callback, metric_logger])
        
        # Créer et entraîner le modèle
        model = SAC(**model_params, env=env)
        
        # Entraîner le modèle
        total_timesteps = 100_000
        
        # Journaliser les informations sur l'utilisation CPU avant l'entraînement
        pre_train_cpu = {
            "percent": psutil.cpu_percent(interval=1, percpu=True),
            "avg_load": sum(psutil.cpu_percent(interval=0, percpu=True)) / NUM_CORES,
            "memory_percent": psutil.virtual_memory().percent
        }
        
        logger.send_log("training_model", "info", extra_labels={
            "asset": asset,
            "timesteps": total_timesteps,
            "cpu_usage": pre_train_cpu
        })
        
        # Démarrer le chronomètre pour mesurer le temps d'entraînement
        start_time = time.time()
        
        model.learn(
            total_timesteps=total_timesteps,
            callback=callbacks,
            tb_log_name=f"SAC_{asset}"
        )
        
        # Calculer le temps d'entraînement
        training_time = time.time() - start_time
        
        # Journaliser les informations sur l'utilisation CPU après l'entraînement
        post_train_cpu = {
            "percent": psutil.cpu_percent(interval=1, percpu=True),
            "avg_load": sum(psutil.cpu_percent(interval=0, percpu=True)) / NUM_CORES,
            "memory_percent": psutil.virtual_memory().percent,
            "training_time_s": round(training_time, 2),
            "steps_per_second": round(total_timesteps / training_time, 2)
        }
        
        logger.send_log("training_completed", "info", extra_labels={
            "asset": asset,
            "cpu_usage": post_train_cpu
        })
        
        # Sauvegarder le modèle
        timestamp = dt.datetime.now().strftime("%Y%m%d-%H%M%S")
        model_path = CHECKPOINT_DIR / f"sac_{asset}_{timestamp}.zip"
        model.save(model_path)
        
        # Créer le fichier JSON associé
        json_path = model_path.with_suffix(".json")
        
        # Récupérer les noms des features
        feature_cols = [col for col in df.columns if col not in ["uid", "run_at", "horizon_h", "expected_ret"]]
        
        # Créer le contenu du JSON
        json_data = {
            "uid": asset,
            "created_at": dt.datetime.now().isoformat(),
            "features": feature_cols,
            "norm_stats": norm_stats
        }
        
        # Écrire le JSON
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        logger.send_log("model_saved", "info", extra_labels={
            "asset": asset,
            "model_path": str(model_path),
            "json_path": str(json_path)
        })

        # Libération explicite de la mémoire
        del df, env, model
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        # Nettoyer les anciens modèles
        _cleanup_old_models(asset)
        
    except Exception as e:
        logger.send_log("training_error", "error", extra_labels={
            "asset": asset,
            "error": str(e),
            "traceback": traceback.format_exc()
        })

def _cleanup_old_models(asset: str, keep: int = 3):
    """Nettoie les anciens modèles, en gardant seulement les 'keep' plus récents."""
    try:
        # Trouver tous les modèles pour cet asset
        models = list(CHECKPOINT_DIR.glob(f"sac_{asset}_*.zip"))
        
        # Trier par date de modification (le plus récent en premier)
        models = sorted(models, key=lambda p: p.stat().st_mtime, reverse=True)
        
        # Garder seulement les 'keep' plus récents
        if len(models) > keep:
            for old_model in models[keep:]:
                # Supprimer le modèle
                old_model.unlink(missing_ok=True)
                
                # Supprimer le JSON associé
                json_path = old_model.with_suffix(".json")
                if json_path.exists():
                    json_path.unlink()
            
            logger.send_log("old_models_cleaned", "info", extra_labels={
                "asset": asset,
                "removed": len(models) - keep,
                "kept": keep
            })
    except Exception as e:
        logger.send_log("cleanup_error", "warning", extra_labels={
            "asset": asset,
            "error": str(e)
        })

def main():
    """Fonction principale."""
    parser = argparse.ArgumentParser(description="Entraîne un modèle SAC pour un asset donné.")
    parser.add_argument("--asset", type=str, required=True, help="Asset à entraîner (ex: BTCEUR)")
    parser.add_argument("--tune", action="store_true", help="Utiliser Optuna pour optimiser les hyperparamètres")
    parser.add_argument("--trials", type=int, default=20, help="Nombre d'essais pour l'optimisation")
    parser.add_argument("--steps", type=int, default=100_000, help="Nombre d'étapes d'entraînement")
    parser.add_argument("--ep_steps", type=int, default=2_016, help="Nombre d'étapes par épisode")
    parser.add_argument("--steps_per_trial", type=int, default=50_000, help="Étapes par essai d'optimisation")
    parser.add_argument("--cores", type=int, default=NUM_CORES, help="Nombre de cœurs CPU à utiliser (défaut: tous)")
    parser.add_argument("--n_envs", type=int, default=1,
                        help="Nombre d'environnements parallèles (SubprocVecEnv)")
    
    args = parser.parse_args()
    
    # Configurer l'utilisation des cœurs en fonction de l'argument --cores
    if args.cores != NUM_CORES:
        # Reconfigurer les variables d'environnement avec le nombre de cœurs spécifié
        os.environ.update(
            OMP_NUM_THREADS=str(args.cores),
            MKL_NUM_THREADS=str(args.cores),
            OPENBLAS_NUM_THREADS=str(args.cores),
            NUMEXPR_NUM_THREADS=str(args.cores),
            VECLIB_MAXIMUM_THREADS=str(args.cores)
        )
        # Reconfigurer PyTorch
        torch.set_num_threads(args.cores)
        torch.set_num_interop_threads(max(1, args.cores // 2))
        
        logger.send_log("custom_core_config", "info", extra_labels={
            "asset": args.asset,
            "cores_requested": args.cores,
            "cores_available": NUM_CORES
        })
    else:
        logger.send_log("using_all_cores", "info", extra_labels={
            "asset": args.asset,
            "cores": NUM_CORES
        })
    
    # Vérifier si l'optimisation est demandée
    if args.tune and optuna is None:
        logger.send_log("optuna_not_installed", "error")
        print("Erreur: L'optimisation nécessite le package 'optuna'. Installez-le avec 'pip install optuna'.")
        sys.exit(1)
    
    # Entraîner le modèle
    train_model(args.asset, tune=args.tune, n_envs=args.n_envs)

if __name__ == "__main__":
    main()
