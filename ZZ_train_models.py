from flask import Flask, request, jsonify
import numpy as np
import pandas as pd
import xgboost as xgb
import joblib
import os
import time
import threading
import json
import copy
from datetime import datetime, timedelta
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from concurrent.futures import ThreadPoolExecutor
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from common.indicators_utils import IndicatorsUtils
from common.models_utils import ModelsUtils

app = Flask(__name__)

SERVICE = "train_models"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
indicator = IndicatorsUtils(service=SERVICE)
models = ModelsUtils(service=SERVICE)


HORIZONS = models.get_horizon()
NB_THREADS = len(HORIZONS)

MODEL_DIR = "/home/<USER>/cryptobot/models/"
os.makedirs(MODEL_DIR, exist_ok=True)



def extract_features_dataframe(df_input, used_features, mode="regression"):
    """
    S'assure que le DataFrame contient exactement les colonnes utilisées à l'entraînement,
    dans le bon ordre, et uniquement celles-ci, en incluant 'log_return' si présent.
    """
    df = df_input.copy()

    # ⚠️ Ne pas modifier used_features directement !
    full_features = used_features.copy()


    target_col = "log_return" if mode == "regression" else "target"
    if target_col not in full_features:
        full_features.append(target_col)

    # Ajoute les colonnes manquantes
    for col in full_features:
        if col not in df.columns:
            df[col] = np.nan

    # Garde uniquement les colonnes utiles et dans le bon ordre
    df = df[full_features]

    # Diagnostic
    missing_cols = df.columns[df.isnull().any()].tolist()
    if missing_cols:
        logger.send_log(f"⚠️ Colonnes avec valeurs manquantes dans les features d'entrée : {missing_cols}", "warning")

    return df




def train_xgboost_classification(selected_horizon, symbol, df, final_params, used_features, safe_shift_points):
    model_name = selected_horizon["full_name"]
    logger.send_log(f"📌 - {symbol} - {selected_horizon['name']} - Entraînement Classification demandé : {model_name} sur {len(df)} données", "info")

    if isinstance(final_params, str):
        final_params = json.loads(final_params)

    if not used_features:
        logger.send_log(f"⚠️ - {symbol} - {selected_horizon['name']} - Aucun feature utilisé trouvé pour {model_name}.", "warning")
        return None

    if df.empty or "target" not in df.columns:
        logger.send_log(f"⚠️ - {symbol} - {selected_horizon['name']} - Données invalides, 'target' manquante ou dataset vide", "warning")
        return None

    y = df["target"]
    X = df.drop(columns=["target"])

    # Split
    X_train, X_temp, y_train, y_temp = train_test_split(
        X, y, test_size=0.2, stratify=y, random_state=final_params.get("random_state", 42)
    )
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, stratify=y_temp, random_state=final_params.get("random_state", 42)
    )

    xgb_model = xgb.XGBClassifier(
        **final_params,
        tree_method="hist",
        eval_metric="logloss",
        early_stopping_rounds=50,
        random_state=42,
        n_jobs=4,
        use_label_encoder=False
    )

    xgb_model.fit(
        X_train, y_train,
        eval_set=[(X_train, y_train), (X_val, y_val)],
        verbose=False
    )

    # Prédictions
    y_train_pred_proba = xgb_model.predict_proba(X_train)[:, 1]
    y_val_pred_proba = xgb_model.predict_proba(X_val)[:, 1]
    y_test_pred_proba = xgb_model.predict_proba(X_test)[:, 1]

    y_train_pred = (y_train_pred_proba >= 0.5).astype(int)
    y_val_pred = (y_val_pred_proba >= 0.5).astype(int)
    y_test_pred = (y_test_pred_proba >= 0.5).astype(int)

    try:
        evaluation_results = models.compute_combined_score_classification(
            y_val, y_val_pred, y_val_pred_proba,
            horizon_hour=selected_horizon["horizon_hour"]
        )
    except Exception as e:
        logger.send_log(f"❌ - {symbol} - {selected_horizon['name']} - Erreur dans compute_combined_score_classification : {e}", "error")
        return None

    model_path = os.path.join(MODEL_DIR, f"{model_name}.joblib")
    try:
        joblib.dump({
            "model": xgb_model,
            "used_features": used_features,
            "safe_shift_points": safe_shift_points
        }, model_path)
        logger.send_log(f"✅ - {symbol} - {selected_horizon['name']} - Modèle enregistré : {model_path}", "info")
    except Exception as e:
        logger.send_log(f"❌ - {symbol} - {selected_horizon['name']} - Échec de l'enregistrement du modèle : {e}", "error")
        return None

    try:
        mongo.update_model_evaluation(model_name, symbol, evaluation_results, used_features)
    except Exception as e:
        logger.send_log(f"❌ - {symbol} - {selected_horizon['name']} - Erreur lors de l'update des données d'évaluation : {e}", "error")
        return None

    # Send metrics to Grafana
    data = {
        "evaluation_results": evaluation_results,
        "uid": symbol,
        "horizon": selected_horizon['name'],
        "type": "classification"
    }
    logger.send_raw_data_log(data, metric="train_params")

    logger.send_log(
        f"✅ - {symbol} - {selected_horizon['name']} - Modèle {model_name} entraîné avec succès. Score : {evaluation_results['combined_score']:.3f}",
        "info"
    )
    return evaluation_results





def train_xgboost_regression(selected_horizon, symbol, df, final_params, used_features, safe_shift_points):
    model_name = selected_horizon["full_name"]
    logger.send_log(f"📌 - {symbol} - {selected_horizon['name']} - Entraînement demandé : {model_name} sur {len(df)} données", "info")

    if isinstance(final_params, str):
        final_params = json.loads(final_params)


    if not used_features:
        logger.send_log(f"⚠️ - {symbol} - {selected_horizon['name']} - Aucun feature utilisé trouvé pour {model_name}.", "warning")
        return None
    

    if df.empty:
        logger.send_log(f"⚠️ - {symbol} - {selected_horizon['name']} - Dataset vide ({model_name}), annulation.", "warning")
        return None
    
    if "log_return" not in df.columns:
        logger.send_log(f"⚠️ - {symbol} - {selected_horizon['name']} - Données invalides, 'log_return' manquant", "warning")
        return None

    y_raw = df["log_return"]
    X = df.drop(columns=["log_return"])

    # Standardisation de la target
    scaler = StandardScaler()
    y_scaled = scaler.fit_transform(y_raw.values.reshape(-1, 1)).flatten()

    xgb_model = xgb.XGBRegressor(
        **final_params,
        tree_method="hist",
        eval_metric="rmse",
        random_state=42,
        n_jobs=4
    )

    xgb_model.fit(
        X, y_scaled,
        verbose=False
    )
    '''
    # Split
    X_train, X_temp, y_train, y_temp = train_test_split(
        X, y_scaled, test_size=0.2, random_state=final_params.get("random_state", 42)
    )
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=final_params.get("random_state", 42)
    )

    xgb_model = xgb.XGBRegressor(
        **final_params,
        tree_method="hist",
        eval_metric="rmse",
        early_stopping_rounds=50,
        random_state=42,
        n_jobs=4
    )

    xgb_model.fit(
        X_train, y_train,
        eval_set=[(X_train, y_train), (X_val, y_val)],
        verbose=False
    )
    


    # 🔁 Reprojection des vraies valeurs et prédictions à l’échelle réelle
    y_train_true = scaler.inverse_transform(y_train.reshape(-1, 1)).flatten()
    y_val_true = scaler.inverse_transform(y_val.reshape(-1, 1)).flatten()
    y_test_true = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()

    y_train_pred = scaler.inverse_transform(xgb_model.predict(X_train).reshape(-1, 1)).flatten()
    y_val_pred = scaler.inverse_transform(xgb_model.predict(X_val).reshape(-1, 1)).flatten()
    y_test_pred = scaler.inverse_transform(xgb_model.predict(X_test).reshape(-1, 1)).flatten()

    try:
        evaluation_results = models.compute_combined_score(
            y_train_pred, y_train_true,
            y_val_pred, y_val_true
        )
    except Exception as e:
        logger.send_log(f"❌ - {symbol} - {selected_horizon['name']} - Erreur dans compute_combined_score : {e}", "error")
        return None
    '''


    model_path = os.path.join(MODEL_DIR, f"{model_name}.joblib")
    try:
        joblib.dump({
            "model": xgb_model,
            "used_features": used_features,
            "scaler": scaler,
            "safe_shift_points": safe_shift_points
        }, model_path)
        logger.send_log(f"✅ - {symbol} - {selected_horizon['name']} - Modèle enregistré : {model_path}", "info")
    except Exception as e:
        logger.send_log(f"❌ - {symbol} - {selected_horizon['name']} - Échec de l'enregistrement du modèle : {e}", "error")
        return None

    evaluation_results = {}
    try:
        mongo.update_model_evaluation(model_name, symbol, evaluation_results, used_features)
    except Exception as e:
        logger.send_log(f"❌ - {symbol} - {selected_horizon['name']} - Erreur lors de l'update des données d'évaluation : {e}", "error")
        return None
    
    '''
    # Send metrics to Grafana
    data = {}
    data["evaluation_results"] = evaluation_results
    data["uid"] = symbol
    data["horizon"] = selected_horizon['name']
    logger.send_raw_data_log(data, metric="train_params")'
    '''

    logger.send_log(f"✅ - {symbol} - {selected_horizon['name']} - Modèle {model_name} entraîné avec succès.", "info")
    return True



def train_classification_model(uid, horizon, candlesticks):
    """
    Entraîne un modèle XGBoost de classification binaire pour un horizon donné.
    
    :param uid: identifiant de la crypto
    :param horizon: dictionnaire contenant les paramètres de l'horizon (name, horizon_hour, shift_value)
    :param candlesticks: DataFrame de prix historiques
    :return: True si entraînement réussi, False sinon
    """
    horizon_hour = horizon['horizon_hour']
    shift_value = horizon['shift_value']
    try:
        model_name = f"xgboost_model_{uid}_{horizon['name']}_classification"
        horizon['full_name'] = model_name

        # 🔁 Paramètres des indicateurs
        indicator_params_doc = mongo.get_indicatorsparams(horizon["name"], uid, horizon_hour)
        if indicator_params_doc is None:
            logger.send_log(f"⚠️ - {uid} - Classification - {horizon['name']} - Aucun paramètre d'indicateur trouvé.", "warning")
            return False
        safe_shift_points = indicator_params_doc['safe_shift_points']

        # 🔁 Paramètres XGBoost
        best_previous_params = mongo.get_hyperparams(horizon, uid, "classification", horizon_hour)
        if not best_previous_params or not best_previous_params.get("best_params"):
            logger.send_log(f"⚠️ - {uid} - Classification - {horizon['name']} - Aucun paramètre optimisé trouvé.", "warning")
            return False

        depth = best_previous_params["data_depth"]
        final_params = best_previous_params.get("best_params")
        used_features = best_previous_params.get("used_features")

        # 🧠 Génération des features
        try:
            df = indicator.generate_training_sample(
                candlesticks.copy(), 
                indicator_params_doc["indicator_params"], 
                uid, 
                shift_value, 
                horizon_hour,
                depth,
                safe_shift_points
            )

            # Création de la target de classification
            SEUIL_POURCENT = horizon_hour * (0.002 / 3)
            seuil_log = np.log(1 + SEUIL_POURCENT)
            df["target"] = (df["log_return"] > seuil_log).astype(int)

            if df["target"].nunique() < 2:
                logger.send_log(f"⚠️ - {uid} - Classification - {horizon['name']} - Target constante : {df['target'].unique().tolist()}", "warning")
                return False

            # Suppression sécurisée des colonnes inutiles
            cols_to_drop = ["timestamp", "future_log_price", "highPrice", "lowPrice", "lastPrice", "past_base_price", "log_return"]
            df.drop(columns=[col for col in cols_to_drop if col in df.columns], inplace=True)

        except Exception as e:
            logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Erreur dans generate_training_sample : {e}", "error")
            return False

        # 🔬 Filtrage des features selon SHAP
        df_filtered = extract_features_dataframe(df, used_features, "classification")

        # ⚙️ Entraînement final
        try:
            train_xgboost_classification(horizon, uid, df_filtered, final_params, used_features, safe_shift_points)
        except Exception as e:
            logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Erreur entraînement modèle : {e}", "error")
            return False

        return True

    except Exception as e:
        logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Erreur inconnue dans train_classification_model : {e}", "error")
        return False
    



def train_regression_model(uid, horizon, candlesticks):
    """
    Entraîne un modèle XGBoost de régression pour un horizon donné.
    
    :param uid: identifiant de la crypto
    :param original_horizon: dictionnaire contenant les paramètres de l'horizon (name, horizon_hour, shift_value)
    :param candlesticks: DataFrame de prix historiques
    :return: True si entraînement réussi, False sinon
    """
    horizon_hour = horizon['horizon_hour']
    shift_value = horizon['shift_value']
    try:
        model_name = f"xgboost_model_{uid}_{horizon['name']}_regression"
        horizon['full_name'] = model_name

        # 🔁 Paramètres des indicateurs
        indicator_params_doc = mongo.get_indicatorsparams(horizon["name"], uid, horizon_hour)
        if indicator_params_doc is None:
            logger.send_log(f"⚠️ - {uid} - Regression - {horizon['name']} - Aucun paramètre d'indicateur trouvé.", "warning")
            return False
        safe_shift_points = indicator_params_doc['safe_shift_points']

        # 🔁 Paramètres XGBoost
        best_previous_params = mongo.get_hyperparams(horizon, uid, "regression", horizon_hour)
        if not best_previous_params or not best_previous_params.get("best_params"):
            logger.send_log(f"⚠️ - {uid} - Regression - {horizon['name']} - Aucun paramètre optimisé trouvé.", "warning")
            return False

        depth = best_previous_params["data_depth"]
        final_params = best_previous_params.get("best_params")
        used_features = best_previous_params.get("used_features")

        # 🧠 Génération des features
        try:
            df = indicator.generate_training_sample(
                candlesticks.copy(), 
                indicator_params_doc["indicator_params"], 
                uid, 
                shift_value, 
                horizon_hour,
                depth,
                safe_shift_points
            )

            # Suppression sécurisée des colonnes inutiles
            cols_to_drop = ["timestamp", "future_log_price", "highPrice", "lowPrice", "lastPrice", "past_base_price"]
            df.drop(columns=[col for col in cols_to_drop if col in df.columns], inplace=True)

        except Exception as e:
            logger.send_log(f"❌ - {uid} - Regression - {horizon['name']} - Erreur dans generate_training_sample : {e}", "error")
            return False

        # 🔬 Filtrage des features selon SHAP
        df_filtered = extract_features_dataframe(df, used_features, "regression")

        # ⚙️ Entraînement final
        try:
            train_xgboost_regression(horizon, uid, df_filtered, final_params, used_features, safe_shift_points)
        except Exception as e:
            logger.send_log(f"❌ - {uid} - Regression - {horizon['name']} - Erreur entraînement modèle : {e}", "error")
            return False

        return True

    except Exception as e:
        logger.send_log(f"❌ - {uid} - Regression - {horizon['name']} - Erreur inconnue dans train_regression_model : {e}", "error")
        return False


    

def loop_training():
    while True:
        trading_uids = mongo.get_trading_pairs()

        if not trading_uids:
            logger.send_log("⚠️ Aucune crypto à entraîner (test ou trading) !", "warning")
            time.sleep(300)  # Attente avant une nouvelle tentative
            continue

        logger.send_log("🚀 Lancement des apprentissages en mode séquentiel", "info")

        for uid in trading_uids:
            until_date = datetime.utcnow()
            candlesticks = mongo.get_candlesticks(uid, until_date)

            if len(candlesticks) == 0:
                logger.send_log(f"⚠️ - {uid} - Aucun candlesticks trouvés", "warning")
                continue

            for original_horizon in HORIZONS:
                horizon = copy.deepcopy(original_horizon)
                horizon_hour = original_horizon["horizon_hour"]
                shift_value = original_horizon["shift_value"]

                # 🔁 Paramètres des indicateurs
                indicator_params = mongo.get_indicatorsparams(horizon["name"], uid, horizon_hour)
                if indicator_params is None:
                    #logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Aucun paramètre d'indicateur trouvé.", "warning")
                    continue

                # Entraine un modèle en regression
                success = train_regression_model(uid, horizon, candlesticks)
                if not success:
                    continue

                # Entraine un modèle en classification
                success = train_classification_model(uid, horizon, candlesticks)
                if not success:
                    continue


        logger.send_log("✅ Tous les apprentissages sont terminés !", "info")
        time.sleep(20)  # Pause entre chaque boucle


if __name__ == "__main__":
    # 🔄 Démarrer l'entraînement dans un thread séparé
    training_thread = threading.Thread(target=loop_training, daemon=True)
    training_thread.start()

    # 🚀 Lancer l'API Flask
    app.run(port=5002, host="0.0.0.0")



