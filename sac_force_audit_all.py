#!/usr/bin/env python3
# sac_force_audit_all.py - Force l'audit de tous les actifs disponibles

import os
import sys
import time
import pathlib
import argparse
from datetime import datetime, timezone
import traceback
import re

# Chemin local
ROOT = pathlib.Path(__file__).resolve().parent
for p in (ROOT, ROOT.parent):
    sys.path.insert(0, str(p))

from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from audit_forecasts import AuditForecasts

# Logger
logger = GrafanaUtils(service="sac_force_audit")

# Connexion MongoDB
mongo = MongoUtils(logger=logger)
mongo.connect(service="sac_force_audit")

def parse_args():
    """Parse les arguments de ligne de commande."""
    parser = argparse.ArgumentParser(description="Force l'audit de tous les actifs")
    parser.add_argument("--asset", type=str, help="Asset spécifique à auditer")
    parser.add_argument("--list", action="store_true", help="Liste tous les actifs disponibles")
    return parser.parse_args()

def get_all_available_assets():
    """Récupère tous les actifs disponibles dans le système."""
    assets = set()
    
    # 1. Depuis forecast_features
    try:
        assets.update(mongo.db["forecast_features"].distinct("uid"))
    except Exception as e:
        logger.send_log("forecast_features_error", "warning", extra_labels={"error": str(e)})
    
    # 2. Depuis candlesticks
    try:
        assets.update(mongo.db["candlesticks"].distinct("uid"))
    except Exception as e:
        logger.send_log("candlesticks_error", "warning", extra_labels={"error": str(e)})
    
    # 3. Depuis market_pairs
    try:
        assets.update(mongo.db["market_pairs"].distinct("symbol"))
    except Exception as e:
        logger.send_log("market_pairs_error", "warning", extra_labels={"error": str(e)})
    
    # 4. Depuis les modèles existants
    model_dir = pathlib.Path("/home/<USER>/cryptobot/models")
    if model_dir.exists():
        for model_path in model_dir.glob("sac_*.zip"):
            parts = model_path.stem.split("_")
            if len(parts) >= 2:
                assets.add(parts[1])
    
    # 5. Ajouter la liste par défaut
    assets.update(CRYPTO_TEST)
    
    # Filtrer les UIDs invalides
    valid_assets = [asset for asset in assets if re.match(r"^[A-Z]{2,6}(EUR|USD|USDT)$", asset)]
    
    return sorted(valid_assets)

def force_audit_asset(asset):
    """Force l'audit d'un asset spécifique."""
    try:
        logger.send_log("forcing_audit", "info", extra_labels={"asset": asset})
        
        # Vérifier si l'asset a des données
        candle_count = mongo.db["candlesticks"].count_documents({"uid": asset})
        if candle_count == 0:
            logger.send_log("no_candles", "warning", extra_labels={"asset": asset})
            return False
        
        # Vérifier si l'asset a des features
        feature_count = mongo.db["forecast_features"].count_documents({"uid": asset})
        if feature_count == 0:
            logger.send_log("no_features", "warning", extra_labels={"asset": asset})
            
            # Tenter de générer les features
            try:
                import subprocess
                logger.send_log("generating_features", "info", extra_labels={"asset": asset})
                result = subprocess.run(
                    ["python", "-m", "sac_export_features", "--asset", asset, "--force"],
                    capture_output=True,
                    text=True,
                    timeout=600  # 10 minutes max
                )
                if result.returncode != 0:
                    logger.send_log("feature_generation_failed", "error", extra_labels={
                        "asset": asset,
                        "returncode": result.returncode,
                        "stderr": result.stderr[:200]
                    })
                    return False
            except Exception as e:
                logger.send_log("feature_generation_error", "error", extra_labels={
                    "asset": asset,
                    "error": str(e)
                })
                return False
        
        # Exécuter l'audit
        audit = AuditForecasts(asset, mongo)
        audit.run()
        
        # Vérifier si l'audit a été créé
        latest_audit = mongo.db["forecast_audit"].find_one(
            {"uid": asset},
            sort=[("created_at", -1)]
        )
        
        if latest_audit and latest_audit["created_at"] > datetime.now(timezone.utc).replace(minute=0, second=0, microsecond=0):
            logger.send_log("audit_successful", "info", extra_labels={
                "asset": asset,
                "horizons": len(latest_audit.get("rows", []))
            })
            return True
        else:
            logger.send_log("audit_verification_failed", "warning", extra_labels={"asset": asset})
            return False
    except Exception as e:
        logger.send_log("audit_error", "error", extra_labels={
            "asset": asset,
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        return False

def main():
    args = parse_args()
    
    # Liste tous les actifs disponibles
    if args.list:
        assets = get_all_available_assets()
        print(f"Actifs disponibles ({len(assets)}):")
        for asset in assets:
            print(f"- {asset}")
        return
    
    # Audit d'un asset spécifique
    if args.asset:
        success = force_audit_asset(args.asset)
        if success:
            print(f"✅ Audit réussi pour {args.asset}")
        else:
            print(f"❌ Échec de l'audit pour {args.asset}")
        return
    
    # Audit de tous les actifs
    assets = get_all_available_assets()
    logger.send_log("starting_audit_all", "info", extra_labels={"count": len(assets)})
    
    results = {
        "success": [],
        "failed": []
    }
    
    for asset in assets:
        try:
            success = force_audit_asset(asset)
            if success:
                results["success"].append(asset)
            else:
                results["failed"].append(asset)
        except Exception as e:
            logger.send_log("asset_processing_error", "error", extra_labels={
                "asset": asset,
                "error": str(e)
            })
            results["failed"].append(asset)
    
    # Résumé
    logger.send_log("audit_all_complete", "info", extra_labels={
        "success_count": len(results["success"]),
        "failed_count": len(results["failed"]),
        "success": results["success"],
        "failed": results["failed"]
    })
    
    print(f"✅ Audit réussi pour {len(results['success'])} actifs")
    print(f"❌ Échec pour {len(results['failed'])} actifs")
    
    if results["failed"]:
        print("\nÉchecs:")
        for asset in results["failed"]:
            print(f"- {asset}")

if __name__ == "__main__":
    main()
