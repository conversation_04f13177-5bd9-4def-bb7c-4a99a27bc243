#!/usr/bin/env python3
# sac_generate_signals.py – Génère des signaux de trading basés sur des modèles SAC
import sys
import argparse
import json
import time
import pathlib
import datetime as dt
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import gc
from functools import lru_cache

from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# Configuration
MODEL_DIR = pathlib.Path("/home/<USER>/cryptobot/models")
PARQUET_DIR = pathlib.Path("data/features")
FEATURE_DIR = PARQUET_DIR  # Alias pour compatibilité
SLEEP_SECONDS = 300  # 5 minutes entre chaque cycle
MIN_ROWS = 50  # Nombre minimum de lignes pour générer un signal
LOOKBACK_MAX = 1000  # Nombre maximum de lignes à utiliser
FALLBACK_THR = 0.2  # Seuil par défaut pour les signaux
SIG_COLLECTION = "signals"  # Collection MongoDB pour les signaux
SIG_COLL = "signals"  # Alias pour compatibilité avec le code existant
TRADE_SIGNALS_COLLECTION = "trade_signals"  # Collection pour les signaux de trading
MAX_SIGNAL_AGE_HOURS = 6  # Âge maximum d'un signal avant d'en générer un nouveau

# Créer les répertoires s'ils n'existent pas
MODEL_DIR.mkdir(parents=True, exist_ok=True)
PARQUET_DIR.mkdir(parents=True, exist_ok=True)

# Logger
logger = GrafanaUtils(service="signal_gen")

def parse_args():
    """Parse les arguments de ligne de commande."""
    parser = argparse.ArgumentParser(description="Génère des signaux de trading basés sur des modèles SAC")
    parser.add_argument("--asset", type=str, help="Asset spécifique pour lequel générer un signal")
    parser.add_argument("--force", action="store_true", help="Force la génération même si les conditions ne sont pas idéales")
    return parser.parse_args()

def _latest_model(uid):
    """Trouve le modèle le plus récent pour un UID donné."""
    try:
        # Chercher d'abord les modèles "best"
        best_models = sorted(MODEL_DIR.glob(f"sac_{uid}_*_best.zip"), reverse=True)
        
        if best_models:
            logger.send_log("best_model_found", "debug", extra_labels={
                "uid": uid, "model": best_models[0].name
            })
            return best_models[0]
        
        # Si pas de meilleur modèle, chercher les modèles réguliers
        models = sorted(MODEL_DIR.glob(f"sac_{uid}_*.zip"), reverse=True)
        
        if not models:
            # Vérifier si des modèles existent pour d'autres UIDs
            all_models = list(MODEL_DIR.glob("sac_*.zip"))
            if all_models:
                other_uids = set()
                for model in all_models:
                    parts = model.stem.split("_")
                    if len(parts) >= 2:
                        other_uids.add(parts[1])  # Le format est sac_PAIRID_date.zip
                
                logger.send_log("no_models", "warning", extra_labels={
                    "uid": uid,
                    "available_uids": list(other_uids)[:5],  # Limiter à 5 pour éviter des logs trop grands
                    "total_models": len(all_models)
                })
            else:
                logger.send_log("no_models_at_all", "warning", extra_labels={
                    "uid": uid,
                    "model_dir": str(MODEL_DIR)
                })
            return None
        
        # Filtrer les modèles valides (taille > 1 Ko)
        valid_models = []
        for model in models:
            try:
                if model.stat().st_size < 1000:  # Moins de 1 Ko
                    logger.send_log("model_too_small", "warning", extra_labels={
                        "uid": uid, "model": model.name, "size_bytes": model.stat().st_size
                    })
                    continue
                    
                # Vérifier que le JSON associé existe
                json_path = model.with_suffix(".json")
                if not json_path.exists():
                    # Créer un JSON minimal si nécessaire
                    try:
                        json_data = {
                            "uid": uid,
                            "created_at": dt.datetime.utcnow().isoformat(),
                            "model_path": str(model),
                            "auto_generated": True,
                            "features": ["open", "high", "low", "close", "volume", 
                                        "open_z", "high_z", "low_z", "close_z", "volume_z"]
                        }
                        with open(json_path, 'w') as f:
                            json.dump(json_data, f, indent=2)
                        logger.send_log("json_auto_generated", "info", extra_labels={
                            "uid": uid, "model": model.name, "json_path": str(json_path)
                        })
                    except Exception as e:
                        logger.send_log("json_generation_failed", "error", extra_labels={
                            "uid": uid, "model": model.name, "error": str(e)
                        })
                        continue
                
                valid_models.append(model)
                
            except Exception as e:
                logger.send_log("model_validation_error", "error", extra_labels={
                    "uid": uid, "model": model.name, "error": str(e)
                })
        
        if not valid_models:
            logger.send_log("no_valid_models", "warning", extra_labels={
                "uid": uid, "total_models": len(models)
            })
            return None
            
        # Retourner le modèle valide le plus récent
        model = valid_models[0]
        logger.send_log("model_found", "debug", extra_labels={
            "uid": uid, "model": model.name, "size_kb": round(model.stat().st_size / 1024, 2)
        })
        return model
        
    except Exception as e:
        logger.send_log("latest_model_error", "error", extra_labels={
            "uid": uid, "error": str(e)
        })
        return None


def _dynamic_threshold(mongo, uid):
    """Calcule un seuil dynamique pour les signaux de trading."""
    try:
        # Récupérer les signaux précédents pour cet UID
        signals = list(mongo.db[SIG_COLLECTION].find(
            {"uid": uid},
            {"allocation": 1, "created_at": 1, "price": 1}
        ).sort("created_at", -1).limit(100))
        
        if not signals:
            logger.send_log("no_previous_signals", "info", extra_labels={"uid": uid})
            return FALLBACK_THR
            
        # Calculer la volatilité des allocations
        allocations = [s.get("allocation", 0) for s in signals if "allocation" in s]
        if not allocations or len(allocations) < 5:
            logger.send_log("insufficient_allocation_history", "info", extra_labels={
                "uid": uid, 
                "count": len(allocations)
            })
            return FALLBACK_THR
            
        import numpy as np
        alloc_std = np.std(allocations)
        
        # Ajout : check de la volatilité prix sur le même historique
        prices = [s.get("price", 0) for s in signals if "price" in s and s["price"] is not None]
        price_std = np.std(prices) if prices else 0
        price_mean = np.mean(prices) if prices else 1
        price_vol = price_std / price_mean if price_mean != 0 else 0
        
        # Si grosse volatilité, monte le seuil pour éviter overtrading
        dynamic_thr = min(max(alloc_std * 0.5, 0.1), 0.4)
        if price_vol > 0.04:  # 4% de std sur 100 points, à ajuster selon ton marché
            dynamic_thr = max(dynamic_thr, 0.3)
            logger.send_log("volatility_threshold_increased", "info", extra_labels={
                "uid": uid, 
                "price_vol": round(price_vol, 4), 
                "dynamic_thr": round(dynamic_thr, 4)
            })
        
        logger.send_log("dynamic_threshold_calculated", "debug", extra_labels={
            "uid": uid, 
            "threshold": round(dynamic_thr, 4),
            "allocation_std": round(alloc_std, 4),
            "allocation_count": len(allocations),
            "price_volatility": round(price_vol, 4) if prices else 0
        })
        
        return dynamic_thr
        
    except Exception as e:
        logger.send_log("dynamic_threshold_error", "warning", extra_labels={
            "uid": uid, 
            "error": str(e)
        })
        return FALLBACK_THR


def _alloc_to_signal(alloc: float = None, *, allocation: float = None, threshold: float = 0.2, uid: str = None) -> str:
    """Convertit l'allocation en signal de trading."""
    # ── Compatibilité ascendante ────────────────────────────────────────────────
    # Certaines parties du code ancien appellent la fonction avec le mot‑clef
    # `allocation=` au lieu de `alloc=`.  Pour éviter des TypeError et rendre le
    # système plus robuste, on accepte l'un ou l'autre.
    if alloc is None and allocation is not None:
        alloc = allocation
    elif alloc is None and allocation is None:
        logger.send_log("alloc_missing_in_alloc_to_signal", "error", extra_labels={
            "uid": uid or "unknown",
            "threshold": str(threshold)
        })
        return "HOLD"
    # Vérifier que l'UID est fourni
    if uid is None:
        logger.send_log("missing_uid_warning", "warning", extra_labels={
            "function": "_alloc_to_signal",
            "alloc": str(alloc),
            "threshold": str(threshold)
        })
        uid = "missing_uid"  # Valeur explicite pour le filtrage
    
    # S'assurer que les valeurs sont des nombres
    try:
        alloc = float(alloc)
        threshold = float(threshold)
    except (ValueError, TypeError) as e:
        logger.send_log("alloc_to_signal", "error", extra_labels={
            "alloc": str(alloc), 
            "threshold": str(threshold),
            "uid": uid,  # Utiliser la valeur vérifiée
            "error": str(e)
        })
        return "HOLD"  # Valeur par défaut en cas d'erreur
    
    signal = "HOLD"
    if alloc > threshold:
        signal = "BUY"
    elif alloc < -threshold:
        signal = "SELL"
    
    # Utiliser le même nom de log que celui recherché dans Grafana
    logger.send_log("alloc_to_signal", "debug", extra_labels={
        "alloc": round(alloc, 4), 
        "threshold": round(threshold, 4), 
        "signal": signal,
        "uid": uid  # Utiliser la valeur vérifiée
    })
    
    return signal


def _load_df(uid):
    """Charge les données pour un UID spécifique."""
    try:
        # Chemin du fichier parquet
        parquet_path = PARQUET_DIR.joinpath(f"features-{uid}.parquet")
        
        if not parquet_path.exists():
            logger.send_log("parquet_file_missing", "warning", extra_labels={
                "uid": uid, "path": str(parquet_path)
            })
            return pd.DataFrame()
            
        # Charger le fichier parquet
        df = pd.read_parquet(parquet_path)
        
        if df.empty:
            logger.send_log("empty_parquet", "warning", extra_labels={"uid": uid})
            return df
            
        # Vérifier le nombre de lignes
        if len(df) < MIN_ROWS:
            logger.send_log("insufficient_rows", "warning", extra_labels={
                "uid": uid, "rows": len(df), "min_required": MIN_ROWS
            })
            return pd.DataFrame()
            
        # Limiter le nombre de lignes pour éviter les problèmes de mémoire
        if len(df) > LOOKBACK_MAX:
            df = df.iloc[-LOOKBACK_MAX:]
            logger.send_log("data_truncated", "debug", extra_labels={
                "uid": uid, "original_rows": len(df), "kept_rows": LOOKBACK_MAX
            })
            
        return df
        
    except Exception as e:
        import traceback
        logger.send_log("load_df_error", "error", extra_labels={
            "uid": uid, "error": str(e), "traceback": traceback.format_exc()[:500]
        })
        return pd.DataFrame()


def _validate_model(model_path, df):
    """Vérifie que le modèle est compatible avec les données."""
    # Implémentation simplifiée pour éviter les erreurs
    return True


# Cache pour les modèles (limité à 10 modèles en mémoire)
@lru_cache(maxsize=10)
def _cached_load_model(model_path_str, uid):
    """Charge un modèle SAC avec mise en cache."""
    model_path = pathlib.Path(model_path_str)
    try:
        from stable_baselines3 import SAC
        logger.send_log("stable_baselines3_imported", "debug", extra_labels={"uid": uid})
        
        logger.send_log("model_load_attempt", "debug", extra_labels={
            "uid": uid, "path": str(model_path)
        })
        
        model = SAC.load(str(model_path))
        
        logger.send_log("model_loaded_successfully", "info", extra_labels={
            "uid": uid, "path": str(model_path)
        })
        
        return model
    except Exception as e:
        logger.send_log("model_loading_error", "error", extra_labels={
            "uid": uid, "error": str(e), "path": str(model_path)
        })
        return None

def _load_model(model_path, uid):
    """Wrapper pour charger un modèle avec mise en cache."""
    return _cached_load_model(str(model_path), uid)

def _clear_model_cache():
    """Vide le cache des modèles."""
    try:
        # Si nous utilisons lru_cache pour _cached_load_model
        if hasattr(_cached_load_model, "cache_clear"):
            _cached_load_model.cache_clear()
            logger.send_log("model_cache_cleared", "debug")
        else:
            logger.send_log("no_model_cache_to_clear", "debug")
    except Exception as e:
        logger.send_log("cache_clear_error", "warning", extra_labels={"error": str(e)})

def _prepare_data_for_prediction(df, uid):
    """Prépare les données pour la prédiction."""
    try:
        # Vérifier si le DataFrame est valide
        if df.empty:
            logger.send_log("empty_dataframe", "warning", extra_labels={"uid": uid})
            return df
            
        # Journaliser les colonnes disponibles
        logger.send_log("available_columns", "debug", extra_labels={
            "uid": uid,
            "columns": list(df.columns)
        })
        
        # Convertir les colonnes datetime en timestamp numérique
        datetime_cols = ["time", "closeTime", "created_at", "updated_at", "date", "timestamp", "run_at"]
        for col in datetime_cols:
            if col in df.columns and not pd.api.types.is_numeric_dtype(df[col]):
                try:
                    # Convertir en timestamp (secondes depuis l'epoch)
                    df[f"{col}_ts"] = pd.to_datetime(df[col]).astype(int) / 10**9
                    # Conserver la colonne originale pour référence
                    logger.send_log("datetime_converted", "debug", extra_labels={
                        "uid": uid, "column": col
                    })
                except Exception as e:
                    logger.send_log("datetime_conversion_failed", "warning", extra_labels={
                        "uid": uid, "column": col, "error": str(e)
                    })
        
        # Ajouter les colonnes requises si elles n'existent pas
        required_columns = ["horizon_h", "expected_ret"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.send_log("adding_required_columns", "info", extra_labels={
                "uid": uid, "missing": missing_columns
            })
            for col in missing_columns:
                df[col] = 0.0  # Valeur par défaut

        # -- Fallback : créer la colonne 'close' si absente -------------------
        if "close" not in df.columns:
            for alt_col in ["lastPrice", "last_price", "last", "close_price"]:
                if alt_col in df.columns:
                    df["close"] = df[alt_col]
                    logger.send_log("close_column_created", "info", extra_labels={
                        "uid": uid,
                        "source": alt_col
                    })
                    break
            # Dernier recours : récupérer le prix courant via Mongo
            if "close" not in df.columns:
                try:
                    mongo_price = MongoUtils()
                    mongo_price.connect(service="signal_gen")
                    current_price = _get_current_price(mongo_price, uid)
                    if current_price is not None:
                        df["close"] = current_price
                        logger.send_log("close_column_filled_from_mongo", "info", extra_labels={
                            "uid": uid,
                            "price": current_price
                        })
                except Exception as e:
                    logger.send_log("close_fill_mongo_error", "warning", extra_labels={
                        "uid": uid,
                        "error": str(e)
                    })
        
        # Vérifier les valeurs NaN
        nan_count = df.isna().sum().sum()
        if nan_count > 0:
            logger.send_log("nan_values_found", "warning", extra_labels={
                "uid": uid,
                "nan_count": int(nan_count)
            })
            df = df.fillna(0)
        
        # Journaliser les informations sur le DataFrame
        logger.send_log("df_loaded", "debug", extra_labels={
            "uid": uid, "rows": len(df), "columns": len(df.columns)
        })
        
        return df
        
    except Exception as e:
        import traceback
        logger.send_log("load_df_error", "error", extra_labels={
            "uid": uid, "error": str(e), "traceback": traceback.format_exc()[:500]
        })
        return df


def _predict(model, df, uid):
    """Prédit un signal de trading en utilisant un modèle SAC."""
    start_time = time.time()
    try:
        # Ajouter des logs détaillés pour le débogage
        logger.send_log("predict_function_called", "debug", extra_labels={
            "uid": uid,
            "model_type": type(model).__name__,
            "df_shape": f"{df.shape[0]} rows, {df.shape[1]} columns" if not df.empty else "empty"
        })
        
        # Vérifier si le modèle est valide
        if model is None:
            logger.send_log("model_not_found", "error", extra_labels={"uid": uid})
            return None
            
        # Vérifier si le DataFrame est valide
        if df.empty:
            logger.send_log("empty_dataframe", "error", extra_labels={"uid": uid})
            return None
            
        # Préparer les données pour la prédiction
        df = _prepare_data_for_prediction(df, uid)
        if df.empty:
            logger.send_log("empty_dataframe_after_preparation", "error", extra_labels={"uid": uid})
            return None
        
        # Créer un environnement pour la prédiction
        try:
            from stable_baselines3 import SAC
            import gym
            from gym import spaces
            import numpy as np
            import traceback
            
            # Identifier les colonnes numériques uniquement
            numeric_columns = []
            datetime_columns = []
            for col in df.columns:
                if col not in ["uid"]:  # Exclure les colonnes non-numériques connues
                    try:
                        # Vérifier si la colonne peut être convertie en float
                        test_val = df[col].iloc[-1]
                        if isinstance(test_val, (pd.Timestamp, dt.datetime, dt.date)):
                            datetime_columns.append(col)
                        else:
                            float(test_val)  # Essayer de convertir en float
                            numeric_columns.append(col)
                    except (ValueError, TypeError):
                        datetime_columns.append(col)

            # Log pour le débogage
            logger.send_log("columns_categorized", "debug", extra_labels={
                "uid": uid,
                "numeric_columns": len(numeric_columns),
                "datetime_columns": len(datetime_columns),
                "total_columns": len(df.columns),
                "numeric_cols_sample": str(numeric_columns[:5]) if numeric_columns else "none"
            })
            
            # Vérifier la forme du modèle
            try:
                model_obs_shape = model.observation_space.shape[0]
                logger.send_log("model_shape_check", "debug", extra_labels={
                    "uid": uid,
                    "model_obs_shape": model_obs_shape
                })
                # ── Harmoniser numeric_columns avec model_obs_shape ───────────
                if len(numeric_columns) > model_obs_shape:
                    removed = len(numeric_columns) - model_obs_shape
                    numeric_columns = numeric_columns[-model_obs_shape:]
                    logger.send_log("numeric_columns_truncated", "info", extra_labels={
                        "uid": uid,
                        "removed_cols": removed,
                        "kept_cols": len(numeric_columns)
                    })
                elif len(numeric_columns) < model_obs_shape:
                    padding_needed = model_obs_shape - len(numeric_columns)
                    logger.send_log("numeric_columns_padded", "debug", extra_labels={
                        "uid": uid,
                        "current_cols": len(numeric_columns),
                        "padding_needed": padding_needed
                    })

                # Créer maintenant le DataFrame numérique définitif
                numeric_df = df[numeric_columns].copy()
            except Exception as e:
                logger.send_log("model_shape_error", "error", extra_labels={
                    "uid": uid,
                    "error": str(e),
                    "error_type": type(e).__name__
                })
                return None

            # Vérifier que le DataFrame numérique n'est pas vide
            if numeric_df.empty:
                logger.send_log("empty_numeric_dataframe", "error", extra_labels={"uid": uid})
                return None
            
            # Définir une classe d'environnement compatible
            class CompatEnv:
                def __init__(self, df, expected_shape):
                    self.df = df
                    self.expected_shape = expected_shape
                    self.observation_space = spaces.Box(
                        low=-np.inf, high=np.inf, shape=(expected_shape,), dtype=np.float32
                    )
                    self.action_space = spaces.Box(
                        low=-1.0, high=1.0, shape=(1,), dtype=np.float32
                    )
                    
                def reset(self):
                    # Créer une observation à partir des dernières lignes du DataFrame
                    try:
                        # Convertir le DataFrame en tableau numpy
                        raw_obs = self.df.iloc[-1].values.astype(np.float32)
                        
                        # Adapter la taille de l'observation à celle attendue par le modèle
                        if len(raw_obs) < self.expected_shape:
                            # Padding
                            padding_size = self.expected_shape - len(raw_obs)
                            logger.send_log("padding_observation", "info", extra_labels={
                                "uid": uid, 
                                "original_size": len(raw_obs),
                                "padding_size": padding_size,
                                "final_size": self.expected_shape
                            })
                            obs = np.pad(raw_obs, (0, padding_size), 'constant')
                        elif len(raw_obs) > self.expected_shape:
                            # Troncature
                            truncate_size = len(raw_obs) - self.expected_shape
                            logger.send_log("truncating_observation", "info", extra_labels={
                                "uid": uid, 
                                "original_size": len(raw_obs),
                                "truncate_size": truncate_size,
                                "final_size": self.expected_shape
                            })
                            obs = raw_obs[:self.expected_shape]
                        else:
                            obs = raw_obs
                            
                        return obs
                    except Exception as e:
                        logger.send_log("reset_error", "error", extra_labels={
                            "uid": uid,
                            "error": str(e),
                            "error_type": type(e).__name__,
                            "traceback": traceback.format_exc()[:500]
                        })
                        # Retourner un tableau de zéros en cas d'erreur
                        return np.zeros(self.expected_shape, dtype=np.float32)
                        
                def step(self, action):
                    return self.reset(), 0, True, {}
                    
                def close(self):
                    pass
            
            # Créer l'environnement compatible
            try:
                env = CompatEnv(numeric_df, model_obs_shape)
                logger.send_log("env_created", "debug", extra_labels={
                    "uid": uid, 
                    "shape": model_obs_shape
                })
            except Exception as e:
                logger.send_log("env_creation_error", "error", extra_labels={
                    "uid": uid, 
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "traceback": traceback.format_exc()[:500]
                })
                return None
        
            # Faire la prédiction
            try:
                # Obtenir l'observation
                obs = env.reset()
                
                # Vérifier que l'observation a la bonne forme
                if len(obs) != model_obs_shape:
                    logger.send_log("obs_shape_mismatch", "error", extra_labels={
                        "uid": uid, 
                        "expected": model_obs_shape, 
                        "actual": len(obs)
                    })
                    return None
                
                # Vérifier les valeurs NaN
                if np.isnan(obs).any():
                    logger.send_log("nan_in_observation", "warning", extra_labels={
                        "uid": uid, 
                        "nan_count": np.isnan(obs).sum()
                    })
                    # Remplacer les NaN par des zéros
                    obs = np.nan_to_num(obs)
                
                # Vérifier les valeurs infinies
                if np.isinf(obs).any():
                    logger.send_log("inf_in_observation", "warning", extra_labels={
                        "uid": uid, 
                        "inf_count": np.isinf(obs).sum()
                    })
                    # Remplacer les infinis par des valeurs grandes mais finies
                    obs = np.where(np.isinf(obs), np.sign(obs) * 1e6, obs)
                
                # Log détaillé de l'observation
                logger.send_log("observation_details", "debug", extra_labels={
                    "uid": uid, 
                    "obs_shape": obs.shape,
                    "obs_min": float(np.min(obs)),
                    "obs_max": float(np.max(obs)),
                    "obs_mean": float(np.mean(obs)),
                    "obs_std": float(np.std(obs)),
                    "has_nan": np.isnan(obs).any(),
                    "has_inf": np.isinf(obs).any()
                })
                
                # Utiliser try/except pour la prédiction elle-même
                try:
                    # Log avant la prédiction
                    logger.send_log("predict_call_start", "debug", extra_labels={
                        "uid": uid, 
                        "model_type": type(model).__name__
                    })
                    
                    # Utiliser deterministic=True pour des prédictions stables
                    action, _ = model.predict(obs, deterministic=True)
                    
                    # Log après la prédiction
                    logger.send_log("predict_call_success", "debug", extra_labels={
                        "uid": uid, 
                        "action_shape": action.shape,
                        "action_value": float(action[0])
                    })
                    
                    # Convertir l'action en allocation
                    allocation = float(action[0])
                    
                    # Récupérer le prix actuel
                    price = df["close"].iloc[-1] if "close" in df.columns else None
                    if price is None:
                        logger.send_log("close_price_missing", "warning", extra_labels={
                            "uid": uid, 
                            "columns": list(df.columns)[:10]
                        })
                        # Essayer d'autres noms de colonnes possibles
                        for col_name in ["lastPrice", "last_price", "last"]:
                            if col_name in df.columns:
                                price = df[col_name].iloc[-1]
                                logger.send_log("alternative_price_found", "info", extra_labels={
                                    "uid": uid, 
                                    "column": col_name,
                                    "price": price
                                })
                                break
                        # Dernier recours : interroger Mongo pour le prix courant
                        if price is None:
                            try:
                                mongo_price = MongoUtils()
                                mongo_price.connect(service="signal_gen")
                                price = _get_current_price(mongo_price, uid)
                                if price is not None:
                                    logger.send_log("fetched_price_from_mongo", "info", extra_labels={
                                        "uid": uid,
                                        "price": price
                                    })
                            except Exception as e:
                                logger.send_log("mongo_price_fetch_error", "warning", extra_labels={
                                    "uid": uid,
                                    "error": str(e)
                                })
                    # Si le prix est toujours invalide (None ou NaN), abandonner la valeur
                    if price is None or (isinstance(price, float) and (np.isnan(price) or np.isinf(price))):
                        logger.send_log("price_still_missing", "warning", extra_labels={
                            "uid": uid
                        })
                        price = None
                    
                    # Déterminer le signal en utilisant un seuil dynamique
                    threshold = FALLBACK_THR  # Seuil par défaut
                    try:
                        mongo_local = MongoUtils()
                        mongo_local.connect(service="signal_gen")
                        threshold = _dynamic_threshold(mongo_local, uid)  # Utiliser la fonction existante
                    except Exception as e:
                        logger.send_log("threshold_calculation_error", "warning", extra_labels={
                            "uid": uid, 
                            "error": str(e),
                            "using_fallback": FALLBACK_THR
                        })
                        # Utiliser le seuil par défaut en cas d'erreur

                    if uid is None:
                        logger.send_log("uid_missing_in_predict", "warning")
                        uid = "missing_uid"

                    # Log de débogage avant l'appel
                    logger.send_log("alloc_to_signal_debug", "debug", extra_labels={
                        "uid": uid,
                        "allocation": float(allocation),
                        "threshold": float(threshold)
                    })

                    # Appel explicite avec uid nommé
                    signal = _alloc_to_signal(alloc=allocation, threshold=threshold, uid=uid)
                    
                    # Calculer le temps d'exécution
                    execution_time = time.time() - start_time
                    
                    logger.send_log("prediction_success", "info", extra_labels={
                        "uid": uid, 
                        "allocation": round(allocation, 4),
                        "threshold": round(threshold, 4),
                        "signal": signal,
                        "price": price,
                        "execution_time_ms": round(execution_time * 1000, 2)
                    })
                    
                    # Nettoyer les ressources
                    try:
                        env.close()
                    except:
                        pass
                    
                    return signal, allocation, price
                    
                except Exception as e:
                    logger.send_log("model_predict_call_error", "error", extra_labels={
                        "uid": uid, 
                        "error": str(e),
                        "error_type": type(e).__name__,
                        "traceback": traceback.format_exc()[:500]
                    })
                    return None
                    
            except Exception as e:
                logger.send_log("observation_processing_error", "error", extra_labels={
                    "uid": uid, 
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "traceback": traceback.format_exc()[:500]
                })
                return None
        except Exception as e:
            logger.send_log("prediction_environment_error", "error", extra_labels={
                "uid": uid, 
                "error": str(e),
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc()[:500]
            })
            return None
    except Exception as e:
        import traceback
        logger.send_log("prediction_failed", "error", extra_labels={
            "uid": uid, 
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc()[:500]
        })
        return None


def _latent_pnl(uid, price, mongo):
    """Calcule le PnL latent pour un UID spécifique."""
    # Implémentation simplifiée pour éviter les erreurs
    return 0.0


def _log_trade(uid, signal, alloc):
    """Journalise un signal de trading."""
    # S'assurer que les types sont corrects
    try:
        signal_str = str(signal)
        alloc_float = float(alloc)
        
        # Conversion pour l'affichage
        signal_display = "NEUTRE" if signal_str == "HOLD" else ("ACHAT" if signal_str == "BUY" else "VENTE")
        
        logger.send_log("trade_signal", "info", extra_labels={
            "uid": uid, 
            "signal": signal_display, 
            "allocation": round(alloc_float, 3)
        })
    except (ValueError, TypeError) as e:
        logger.send_log("log_trade_error", "warning", extra_labels={
            "uid": uid, 
            "signal_type": type(signal).__name__,
            "alloc_type": type(alloc).__name__,
            "error": str(e)
        })


def _log_cumulative(uid, mongo):
    """Journalise les statistiques cumulatives pour un UID."""
    try:
        # Sur les 50 derniers signaux de trade_signals
        recent = list(mongo.db[TRADE_SIGNALS_COLLECTION].find(
            {"uid": uid}, sort=[("created_at", -1)], limit=50))
        
        if not recent or len(recent) < 2:
            logger.send_log("insufficient_signals_for_stats", "info", extra_labels={"uid": uid})
            return
            
        # Calcul gain moyen et taux de win
        n_win = sum(1 for s in recent if s.get("gain", 0) > 0)
        n_loss = sum(1 for s in recent if s.get("gain", 0) < 0)
        n_neutral = sum(1 for s in recent if s.get("gain", 0) == 0)
        
        # Éviter division par zéro
        total_trades = n_win + n_loss + n_neutral
        win_rate = round(100 * n_win / total_trades, 1) if total_trades > 0 else 0
        
        # Calculer les gains moyens
        gains = [s.get("gain", 0) for s in recent if "gain" in s]
        avg_gain = np.mean(gains) if gains else 0
        
        # Calculer les gains moyens des trades gagnants et perdants
        win_gains = [g for g in gains if g > 0]
        loss_gains = [g for g in gains if g < 0]
        
        avg_win = np.mean(win_gains) if win_gains else 0
        avg_loss = np.mean(loss_gains) if loss_gains else 0
        
        # Calculer le profit factor
        profit_factor = abs(sum(win_gains) / sum(loss_gains)) if sum(loss_gains) != 0 else float('inf')
        
        logger.send_log("cumulative_performance", "info", extra_labels={
            "uid": uid,
            "n_trades": total_trades,
            "win_rate": win_rate,
            "avg_gain": round(avg_gain, 5),
            "avg_win": round(avg_win, 5),
            "avg_loss": round(avg_loss, 5),
            "profit_factor": round(profit_factor, 2),
            "n_win": n_win,
            "n_loss": n_loss,
            "n_neutral": n_neutral
        })
        
        # Contrôle du drawdown
        drawdown_limit = -0.05  # -5% max drawdown autorisé sur les 20 dernières trades
        recent_gains = [s.get("gain", 0) for s in recent if "gain" in s]
        
        if recent_gains:
            cum_gain = np.sum(recent_gains)
            cum_curve = np.cumsum(recent_gains)
            max_cum = np.maximum.accumulate(cum_curve)
            drawdowns = cum_curve - max_cum
            max_drawdown = np.min(drawdowns)
            
            logger.send_log("drawdown_monitoring", "info", extra_labels={
                "uid": uid,
                "cumulative_gain": round(cum_gain, 5),
                "max_drawdown": round(max_drawdown, 5),
                "current_drawdown": round(drawdowns[-1], 5) if len(drawdowns) > 0 else 0
            })
            
            # Alerte si le drawdown dépasse la limite
            if max_drawdown < drawdown_limit:
                logger.send_log("drawdown_alert", "warning", extra_labels={
                    "uid": uid,
                    "drawdown": round(max_drawdown, 5),
                    "limit": drawdown_limit
                })
                # Ici, tu peux choisir de suspendre les trades pour ce uid pendant X cycles
                # ou baisser l'allocation
                
                # Exemple: Marquer l'UID pour réduire l'allocation
                mongo.db["trading_controls"].update_one(
                    {"uid": uid},
                    {"$set": {
                        "reduce_allocation": True,
                        "reduction_factor": 0.5,  # Réduire de 50%
                        "reason": "drawdown_protection",
                        "updated_at": dt.datetime.utcnow()
                    }},
                    upsert=True
                )
    except Exception as e:
        logger.send_log("drawdown_calculation_error", "warning", extra_labels={
            "uid": uid,
            "error": str(e)
        })


def _generate_signal_for_uid(mongo, uid, model_path):
    """Génère un signal de trading pour un UID spécifique."""
    start_time = time.time()
    logger.send_log("signal_generation_started", "info", extra_labels={
        "uid": uid, 
        "model": model_path.name,
        "model_size_kb": round(model_path.stat().st_size / 1024, 2) if model_path.exists() else 0
    })
    
    try:
        # 1. Charger les données pour la prédiction
        try:
            df = _load_df(uid)
            if df.empty:
                logger.send_log("empty_dataframe", "warning", extra_labels={"uid": uid})
                return False
                
            # Journaliser les statistiques des données
            data_stats = {
                "rows": len(df),
                "columns": len(df.columns),
                "date_range": f"{df['run_at'].min()} to {df['run_at'].max()}" if 'run_at' in df.columns else "unknown",
                "memory_usage_mb": round(df.memory_usage(deep=True).sum() / (1024**2), 2)
            }
            logger.send_log("data_loaded_for_signal", "debug", extra_labels={**data_stats, "uid": uid})
        except Exception as e:
            import traceback
            logger.send_log("load_df_error", "error", extra_labels={
                "uid": uid, 
                "error": str(e),
                "traceback": traceback.format_exc()[:500]
            })
            return False
        
        # 2. Faire la prédiction
        try:
            # Utiliser le modèle mis en cache
            model = _load_model(model_path, uid)
            if model is None:
                logger.send_log("model_loading_failed", "error", extra_labels={"uid": uid})
                return False
                
            # Préparer les données
            df = _prepare_data_for_prediction(df, uid)
            
            # Faire la prédiction
            prediction_result = _predict(model, df, uid)
            if prediction_result is None:
                logger.send_log("prediction_failed", "error", extra_labels={"uid": uid})
                return False
                
            signal, alloc, price = prediction_result

            # Si le prix est None / NaN, tenter un dernier fallback
            if price is None or (isinstance(price, float) and (np.isnan(price) or np.isinf(price))):
                try:
                    price = _get_current_price(mongo, uid)
                    if price is not None:
                        logger.send_log("price_fallback_success", "info", extra_labels={
                            "uid": uid,
                            "price": price
                        })
                except Exception as e:
                    logger.send_log("price_fallback_error", "warning", extra_labels={
                        "uid": uid,
                        "error": str(e)
                    })

            # Vérifier les types de données
            try:
                signal_str = str(signal)
                alloc_float = float(alloc)
                if price is not None and not (isinstance(price, float) and (np.isnan(price) or np.isinf(price))):
                    price_float = float(price)
                else:
                    price_float = None
                # Journaliser la prédiction
                prediction_stats = {
                    "signal": signal_str,
                    "allocation": round(alloc_float, 4),
                    "price": round(price_float, 2) if price_float else None,
                    "prediction_time_ms": round((time.time() - start_time) * 1000, 2)
                }
                logger.send_log("prediction_complete", "info", extra_labels={**prediction_stats, "uid": uid})
            except (ValueError, TypeError) as e:
                logger.send_log("type_conversion_error", "error", extra_labels={
                    "uid": uid, 
                    "signal_type": type(signal).__name__,
                    "alloc_type": type(alloc).__name__,
                    "price_type": type(price).__name__,
                    "error": str(e)
                })
                return False
            
        except Exception as e:
            import traceback
            logger.send_log("prediction_error", "error", extra_labels={
                "uid": uid, 
                "error": str(e),
                "traceback": traceback.format_exc()[:500]
            })
            return False
            
        # 3. Enregistrer le signal dans la base de données
        try:
            import datetime as dt
            now = dt.datetime.utcnow()
            
            # Vérifier si un signal récent existe déjà
            recent_signal = mongo.db[TRADE_SIGNALS_COLLECTION].find_one(
                {
                    "uid": uid,
                    "created_at": {"$gte": now - dt.timedelta(hours=MAX_SIGNAL_AGE_HOURS)}
                },
                sort=[("created_at", -1)]
            )
            
            if recent_signal:
                logger.send_log("recent_signal_exists", "info", extra_labels={
                    "uid": uid,
                    "existing_signal": recent_signal["signal"],
                    "existing_time": recent_signal["created_at"],
                    "age_hours": round((now - recent_signal["created_at"]).total_seconds() / 3600, 2)
                })
                
                # Si le signal est le même, ne pas en créer un nouveau
                if recent_signal["signal"] == signal_str:
                    logger.send_log("signal_unchanged", "info", extra_labels={"uid": uid})
                    return True
            
            # Utiliser l'allocation pour dimensionner la position
            size = min(max(abs(alloc_float), 0.1), 1.0)  # Clamp entre 10% et 100%
            direction = 1 if alloc_float > 0 else -1

            # Créer le document de signal
            signal_doc = {
                "uid": uid,
                "signal": signal_str,
                "alloc": alloc_float,
                "price": price_float,
                "model_ckpt": model_path.name,
                "n_features": len(df.columns),
                "created_at": now,
                "run_at": now,
                "position_size": round(size, 4),
                "direction": direction
            }
            
            # Ajouter des métriques de performance si disponibles
            try:
                # Calculer le gain/perte potentiel
                if recent_signal and recent_signal.get("price") and price_float:
                    # Calculer le gain brut
                    prev_price = recent_signal["price"]
                    price_change = (price_float - prev_price) / prev_price
                    
                    # Appliquer le sens du signal précédent
                    if recent_signal["signal"] == "BUY":
                        raw_gain = price_change
                    elif recent_signal["signal"] == "SELL":
                        raw_gain = -price_change
                    else:
                        raw_gain = 0
                    
                    # Appliquer les frais
                    fee_rate = 0.0016  # 0.16% de frais de trading
                    spread_rate = 0.0008  # 0.08% de spread estimé
                    
                    signal_doc["gain"] = raw_gain - fee_rate - spread_rate
                    signal_doc["latent_gain"] = 0.0  # Sera mis à jour plus tard
                    signal_doc["fee_paid"] = fee_rate
                    signal_doc["spread_paid"] = spread_rate
                    
                    logger.send_log("gain_calculated", "info", extra_labels={
                        "uid": uid,
                        "raw_gain": round(raw_gain, 4),
                        "net_gain": round(signal_doc["gain"], 4),
                        "prev_price": round(prev_price, 2),
                        "current_price": round(price_float, 2)
                    })
            except Exception as e:
                logger.send_log("gain_calculation_error", "warning", extra_labels={
                    "uid": uid,
                    "error": str(e)
                })
            
            # Ajouter des informations sur l'épisode si disponibles
            try:
                # Nombre d'étapes dans l'épisode (estimation)
                signal_doc["episode_steps"] = len(df)
            except Exception as e:
                pass
                
            # Insérer le document
            logger.send_log("saving_signal", "info", extra_labels={
                "uid": uid, 
                "collection": TRADE_SIGNALS_COLLECTION,
                "signal": signal_str,
                "alloc": round(alloc_float, 4)
            })
            
            # Insérer dans la collection trade_signals
            mongo.db[TRADE_SIGNALS_COLLECTION].insert_one(signal_doc)
            
            # Insérer également dans la collection signals pour compatibilité
            mongo.db[SIG_COLLECTION].insert_one({
                "uid": uid,
                "signal": signal_str,
                "allocation": alloc_float,
                "created_at": now,
                "price": price_float,
                "model": model_path.name
            })
            
            # Logger le signal
            _log_trade(uid, signal_str, alloc_float)
            
            # Ajouter un trailing stop virtuel
            try:
                trailing_pct = 0.015  # 1.5% de trailing stop
                
                # Récupérer le dernier signal pour cet UID
                last_signal = mongo.db[TRADE_SIGNALS_COLLECTION].find_one(
                    {"uid": uid, "signal": {"$in": ["BUY", "SELL"]}},
                    sort=[("created_at", -1)]
                )
                
                if last_signal and "price" in last_signal and last_signal["price"]:
                    entry_price = last_signal["price"]
                    entry_signal = last_signal["signal"]
                    
                    # Calculer le niveau de stop
                    if entry_signal == "BUY":
                        stop_level = entry_price * (1 - trailing_pct)
                    else:  # SELL
                        stop_level = entry_price * (1 + trailing_pct)
                    
                    # Mettre à jour le document avec le trailing stop
                    mongo.db[TRADE_SIGNALS_COLLECTION].update_one(
                        {"_id": last_signal["_id"]},
                        {"$set": {
                            "trailing_stop": round(stop_level, 2),
                            "trailing_pct": trailing_pct,
                            "max_favorable_price": entry_price,  # Prix le plus favorable atteint
                            "stop_updated_at": dt.datetime.utcnow()
                        }}
                    )
                    
                    logger.send_log("trailing_stop_added", "info", extra_labels={
                        "uid": uid,
                        "entry_price": round(entry_price, 2),
                        "stop_level": round(stop_level, 2),
                        "trailing_pct": trailing_pct,
                        "signal": entry_signal
                    })
            except Exception as e:
                logger.send_log("trailing_stop_error", "warning", extra_labels={
                    "uid": uid,
                    "error": str(e)
                })
            
            # Mettre à jour les statistiques cumulatives si nécessaire
            if now.minute % 30 == 0:  # Toutes les 30 minutes
                _log_cumulative(uid, mongo)
                
            return True
            
        except Exception as e:
            import traceback
            logger.send_log("signal_save_error", "error", extra_labels={
                "uid": uid, 
                "error": str(e),
                "traceback": traceback.format_exc()[:500]
            })
            return False
            
    except Exception as e:
        import traceback
        logger.send_log("generate_signal_error", "error", extra_labels={
            "uid": uid, 
            "error": str(e),
            "traceback": traceback.format_exc()[:500]
        })
        return False


def _get_current_price(mongo, uid):
    """Récupère le prix actuel pour un UID donné."""
    try:
        # Méthode 1: Utiliser le dernier signal
        latest_signal = mongo.db[TRADE_SIGNALS_COLLECTION].find_one(
            {"uid": uid},
            sort=[("created_at", -1)]
        )
        
        if latest_signal and "price" in latest_signal:
            return latest_signal["price"]
        
        # Méthode 2: Utiliser les features récentes
        latest_feature = mongo.db["forecast_features"].find_one(
            {"uid": uid},
            sort=[("created_at", -1)]
        )
        
        if latest_feature:
            for price_field in ["close", "lastPrice", "last_price", "last"]:
                if price_field in latest_feature and latest_feature[price_field]:
                    return float(latest_feature[price_field])
        
        # Méthode 3: Utiliser une collection dédiée aux prix
        latest_price = mongo.db["latest_prices"].find_one({"uid": uid})
        if latest_price and "price" in latest_price:
            return float(latest_price["price"])
        
        logger.send_log("price_not_found", "warning", extra_labels={"uid": uid})
        return None
        
    except Exception as e:
        logger.send_log("price_fetch_error", "warning", extra_labels={
            "uid": uid,
            "error": str(e)
        })
        return None

def _update_trailing_stops(mongo, uid=None):
    """Met à jour les trailing stops en fonction des prix actuels."""
    try:
        # Construire la requête pour récupérer les signaux actifs
        query = {
            "signal": {"$in": ["BUY", "SELL"]},
            "trailing_stop": {"$exists": True},
            "closed_at": {"$exists": False}
        }
        if uid:
            query["uid"] = uid
        
        # Récupérer tous les signaux actifs avec trailing stop
        active_signals = list(mongo.db[TRADE_SIGNALS_COLLECTION].find(query))
        
        if not active_signals:
            return 0
            
        logger.send_log("updating_trailing_stops", "debug", extra_labels={
            "active_signals": len(active_signals),
            "uid_filter": uid if uid else "all"
        })
        
        updates_count = 0
        triggers_count = 0
        
        for signal in active_signals:
            try:
                signal_uid = signal.get("uid")
                entry_price = signal.get("price")
                entry_signal = signal.get("signal")
                current_stop = signal.get("trailing_stop")
                trailing_pct = signal.get("trailing_pct", 0.015)  # 1.5% par défaut
                max_favorable_price = signal.get("max_favorable_price", entry_price)
                
                if not all([signal_uid, entry_price, entry_signal, current_stop]):
                    continue
                
                # Récupérer le prix actuel
                current_price = _get_current_price(mongo, signal_uid)
                if not current_price:
                    continue
                
                # Vérifier si le stop est déclenché
                stop_triggered = False
                if entry_signal == "BUY" and current_price <= current_stop:
                    stop_triggered = True
                elif entry_signal == "SELL" and current_price >= current_stop:
                    stop_triggered = True
                
                if stop_triggered:
                    # Calculer le gain/perte
                    if entry_signal == "BUY":
                        gain = (current_stop / entry_price) - 1
                    else:  # SELL
                        gain = 1 - (current_stop / entry_price)
                    
                    # Marquer le signal comme fermé
                    mongo.db[TRADE_SIGNALS_COLLECTION].update_one(
                        {"_id": signal["_id"]},
                        {"$set": {
                            "closed_at": dt.datetime.utcnow(),
                            "close_price": current_stop,
                            "close_reason": "trailing_stop",
                            "final_gain": round(gain, 4)
                        }}
                    )
                    
                    logger.send_log("trailing_stop_triggered", "info", extra_labels={
                        "uid": signal_uid,
                        "entry_price": round(entry_price, 2),
                        "stop_price": round(current_stop, 2),
                        "gain_pct": round(gain * 100, 2),
                        "signal": entry_signal
                    })
                    
                    triggers_count += 1
                    continue
                
                # Mettre à jour le prix le plus favorable
                new_max_price = max_favorable_price
                if entry_signal == "BUY" and current_price > max_favorable_price:
                    new_max_price = current_price
                elif entry_signal == "SELL" and current_price < max_favorable_price:
                    new_max_price = current_price
                
                # Calculer le nouveau niveau de stop si le prix le plus favorable a changé
                new_stop = current_stop
                if new_max_price != max_favorable_price:
                    if entry_signal == "BUY":
                        new_stop = new_max_price * (1 - trailing_pct)
                    else:  # SELL
                        new_stop = new_max_price * (1 + trailing_pct)
                    
                    # Ne jamais reculer le stop (toujours protéger les gains)
                    if entry_signal == "BUY":
                        new_stop = max(new_stop, current_stop)
                    else:  # SELL
                        new_stop = min(new_stop, current_stop)
                
                # Mettre à jour le stop si nécessaire
                if new_stop != current_stop or new_max_price != max_favorable_price:
                    mongo.db[TRADE_SIGNALS_COLLECTION].update_one(
                        {"_id": signal["_id"]},
                        {"$set": {
                            "trailing_stop": round(new_stop, 2),
                            "max_favorable_price": new_max_price,
                            "stop_updated_at": dt.datetime.utcnow(),
                            "current_gain": round((current_price / entry_price - 1) * 100, 2) if entry_signal == "BUY" else round((1 - current_price / entry_price) * 100, 2)
                        }}
                    )
                    
                    logger.send_log("trailing_stop_updated", "debug", extra_labels={
                        "uid": signal_uid,
                        "previous_stop": round(current_stop, 2),
                        "new_stop": round(new_stop, 2),
                        "current_price": round(current_price, 2),
                        "max_price": round(new_max_price, 2),
                        "signal": entry_signal
                    })
                    
                    updates_count += 1
                    
            except Exception as e:
                logger.send_log("signal_processing_error", "warning", extra_labels={
                    "uid": signal.get("uid", "unknown"),
                    "error": str(e)
                })
        
        if updates_count > 0 or triggers_count > 0:
            logger.send_log("trailing_stops_summary", "info", extra_labels={
                "updates": updates_count,
                "triggers": triggers_count,
                "total_signals": len(active_signals)
            })
            
        return updates_count + triggers_count
                
    except Exception as e:
        logger.send_log("update_trailing_stops_error", "error", extra_labels={
            "error": str(e),
            "uid": uid if uid else "all"
        })
        return 0


def run_cycle(mongo):
    """Exécute un cycle complet de génération de signaux."""
    try:
        # Récupérer la liste des UIDs valides
        valid_uids = _get_uids_to_process(mongo)
        
        if not valid_uids:
            logger.send_log("no_valid_uids_found", "warning")
            return
        
        # Mettre à jour les trailing stops pour tous les UIDs
        _update_trailing_stops(mongo)
        
        # Générer des signaux pour chaque UID
        for uid in valid_uids:
            try:
                # Récupérer le modèle le plus récent
                model_path = _latest_model(uid)
                if not model_path:
                    logger.send_log("no_model_found", "warning", extra_labels={"uid": uid})
                    continue
                
                # Générer un signal
                success = _generate_signal_for_uid(mongo, uid, model_path)
                
                if success:
                    logger.send_log("signal_generated", "info", extra_labels={"uid": uid})
                else:
                    logger.send_log("signal_generation_failed", "warning", extra_labels={"uid": uid})
                
                # Mettre à jour les trailing stops spécifiques à cet UID après génération
                _update_trailing_stops(mongo, uid)
                
            except Exception as e:
                logger.send_log("uid_processing_error", "error", extra_labels={
                    "uid": uid,
                    "error": str(e)
                })
        
        # Vérifier les modèles disponibles
        check_model_availability(mongo)
        
    except Exception as e:
        logger.send_log("run_cycle_error", "error", extra_labels={"error": str(e)})


def _get_uids_to_process(mongo):
    """Récupère la liste des UIDs à traiter."""
    try:
        # Récupérer tous les UIDs disponibles dans les features
        uids = set(mongo.db["forecast_features"].distinct("uid"))
        
        # Vérifier également les UIDs qui ont des modèles
        model_uids = set()
        for model_path in MODEL_DIR.glob("sac_*.zip"):
            parts = model_path.stem.split("_")
            if len(parts) >= 2:
                model_uids.add(parts[1])
        
        # Combiner les deux ensembles
        all_uids = uids.union(model_uids)
        
        if not all_uids:
            logger.send_log("no_uids_found", "warning")
            return []
            
        # Filtrer pour ne garder que les UIDs qui ont à la fois des features et des modèles
        valid_uids = []
        for uid in all_uids:
            has_model = any(MODEL_DIR.glob(f"sac_{uid}_*.zip"))
            has_features = PARQUET_DIR.joinpath(f"features-{uid}.parquet").exists()  # Utiliser PARQUET_DIR
            
            if has_model and has_features:
                # Vérifier la taille du modèle
                model_files = list(MODEL_DIR.glob(f"sac_{uid}_*.zip"))
                valid_model = False
                for model_file in model_files:
                    if model_file.stat().st_size >= 1000:  # Au moins 1 Ko
                        valid_model = True
                        break
                
                if valid_model:
                    valid_uids.append(uid)
                else:
                    logger.send_log("uid_models_too_small", "warning", extra_labels={
                        "uid": uid,
                        "model_count": len(model_files)
                    })
            else:
                logger.send_log("uid_missing_requirements", "debug", extra_labels={
                    "uid": uid, 
                    "has_model": has_model,
                    "has_features": has_features
                })
        
        logger.send_log("uids_found", "debug", extra_labels={
            "total_uids": len(all_uids),
            "valid_uids": len(valid_uids),
            "valid_list": valid_uids[:10] if valid_uids else []  # Limiter à 10 pour éviter des logs trop grands
        })
        
        if not valid_uids:
            logger.send_log("no_valid_uids", "warning", extra_labels={
                "total_uids": len(all_uids),
                "missing_models": len([uid for uid in all_uids if not any(MODEL_DIR.glob(f"sac_{uid}_*.zip"))]),
                "missing_features": len([uid for uid in all_uids if not PARQUET_DIR.joinpath(f"features-{uid}.parquet").exists()])
            })
        
        return valid_uids
    except Exception as e:
        logger.send_log("get_uids_error", "error", extra_labels={"error": str(e)})
        return []


# ═════════════════════════════ main ═══════════════════════════════
def main():
    """Service daemon : exécute run_cycle() en boucle."""
    logger.send_log("daemon_started", "info", extra_labels={
        "version": "1.2.0",
        "model_dir": str(MODEL_DIR),
        "feature_dir": str(PARQUET_DIR),
        "sleep_seconds": SLEEP_SECONDS
    })

    # Vérifier les répertoires une fois au démarrage
    if not MODEL_DIR.exists():
        logger.send_log("model_dir_missing", "error", extra_labels={"path": str(MODEL_DIR)})
        return

    if not PARQUET_DIR.exists():
        logger.send_log("feature_dir_missing", "error", extra_labels={"path": str(PARQUET_DIR)})
        return

    # Connexion Mongo (persistante)
    mongo = MongoUtils()
    mongo.connect(service="signal_gen")

    cycle_count = 0
    while True:
        cycle_start = time.time()
        try:
            run_cycle(mongo)
        except Exception as e:
            logger.send_log("cycle_uncaught_exception", "error", extra_labels={"error": str(e)})

        _periodic_cleanup(cycle_count)
        cycle_count += 1

        # Attendre jusqu’au prochain cycle
        elapsed = time.time() - cycle_start
        sleep_for = max(1, SLEEP_SECONDS - elapsed)
        time.sleep(sleep_for)


 


def check_model_availability(mongo):
    """Vérifie la disponibilité des modèles et génère un rapport."""
    try:
        logger.send_log("checking_models", "debug")
        
        # Vérifier si MODEL_DIR existe
        if not MODEL_DIR.exists():
            logger.send_log("model_dir_missing", "warning", extra_labels={"path": str(MODEL_DIR)})
            return {}
            
        model_files = list(MODEL_DIR.glob("sac_*.zip"))
        logger.send_log("model_files_found", "debug", extra_labels={"count": len(model_files)})
        
        # Grouper par paire de trading
        models_by_pair = {}
        for model_path in model_files:
            try:
                name = model_path.stem
                parts = name.split("_")
                if len(parts) >= 2:
                    pair = parts[1]  # Le format est sac_PAIRID_date.zip
                    if pair not in models_by_pair:
                        models_by_pair[pair] = []
                    models_by_pair[pair].append(str(model_path))
            except Exception as e:
                logger.send_log("model_parse_error", "warning", extra_labels={"file": str(model_path), "error": str(e)})
        
        # Générer un rapport
        logger.send_log("model_availability", "info", extra_labels={
            "total_models": len(model_files),
            "total_pairs": len(models_by_pair)
        })
        
        # Identifier les paires sans modèles
        all_pairs = set()
        try:
            # Récupérer toutes les paires depuis la base de données
            mongo_pairs = mongo.db["forecast_features"].distinct("uid")
            all_pairs.update(mongo_pairs)
            logger.send_log("db_pairs_found", "debug", extra_labels={"count": len(all_pairs)})
        except Exception as e:
            logger.send_log("db_pairs_fetch_error", "error", extra_labels={"error": str(e)})
        
        missing_models = all_pairs - set(models_by_pair.keys())
        if missing_models:
            logger.send_log("missing_models", "warning", extra_labels={
                "count": len(missing_models),
                "pairs": list(missing_models)[:10]  # Limite pour éviter des logs trop grands
            })
        
        return models_by_pair
    except Exception as e:
        logger.send_log("model_check_failed", "error", extra_labels={"error": str(e)})
        # Ne pas propager l'exception pour éviter de bloquer le démarrage
        return {}


def track_model_performance(uid, prediction, actual_price, previous_price):
    """Suit les performances des prédictions en production."""
    from common.grafana_utils import GrafanaUtils
    logger = GrafanaUtils(service="model_performance")
    
    # Calculer les métriques de performance
    price_change = (actual_price - previous_price) / previous_price if previous_price else 0
    prediction_direction = 1 if prediction > 0 else (-1 if prediction < 0 else 0)
    actual_direction = 1 if price_change > 0 else (-1 if price_change < 0 else 0)
    direction_match = prediction_direction == actual_direction
    
    # Préparer les métriques
    performance_metrics = {
        "uid": uid,
        "prediction": prediction,
        "prediction_direction": prediction_direction,
        "actual_price": actual_price,
        "previous_price": previous_price,
        "price_change_pct": round(price_change * 100, 4),
        "actual_direction": actual_direction,
        "direction_match": direction_match,
        "timestamp": dt.datetime.now().isoformat()
    }
    
    # Envoyer le log
    logger.send_log("prediction_performance", "info", extra_labels=performance_metrics)


def _debug_model(model_path, uid):
    """Fonction de débogage pour tester le chargement et l'utilisation d'un modèle."""
    try:
        import os
        import traceback
        import numpy as np
        
        logger.send_log("debug_model_start", "info", extra_labels={
            "uid": uid, 
            "model_path": str(model_path),
            "model_exists": model_path.exists(),
            "model_size_kb": round(model_path.stat().st_size / 1024, 2) if model_path.exists() else 0
        })
        
        # Vérifier si le modèle existe
        if not model_path.exists():
            logger.send_log("debug_model_not_found", "error", extra_labels={"uid": uid})
            return False
            
        # Désactiver les avertissements de TensorFlow
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
        
        # Importer les bibliothèques nécessaires
        try:
            from stable_baselines3 import SAC
            logger.send_log("debug_import_success", "info", extra_labels={"uid": uid})
        except ImportError as e:
            logger.send_log("debug_import_error", "error", extra_labels={
                "uid": uid, 
                "error": str(e),
                "traceback": traceback.format_exc()[:500]
            })
            return False
            
        # Tenter de charger le modèle
        try:
            logger.send_log("debug_model_load_attempt", "info", extra_labels={"uid": uid})
            model = SAC.load(str(model_path), verbose=0)
            
            if model is None:
                logger.send_log("debug_model_load_none", "error", extra_labels={"uid": uid})
                return False
                
            logger.send_log("debug_model_loaded", "info", extra_labels={
                "uid": uid,
                "model_type": type(model).__name__,
                "obs_shape": model.observation_space.shape[0] if hasattr(model, 'observation_space') else "unknown",
                "action_shape": model.action_space.shape[0] if hasattr(model, 'action_space') else "unknown"
            })
            
            # Tester la prédiction avec des données aléatoires
            try:
                obs_shape = model.observation_space.shape[0]
                test_obs = np.zeros(obs_shape, dtype=np.float32)
                
                logger.send_log("debug_predict_attempt", "info", extra_labels={
                    "uid": uid,
                    "obs_shape": obs_shape
                })
                
                action, _ = model.predict(test_obs, deterministic=True)
                
                logger.send_log("debug_predict_success", "info", extra_labels={
                    "uid": uid,
                    "action_shape": action.shape,
                    "action_value": float(action[0])
                })
                
                return True
                
            except Exception as e:
                logger.send_log("debug_predict_error", "error", extra_labels={
                    "uid": uid,
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "traceback": traceback.format_exc()[:500]
                })
                return False
                
        except Exception as e:
            logger.send_log("debug_model_load_error", "error", extra_labels={
                "uid": uid,
                "error": str(e),
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc()[:500]
            })
            return False
            
    except Exception as e:
        logger.send_log("debug_model_exception", "error", extra_labels={
            "uid": uid,
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc()[:500]
        })
        return False


def _periodic_cleanup(cycle_count):
    """Effectue un nettoyage périodique des ressources."""
    # Nettoyer le cache des modèles tous les 100 cycles
    if cycle_count % 100 == 0:
        _clear_model_cache()
        
    # Forcer la collecte des déchets tous les 10 cycles
    if cycle_count % 10 == 0:
        gc.collect()
        logger.send_log("garbage_collection", "debug", extra_labels={
            "cycle": cycle_count
        })


def _check_signal_collections(mongo):
    """Vérifie et répare les collections de signaux."""
    try:
        # Vérifier si les collections existent
        collections = mongo.db.list_collection_names()
        
        if TRADE_SIGNALS_COLLECTION not in collections:
            logger.send_log("trade_signals_collection_missing", "warning")
            # La collection sera créée automatiquement lors de la première insertion
        
        if SIG_COLLECTION not in collections:
            logger.send_log("signals_collection_missing", "warning")
            # La collection sera créée automatiquement lors de la première insertion
        
        # Vérifier le nombre de documents dans chaque collection
        trade_signals_count = mongo.db[TRADE_SIGNALS_COLLECTION].count_documents({})
        signals_count = mongo.db[SIG_COLLECTION].count_documents({})
        
        logger.send_log("signal_collections_status", "info", extra_labels={
            "trade_signals_count": trade_signals_count,
            "signals_count": signals_count
        })
        
        # Si une collection est vide mais l'autre non, copier les données
        if trade_signals_count == 0 and signals_count > 0:
            logger.send_log("copying_signals_to_trade_signals", "info")
            
            # Récupérer les signaux récents
            recent_signals = list(mongo.db[SIG_COLLECTION].find(
                {},
                sort=[("created_at", -1)],
                limit=1000
            ))
            
            # Convertir en format trade_signals
            for signal in recent_signals:
                try:
                    trade_signal = {
                        "uid": signal["uid"],
                        "signal": signal["signal"],
                        "alloc": signal.get("allocation", 0),
                        "price": signal.get("price"),
                        "created_at": signal["created_at"],
                        "run_at": signal["created_at"],
                        "model_ckpt": signal.get("model", "unknown")
                    }
                    
                    # Insérer dans trade_signals
                    mongo.db[TRADE_SIGNALS_COLLECTION].insert_one(trade_signal)
                except Exception as e:
                    logger.send_log("signal_conversion_error", "warning", extra_labels={
                        "error": str(e),
                        "signal_id": str(signal.get("_id"))
                    })
            
            logger.send_log("signals_copied", "info", extra_labels={
                "copied_count": len(recent_signals)
            })
        
        return True
    except Exception as e:
        logger.send_log("check_signal_collections_error", "error", extra_labels={
            "error": str(e)
        })
        return False






# Move the __main__ block to the end of the file

if __name__ == "__main__":
    args = parse_args()
    
    # Si un asset spécifique est demandé, générer un signal pour cet asset uniquement
    if args.asset:
        logger.send_log("single_asset_mode", "info", extra_labels={"asset": args.asset})
        
        # Connexion à MongoDB
        mongo = MongoUtils()
        mongo.connect(service="signal_gen")
        
        # Vérifier si un modèle existe pour cet asset
        model_path = _latest_model(args.asset)
        if not model_path:
            logger.send_log("no_model", "error", extra_labels={"asset": args.asset})
            sys.exit(1)
            
        # Générer le signal
        success = _generate_signal_for_uid(mongo, args.asset, model_path)
        
        if success:
            logger.send_log("signal_generated", "info", extra_labels={"asset": args.asset})
            sys.exit(0)
        else:
            logger.send_log("signal_generation_failed", "error", extra_labels={"asset": args.asset})
            sys.exit(1)
    else:
        # Mode service continu
        main()  # Appel de la fonction main() au lieu de logger.send_log("daemon_started", "info")
