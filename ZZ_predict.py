from flask import Flask, request, jsonify
import numpy as np
import pandas as pd
import xgboost as xgb
import joblib
import os
import copy
import gc
import time
import json
import threading
from datetime import datetime, timedelta, timezone
from scipy.interpolate import interp1d
from pymongo import MongoClient
from concurrent.futures import ThreadPoolExecutor
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from common.indicators_utils import IndicatorsUtils
from common.models_utils import ModelsUtils

app = Flask(__name__)

SERVICE = "predict"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
indicator = IndicatorsUtils(service=SERVICE)
models = ModelsUtils(service=SERVICE)

MODEL_DIR = "/home/<USER>/cryptobot/models/"
NB_THREADS = 4

execution_lock = threading.Lock()





def extract_features_dataframe(df_input, used_features):
    """
    S'assure que le DataFrame contient exactement les colonnes utilisées à l'entraînement,
    dans le bon ordre, et uniquement celles-ci.
    """
    df = df_input.copy()

    # Ajoute les colonnes manquantes
    for col in used_features:
        if col not in df.columns:
            df[col] = np.nan  # ou 0 si tu préfères

    # Garde uniquement les colonnes utiles et dans le bon ordre
    df = df[used_features]

    # Diagnostic
    missing_cols = df.columns[df.isnull().any()].tolist()
    if missing_cols:
        logger.send_log(f"⚠️ Colonnes avec valeurs manquantes dans les features d'entrée : {missing_cols}", "warning")

    return df



def compute_base_price_from_series(price_series, horizon_minutes, method="sma"):
    """
    Calcule une SMA ou EMA sur 20% de l'horizon à partir d'une série de prix.
    """
    # ⚠️ S'assurer que l'index est timezone-aware (UTC)
    if price_series.index.tz is None:
        price_series.index = price_series.index.tz_localize("UTC")

    if len(price_series) < 2:
        return price_series.iloc[-1]  # fallback : dernière valeur

    interval_minutes = (price_series.index[1] - price_series.index[0]).total_seconds() / 60.0
    window_size = max(1, int(horizon_minutes * 0.2 / interval_minutes))

    if method == "ema":
        return price_series.ewm(span=window_size).mean().iloc[-1]
    else:
        return price_series[-window_size:].mean()






def estimate_uncertainty(model, raw_data, scaler):
    """
    Estime le log_return et une incertitude fictive basée sur la variance des arbres (simulée).
    """
    # Prédiction unique
    log_return_scaled = model.predict(raw_data)[0]
    log_return_pred = scaler.inverse_transform([[log_return_scaled]])[0][0]

    # Fallback : estimer une incertitude artificielle basée sur le nombre d’arbres
    try:
        booster = model.get_booster()
        # ❌ Ne pas utiliser .best_iteration si early_stopping n’est pas activé
        num_trees = booster.best_iteration + 1  # +1 car indexé à 0
    except AttributeError:
        num_trees = model.get_params().get("n_estimators", 100)

    # Heuristique : plus il y a d’arbres, plus l’incertitude diminue légèrement
    base_std = 0.002  # base de 0.2% log_return
    log_return_std = base_std * (1 + 5 / np.sqrt(num_trees))

    return log_return_pred, log_return_std




def make_prediction_classification(uid, raw_data, horizon, model):
    """
    Effectue une prédiction binaire (hausse / non hausse) avec un modèle de classification XGBoost.
    Renvoie un score de probabilité d’un mouvement haussier.
    """
    model_name = horizon["full_name"]

    if len(raw_data) != 1:
        #logger.send_log(f"⚠️ - {uid} - {model_name} - raw_data contient {len(raw_data)} lignes, attendu 1.", "warning")
        raw_data = raw_data.head(1)

    try:
        # Prédiction de la probabilité que la classe soit 1 (hausse)
        y_proba = model.predict_proba(raw_data)[:, 1]  # Probabilité d'une hausse
        y_class = (y_proba >= 0.5).astype(int)[0]

        prob = float(y_proba[0])

        confidence_score = float(2 * abs(prob - 0.5))  # 0.0 = incertitude, 1.0 = forte confiance

        return {
            "prediction": int(y_class),
            "probability": prob,
            "confidence_score": confidence_score,
        }

    except Exception as e:
        logger.send_log(f"❌ - {uid} - {model_name} - Erreur prédiction classification : {e}", "error")
        return None



def make_prediction_regression(uid, raw_data, horizon, base_price, model, scaler, method="ema"):
    """
    Exécute une prédiction XGBoost avec incertitude et projection depuis une base déjà fournie (SMA/EMA).
    """
    model_name = horizon["full_name"]

    if len(raw_data) != 1:
        #logger.send_log(f"⚠️ - {uid} - {model_name} - raw_data contient {len(raw_data)} lignes, attendu 1.", "warning")
        raw_data = raw_data.head(1)

    try:
        # 🔁 Estimation log_return et incertitude
        log_return_pred, log_return_std = estimate_uncertainty(model, raw_data, scaler)

        # 📈 Estimation du prix futur depuis la base lissée
        future_price = float(base_price * np.exp(log_return_pred))
        future_price_min = float(base_price * np.exp(log_return_pred - log_return_std))
        future_price_max = float(base_price * np.exp(log_return_pred + log_return_std))
        variation_pct = float((future_price - base_price) / base_price * 100)

        confidence_score = float(1 / (1 + abs(log_return_std)))

        return {
            "log_return_pred": log_return_pred,
            "log_return_std": log_return_std,
            "base_price": float(base_price),
            "future_price_estimated": future_price,
            "future_price_min": future_price_min,
            "future_price_max": future_price_max,
            "variation_pct": variation_pct,
            "confidence_score": confidence_score,
            "base_method": method
        }

    except Exception as e:
        logger.send_log(f"❌ - {uid} - {model_name} - Erreur prédiction (avec base fournie) : {e}", "error")
        return None
    


def load_model(uid,model_name):
    # 📦 Chargement du modèle
    model_path = os.path.join(MODEL_DIR, f"{model_name}.joblib")
    if not os.path.exists(model_path):
        #logger.send_log(f"⚠️ - {uid} - {model_name} - Modèle introuvable à {model_path}", "warning")
        return None
    try:
        loaded_model = joblib.load(model_path)
    except Exception as e:
        logger.send_log(f"❌ - {uid} - {model_name} - Erreur chargement modèle : {e}", "error")
        return None
    return loaded_model



def ask_prediction_classification(uid, horizon, indicator_params, original_time): 
    # Chargement du modèle de classification
    model_name = f"xgboost_model_{uid}_{horizon['name']}_classification"
    model = load_model(uid, model_name)
    horizon["full_name"] = model_name

    if model is None:
        return None

    model_horizon = model["model"]
    used_features = model["used_features"]
    safe_shift_points = model['safe_shift_points']

    # Récupération des hyperparamètres
    classification_model_parameters = mongo.get_hyperparams(horizon, uid, "classification", horizon["horizon_hour"])
    if classification_model_parameters is None:
        logger.send_log(f"⚠️ - {uid} - {model_name} - {horizon['name']} - Hyperparamètres non trouvés. Annulation.", "warning")
        return None
    
    try:
        # Décalage temporel pour constituer les features d'entrée
        #offset_points = indicator.compute_target_offset(horizon["horizon_hour"])
        offset_points = offset_points = horizon['shift_value']
        cutoff_time = original_time - timedelta(minutes=offset_points * 5)
        cutoff_time = cutoff_time.replace(tzinfo=timezone.utc)

        candlesticks = mongo.get_candlesticks(uid, until_date=cutoff_time)

        # Construction d'une série temporelle indexée (optionnelle)
        try:
            ts_price = pd.Series(
                [c["lastPrice"] for c in candlesticks if "lastPrice" in c],
                index=pd.to_datetime([c["created_at"] for c in candlesticks])
            ).sort_index()
        except Exception as e:
            logger.send_log(f"❌ - {uid} - {model_name} - {horizon['name']} - Erreur série temporelle : {e}", "error")
            return None

        horizon_minutes = int(horizon["horizon_hour"]) * 60
        base_price = compute_base_price_from_series(ts_price, horizon_minutes, method="ema")

        # Génération des features
        raw_data = indicator.generate_prediction_sample_safe(candlesticks, indicator_params["indicator_params"], uid, safe_shift_points)
        if raw_data is None:
            logger.send_log(f"⚠️ - {uid} - {model_name} - {horizon['name']} - Aucun feature généré pour prédiction.", "warning")
            return None

        df_features = extract_features_dataframe(raw_data, used_features)

        try:
            predicted_data = make_prediction_classification(
                uid=uid,
                raw_data=df_features,
                horizon=horizon,
                model=model_horizon
            )
        except Exception as e:
            logger.send_log(f"❌ - {uid} - {model_name} - Erreur prédiction classification : {e}", "error")
            return None

    except Exception as e:
        logger.send_log(f"❌ - {uid} - {model_name} - Erreur préparation prédiction classification : {e}", "error")
        return None

    return predicted_data, safe_shift_points



def ask_prediction_regression(uid, horizon, indicator_params, original_time): 
    # Load regression model
    model_name = f"xgboost_model_{uid}_{horizon['name']}_regression"
    model = load_model(uid, model_name)
    horizon["full_name"] = model_name

    if model is None:
        return None

    model_horizon = model["model"]
    used_features = model['used_features']
    scaler = model["scaler"]
    safe_shift_points = model['safe_shift_points']
    interval_min = 5

    # Récupération du document hyperparams pour update
    regression_model_parameters = mongo.get_hyperparams(horizon, uid, "regression", horizon['horizon_hour'])
    if regression_model_parameters is None:
        logger.send_log(f"⚠️ - {uid} - {model_name} - {horizon['name']}  Hyperparamètres non trouvés. Annulation", "warning")
        return None

    try:
        # 🕒 Date à laquelle les features sont "observées"
        base_time = original_time - timedelta(minutes=safe_shift_points * interval_min)
        base_time = base_time.replace(tzinfo=timezone.utc)
        predicted_for_time = base_time + timedelta(hours=horizon["horizon_hour"])


        # 🔁 Récupérer les données jusqu’à cette base_time
        candlesticks = mongo.get_candlesticks(uid, until_date=base_time)

        # 📈 Série temporelle pour trouver le bon prix de base
        try:
            timestamps = pd.to_datetime([c["created_at"] for c in candlesticks])

            # Localiser uniquement si naïf
            if timestamps.tz is None:
                timestamps = timestamps.tz_localize("UTC")

            ts_price = pd.Series(
                [c["lastPrice"] for c in candlesticks if "lastPrice" in c],
                index=timestamps
            ).sort_index()
        except Exception as e:
            logger.send_log(f"❌ - {uid} - {model_name} - Erreur création série SMA/EMA : {e}", "error")
            return None

        # 📍 Base de prix alignée avec le moment des features
        base_price = ts_price.asof(base_time)
        if base_price is None:
            logger.send_log(f"❌ - {uid} - {model_name} - Aucun prix disponible pour base_time={base_time}", "error")
            return None

        # 🔁 Générer les features avec indicateurs sécurisés
        raw_data = indicator.generate_prediction_sample_safe(
            candlesticks,
            indicator_params["indicator_params"],
            uid,
            safe_shift_points
        )
        if raw_data is None:
            logger.send_log(f"⚠️ - {uid} - {model_name} - Aucun donnée formatée disponible à la prédiction. Suivant ...", "warning")
            return None

        df_features = extract_features_dataframe(raw_data, used_features)

        # 📈 Prédiction
        try:
            predicted_data = make_prediction_regression(
                uid,
                df_features,
                horizon,
                base_price,
                model_horizon,
                scaler
            )
        except Exception as e:
            logger.send_log(f"❌ - {uid} - {model_name} - Erreur lors de la prédiction : {e}", "error")
            return None

    except Exception as e:
        logger.send_log(f"❌ - {uid} - {model_name} - Erreur préparation prédiction : {e}", "error")
        return None
    
    return predicted_data, safe_shift_points, predicted_for_time


'''
def ask_prediction_regression(uid,horizon, indicator_params, original_time): 
    # Load regression model
    model_name= f"xgboost_model_{uid}_{horizon['name']}_regression"
    model = load_model(uid,model_name)
    horizon["full_name"] = model_name

    if model is None:
        return None

    model_horizon = model["model"]
    used_features = model['used_features']
    scaler = model["scaler"]
    safe_shift_points = model['safe_shift_points']

    # Récupération du document hyperparams pour update
    regression_model_parameters = mongo.get_hyperparams(horizon, uid, "regression", horizon['horizon_hour'])
    if regression_model_parameters == None:
        logger.send_log(f"⚠️ - {uid} - {model_name} - {horizon['name']}  Hyperparamètres non trouvés pour {model_name}. Annulation", "warning")
        return None
    
    try:
        # Décalage = target_offset en minutes
        #offset_points = indicator.compute_target_offset(horizon["horizon_hour"])
        offset_points = horizon['shift_value']
        cutoff_time = original_time - timedelta(minutes=offset_points * 5)
        cutoff_time = cutoff_time.replace(tzinfo=timezone.utc)

        # Récupérer les candlesticks jusqu’à cutoff_time (et pas jusqu’à original_time)
        candlesticks = mongo.get_candlesticks(uid, until_date=cutoff_time)

        # Construction d'une série temporelle indexée pour SMA/EMA
        try:
            ts_price = pd.Series(
                [c["lastPrice"] for c in candlesticks if "lastPrice" in c],
                index=pd.to_datetime([c["created_at"] for c in candlesticks])
            ).sort_index()
        except Exception as e:
            logger.send_log(f"❌ - {uid} - {model_name} - {horizon['name']} - Erreur création série SMA/EMA : {e}", "error")
            return None


        horizon_minutes = int(horizon["horizon_hour"]) * 60
        base_price = compute_base_price_from_series(ts_price, horizon_minutes, method="ema")


        raw_data = indicator.generate_prediction_sample_safe(candlesticks, indicator_params["indicator_params"], uid, safe_shift_points)
        if raw_data is None:
            logger.send_log(f"⚠️ - {uid} - {model_name} - {horizon['name']} - Aucun donnée formatée disponible à la prédiction. Suivant ...", "warning")
            return None

        df_features = extract_features_dataframe(raw_data, used_features)
        #logger.send_log(f"📊 Colonnes Extraites ({len(df_features.columns)} total) : {df_features.columns.tolist()}", "debug")


        try:
            predicted_data = make_prediction_regression(
                uid,
                df_features,
                horizon,
                base_price,
                model_horizon,
                scaler
            )

        except Exception as e:
                logger.send_log(f"❌ - {uid} - {model_name} - Erreur lors de la prédiction : {e}", "error")
                return None
    except Exception as e:
            logger.send_log(f"❌ - {uid} - {model_name} - Erreur lors de la préparation de la prédiction du modèle : {e}", "error")
            return None
    
    return predicted_data, safe_shift_points
'''




def compute_predictions(uid, missing_prediction, original_horizon):
    horizon = copy.deepcopy(original_horizon)  # ⬅️ Copie profonde pour isoler chaque tâche

    # 🔁 Paramètres des indicateurs
    indicator_params = mongo.get_indicatorsparams(horizon["name"], uid, horizon["horizon_hour"])
    if indicator_params is None:
        #logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Aucun paramètre d'indicateur trouvé.", "warning")
        return False

    
    logger.send_log(f"⏳ - {uid} - {horizon['name']} - Demande de prédictions pour {len(missing_prediction)} indicateurs.", "info")
    
    for candlestick in missing_prediction:
        current_price = candlestick["lastPrice"]
        #original_time = candlestick["created_at"]
        #predicted_for_time = original_time + timedelta(hours=int(horizon["horizon_hour"]))

        original_time = candlestick["created_at"]

        try:
            try:
                predicted_data_regression, safe_shift_points_regression, predicted_for_time = ask_prediction_regression(uid,horizon, indicator_params, original_time)
                    
            except Exception as e:
                logger.send_log(f"❌ - {uid} - {horizon["name"]} - Erreur lors de la prédiction en régression : {e}", "error")
                return False

            try:
                predicted_data_classification = ask_prediction_classification(uid,horizon, indicator_params, original_time)
                    
            except Exception as e:
                logger.send_log(f"❌ - {uid} - {horizon["name"]} - Erreur lors de la prédiction en classification : {e}", "error")
                return False

                
            if predicted_data_classification is not None and predicted_data_regression is not None:
                safe_shift_minutes = safe_shift_points_regression * 5
                #predicted_for_time = (original_time - timedelta(minutes=safe_shift_minutes)) + timedelta(hours=int(horizon["horizon_hour"]))

                prediction_result = {
                    "uid": uid,
                    "created_at": candlestick["created_at"],
                    "candlestick_id": candlestick["_id"],
                    "horizon": horizon["name"],
                    "prediction_hour": int(horizon["horizon_hour"]),
                    "predicted_for_time": predicted_for_time,
                    "current_price": current_price,
                    "regression": predicted_data_regression,
                    "classification": predicted_data_classification
                }
                mongo.store_prediction(prediction_result)
                mongo.update_candlestick_prediction(candlestick["_id"], uid, horizon["name"])

                logger.send_raw_data_log(prediction_result, metric="prediction_data")
            else:
                logger.send_log(f"❌ - {uid} - {horizon["name"]} - Prediction result incomplet : {prediction_result}", "error")
                return False
                
            logger.send_log(f"Prediction result = {prediction_result}", "info")
        except Exception as e:
            logger.send_log(f"❌ - {uid} - {horizon["name"]} - Erreur lors de la prédiction du modèle : {e}", "error")
            continue

    return True



def get_missing_predictions(uid):
    HORIZONS = models.get_horizon()

    for original_horizon in HORIZONS:
        missing_prediction = mongo.get_missing_predictions(uid, original_horizon["name"])

        #example_doc = missing_prediction[0]
        #logger.send_log(f"📄 - {uid} - Exemple de document:\n{json.dumps(example_doc, default=str, indent=2)}", "debug")
        #logger.send_log(f"⚠️ - {uid} - {original_horizon["name"]} - Aperçu doc : {missing_prediction}", "info")
        if len(missing_prediction) == 0 or not missing_prediction:
            logger.send_log(f"⚠️ - {uid} - {original_horizon["name"]} - Pas de prédiction manquante. Annulation", "info")
            continue
        
        try:
            compute_predictions(uid, missing_prediction, original_horizon)
        except Exception as e:
            logger.send_log(f"❌ - {uid} - {original_horizon["name"]} - Erreur lors de la prédiction du modèle : {e}", "error")
            continue
    
    return True



def get_prediction_to_update(uid):
    prediction_to_update = mongo.get_prediction_to_update(uid)

    if len(prediction_to_update) == 0 or not prediction_to_update:
        logger.send_log(f"⚠️ - {uid} - Pas de prédiction manquante. Annulation", "info")
        return False
    
    try:
        compute_predictions(uid, prediction_to_update)
    except Exception as e:
        logger.send_log(f"❌ Erreur lors de la prédiction du modèle : {e}", "error")
        return False
    
    return True




@app.route("/update_predictions", methods=["GET"])
def update_predictions_route():
    uid = request.args.get("uid")

    if not uid:
        return jsonify({"error": "Le paramètre 'uid' est requis"}), 400

    result = get_prediction_to_update(uid)

    if result:
        return jsonify({"status": "success", "message": f"Prédictions mises à jour pour {uid}"}), 200
    else:
        return jsonify({"status": "failed", "message": f"Aucune prédiction mise à jour pour {uid}"}), 200




def loop_predictions():
    logger.send_log(f"✅ - Service predict démarré.", "info")
    pid = os.getpid()
    logger.send_log(f"✅ - PID = {pid}", "info")

    #get_missing_predictions("ETHEUR")

    now = datetime.utcnow()
    minute = (now.minute // 5 + 1) * 5
    if minute >= 60:
        first_run = now.replace(hour=(now.hour + 1) % 24, minute=0, second=0, microsecond=0)
    else:
        first_run = now.replace(minute=minute, second=0, microsecond=0)

    wait_seconds = max(0, (first_run - now).total_seconds())
    logger.send_log(f"🕒 - Attente initiale jusqu'à {first_run.strftime('%Y-%m-%d %H:%M:%S UTC')} pour démarrer...", "info")
    time.sleep(wait_seconds)

    while True:
        now = datetime.utcnow()

        # Aller à la prochaine minute multiple de 5
        minute = (now.minute // 5 + 1) * 5
        if minute >= 60:
            next_run = now.replace(hour=(now.hour + 1) % 24, minute=0, second=0, microsecond=0)
        else:
            next_run = now.replace(minute=minute, second=0, microsecond=0)

        # Exécuter uniquement la détection + comblement des gaps
        logger.send_log(f"🔁 - Cycle lancé à {now.strftime('%Y-%m-%d %H:%M:%S UTC')}", "info")

        # Vérification du verrou d'exécution
        if execution_lock.acquire(blocking=False):
            try:
                # Get les paires actives
                trading_uids = mongo.get_trading_pairs()
                if not trading_uids:
                    logger.send_log(f"⚠️ - Aucune crypto à prédire (test ou trading) !", "warning")
                    time.sleep(300)  # Attente avant une nouvelle tentative
                    continue
                else:
                    logger.send_log(f"🔁 - Lancement des prédictions pour {len(trading_uids)} cryptos récupérées.", "info")

                max_workers = min(NB_THREADS, len(trading_uids))  # Limite dynamique
                max_workers=1

                #logger.send_log(f"🚀 Lancement des prédictions en parallèle (max_workers={max_workers})", "info")

                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = set()

                    for uid in trading_uids:
                        future = executor.submit(get_missing_predictions, uid)
                        futures.add(future)

                    for future in futures:
                        try:
                            future.result()
                        except Exception as e:
                            logger.send_log(f"❌ - Erreur lors d'une prédiction : {e}", "error")

                logger.send_log("✅ -  Toutes les prédictions sont terminées !", "info")
            finally:
                execution_lock.release()
        else:
            logger.send_log("❌ - Une exécution précédente est toujours en cours. Ce cycle est annulé.", "error")


        # Attente jusqu'à la prochaine minute multiple de 5
        sleep_time = max(0, (next_run - datetime.utcnow()).total_seconds())
        logger.send_log(f"🕒 - Attente de {sleep_time} secondes avant le prochain cycle...", "info")
        time.sleep(sleep_time)




if __name__ == "__main__":
    # 🔄 Démarrer l'entraînement dans un thread séparé
    training_thread = threading.Thread(target=loop_predictions, daemon=True)
    training_thread.start()

    # 🚀 Lancer l'API Flask
    app.run(port=5000, host="0.0.0.0")