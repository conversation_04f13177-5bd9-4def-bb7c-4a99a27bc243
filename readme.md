# Cryptobot

Ce projet est un bot de trading crypto automatisé qui utilise des indicateurs techniques et des modèles de machine learning pour prendre des décisions sur différentes périodes (court, moyen et long terme).

## Fonctionnalités principales



- Récupération des données brutes (candlesticks)
    Service = "fetch_candlesticks"
    Toutes les 5 mns
    Données récupérées depuis le endpoint https://api.binance.com/api/v3/klines
    Ecriture dans la collection "candlesticks"

- Calcul d'indicateurs techniques (indicators)
    Service = "compute_indicators"
    Toutes les 5 mns
    Calcul d'indicateurs techniques pour 3 horizons 
        short, 
        medium, 
        long

- Optimisation des modèles
- Entraînement des modèles avec les paramètres optimisés
- Prédiction multi-horizon
- Déploiement automatique sur Raspberry Pi

---
