#!/usr/bin/env python3
"""
sac_repair_features.py - Diagnostic et réparation des features pour tous les actifs
"""

import sys
import re
import time
import pathlib
from datetime import datetime, timedelta, timezone
import traceback
import pandas as pd
import numpy as np
from pymongo import UpdateOne

# Chemin local
ROOT = pathlib.Path(__file__).resolve().parent
for p in (ROOT, ROOT.parent):
    sys.path.insert(0, str(p))

from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# Logger
logger = GrafanaUtils(service="repair_features")

# Connexion MongoDB
mongo = MongoUtils(logger=logger)
mongo.connect(service="repair_features")

# Liste des actifs par défaut si aucun n'est trouvé
DEFAULT_ASSETS = [
    "BTCEUR", "ETHEUR", "XRPEUR", "BNBEUR", "ADAEUR", 
    "DOGEEUR", "SOLUSDT", "AVAXEUR", "DOTEUR", "MATICEUR"
]

def get_all_assets():
    """Récupère tous les actifs disponibles dans le système."""
    assets = set()
    
    # 1. Depuis candlesticks (source primaire)
    try:
        assets.update(mongo.db["candlesticks"].distinct("uid"))
        logger.send_log("candlesticks_assets", "info", extra_labels={"count": len(assets)})
    except Exception as e:
        logger.send_log("candlesticks_error", "warning", extra_labels={"error": str(e)})
    
    # 2. Depuis predictions
    try:
        assets.update(mongo.db["predictions"].distinct("uid"))
        logger.send_log("predictions_assets", "info", extra_labels={"count": len(assets)})
    except Exception as e:
        logger.send_log("predictions_error", "warning", extra_labels={"error": str(e)})
    
    # 3. Depuis forecast_features (existantes)
    try:
        assets.update(mongo.db["forecast_features"].distinct("uid"))
        logger.send_log("features_assets", "info", extra_labels={"count": len(assets)})
    except Exception as e:
        logger.send_log("features_error", "warning", extra_labels={"error": str(e)})
    
    # 4. Depuis les modèles existants
    model_dir = pathlib.Path("/home/<USER>/cryptobot/models")
    if model_dir.exists():
        model_assets = set()
        for model_path in model_dir.glob("sac_*.zip"):
            parts = model_path.stem.split("_")
            if len(parts) >= 2:
                model_assets.add(parts[1])
        assets.update(model_assets)
        logger.send_log("model_assets", "info", extra_labels={"count": len(model_assets)})
    
    # 5. Ajouter la liste par défaut si nécessaire
    if not assets:
        assets.update(DEFAULT_ASSETS)
        logger.send_log("using_default_assets", "warning", extra_labels={"count": len(assets)})
    
    # Filtrer les UIDs invalides
    valid_assets = [asset for asset in assets if re.match(r"^[A-Z]{2,6}(EUR|USD|USDT)$", asset)]
    
    logger.send_log("valid_assets", "info", extra_labels={
        "count": len(valid_assets),
        "assets": valid_assets[:15]  # Limiter à 15 pour éviter des logs trop grands
    })
    
    return sorted(valid_assets)

def diagnose_asset(asset):
    """Diagnostique les problèmes pour un actif spécifique."""
    results = {
        "asset": asset,
        "has_candles": False,
        "candle_count": 0,
        "has_predictions": False,
        "prediction_count": 0,
        "has_features": False,
        "feature_count": 0,
        "has_audit": False,
        "audit_count": 0,
        "latest_candle": None,
        "latest_prediction": None,
        "latest_feature": None,
        "latest_audit": None,
        "issues": []
    }
    
    # 1. Vérifier les bougies
    try:
        candle_count = mongo.db["candlesticks"].count_documents({"uid": asset})
        results["candle_count"] = candle_count
        results["has_candles"] = candle_count > 0
        
        if candle_count > 0:
            latest_candle = mongo.db["candlesticks"].find_one(
                {"uid": asset},
                sort=[("closeTime", -1)]
            )
            if latest_candle:
                results["latest_candle"] = latest_candle.get("closeTime")
        
        if candle_count < 100:
            results["issues"].append("insufficient_candles")
    except Exception as e:
        results["issues"].append(f"candle_error: {str(e)}")
    
    # 2. Vérifier les prédictions
    try:
        prediction_count = mongo.db["predictions"].count_documents({"uid": asset})
        results["prediction_count"] = prediction_count
        results["has_predictions"] = prediction_count > 0
        
        if prediction_count > 0:
            latest_prediction = mongo.db["predictions"].find_one(
                {"uid": asset},
                sort=[("created_at", -1)]
            )
            if latest_prediction:
                results["latest_prediction"] = latest_prediction.get("created_at")
        
        if prediction_count < 50:
            results["issues"].append("insufficient_predictions")
    except Exception as e:
        results["issues"].append(f"prediction_error: {str(e)}")
    
    # 3. Vérifier les features
    try:
        feature_count = mongo.db["forecast_features"].count_documents({"uid": asset})
        results["feature_count"] = feature_count
        results["has_features"] = feature_count > 0
        
        if feature_count > 0:
            latest_feature = mongo.db["forecast_features"].find_one(
                {"uid": asset},
                sort=[("created_at", -1)]
            )
            if latest_feature:
                results["latest_feature"] = latest_feature.get("created_at")
                results["latest_run_at"] = latest_feature.get("run_at")
        
        if feature_count < 10:
            results["issues"].append("insufficient_features")
    except Exception as e:
        results["issues"].append(f"feature_error: {str(e)}")
    
    # 4. Vérifier les audits
    try:
        audit_count = mongo.db["forecast_audit"].count_documents({"uid": asset})
        results["audit_count"] = audit_count
        results["has_audit"] = audit_count > 0
        
        if audit_count > 0:
            latest_audit = mongo.db["forecast_audit"].find_one(
                {"uid": asset},
                sort=[("created_at", -1)]
            )
            if latest_audit:
                results["latest_audit"] = latest_audit.get("created_at")
        
        if audit_count < 1:
            results["issues"].append("no_audit")
    except Exception as e:
        results["issues"].append(f"audit_error: {str(e)}")
    
    # 5. Vérifier la cohérence des dates
    now = datetime.now(timezone.utc)
    
    if results["latest_candle"] and (now - results["latest_candle"]).total_seconds() > 86400:
        results["issues"].append("outdated_candles")
    
    if results["latest_prediction"] and (now - results["latest_prediction"]).total_seconds() > 86400:
        results["issues"].append("outdated_predictions")
    
    if results["latest_feature"] and (now - results["latest_feature"]).total_seconds() > 86400:
        results["issues"].append("outdated_features")
    
    if results["latest_audit"] and (now - results["latest_audit"]).total_seconds() > 86400:
        results["issues"].append("outdated_audit")
    
    # 6. Vérifier les anomalies dans les features
    if results["has_features"]:
        try:
            # Vérifier les MAPE anormaux
            high_mape = mongo.db["forecast_features"].count_documents({
                "uid": asset,
                "MAPE": {"$gt": 1.0}  # MAPE > 100%
            })
            
            if high_mape > 0:
                results["issues"].append(f"high_mape_count: {high_mape}")
        except Exception as e:
            results["issues"].append(f"mape_check_error: {str(e)}")
    
    return results

def repair_features(asset, diagnosis):
    """Répare les features pour un actif spécifique."""
    if not diagnosis["has_candles"]:
        logger.send_log("repair_skipped_no_candles", "warning", extra_labels={"asset": asset})
        return False
    
    if not diagnosis["has_predictions"]:
        logger.send_log("repair_skipped_no_predictions", "warning", extra_labels={"asset": asset})
        return False
    
    # 1. Supprimer les features obsolètes ou anormales
    try:
        # Supprimer les features avec MAPE > 100%
        delete_result = mongo.db["forecast_features"].delete_many({
            "uid": asset,
            "MAPE": {"$gt": 1.0}
        })
        
        logger.send_log("deleted_anomalous_features", "info", extra_labels={
            "asset": asset,
            "count": delete_result.deleted_count
        })
        
        # Supprimer les features trop anciennes (plus de 30 jours)
        old_date = datetime.now(timezone.utc) - timedelta(days=30)
        delete_result = mongo.db["forecast_features"].delete_many({
            "uid": asset,
            "created_at": {"$lt": old_date}
        })
        
        logger.send_log("deleted_old_features", "info", extra_labels={
            "asset": asset,
            "count": delete_result.deleted_count
        })
    except Exception as e:
        logger.send_log("delete_features_error", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
    
    # 2. Forcer la régénération des features via audit_forecasts
    try:
        from audit_forecasts import AuditForecasts
        
        logger.send_log("regenerating_features", "info", extra_labels={"asset": asset})
        
        audit = AuditForecasts(asset, mongo)
        success = audit.run()
        
        if success:
            logger.send_log("features_regenerated", "info", extra_labels={"asset": asset})
            return True
        else:
            logger.send_log("features_regeneration_failed", "warning", extra_labels={"asset": asset})
            return False
    except Exception as e:
        logger.send_log("regeneration_error", "error", extra_labels={
            "asset": asset,
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        return False

def main():
    """Fonction principale."""
    assets = get_all_assets()
    
    logger.send_log("starting_repair", "info", extra_labels={"asset_count": len(assets)})
    
    results = {
        "diagnosed": 0,
        "repaired": 0,
        "failed": 0,
        "skipped": 0,
        "assets_with_issues": []
    }
    
    for asset in assets:
        try:
            # Diagnostiquer l'actif
            diagnosis = diagnose_asset(asset)
            results["diagnosed"] += 1
            
            # Journaliser le diagnostic
            logger.send_log("asset_diagnosis", "info", extra_labels={
                "asset": asset,
                "has_candles": diagnosis["has_candles"],
                "has_predictions": diagnosis["has_predictions"],
                "has_features": diagnosis["has_features"],
                "has_audit": diagnosis["has_audit"],
                "issues": diagnosis["issues"]
            })
            
            # Si des problèmes sont détectés, tenter de réparer
            if diagnosis["issues"]:
                results["assets_with_issues"].append({
                    "asset": asset,
                    "issues": diagnosis["issues"]
                })
                
                # Réparer si nécessaire
                if "insufficient_features" in diagnosis["issues"] or \
                   "outdated_features" in diagnosis["issues"] or \
                   "high_mape_count" in diagnosis["issues"] or \
                   "no_audit" in diagnosis["issues"]:
                    
                    success = repair_features(asset, diagnosis)
                    
                    if success:
                        results["repaired"] += 1
                    else:
                        results["failed"] += 1
                else:
                    logger.send_log("repair_not_needed", "info", extra_labels={"asset": asset})
                    results["skipped"] += 1
            else:
                logger.send_log("asset_healthy", "info", extra_labels={"asset": asset})
                results["skipped"] += 1
        except Exception as e:
            logger.send_log("asset_processing_error", "error", extra_labels={
                "asset": asset,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            results["failed"] += 1
    
    # Résumé final
    logger.send_log("repair_summary", "info", extra_labels={
        "diagnosed": results["diagnosed"],
        "repaired": results["repaired"],
        "failed": results["failed"],
        "skipped": results["skipped"],
        "assets_with_issues_count": len(results["assets_with_issues"])
    })
    
    print(f"Diagnostic terminé pour {results['diagnosed']} actifs")
    print(f"✅ Réparés: {results['repaired']}")
    print(f"❌ Échecs: {results['failed']}")
    print(f"⏭️ Ignorés: {results['skipped']}")
    
    if results["assets_with_issues"]:
        print("\nActifs avec problèmes:")
        for item in results["assets_with_issues"]:
            print(f"- {item['asset']}: {', '.join(item['issues'])}")

if __name__ == "__main__":
    main()