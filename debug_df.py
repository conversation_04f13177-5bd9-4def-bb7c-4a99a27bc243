import pandas as pd
import numpy as np
import optuna
from datetime import datetime, timedelta, timezone
from sklearn.ensemble import RandomForestRegressor
from sklearn.inspection import permutation_importance
from sklearn.preprocessing import StandardScaler
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from common.indicators_utils import IndicatorsUtils
from common.models_utils import ModelsUtils


SERVICE = "debug_df"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
indicator = IndicatorsUtils(service=SERVICE)
models = ModelsUtils(service=SERVICE)


INDICATOR_CONFIG_LARGE = {
    "RSI": {"min_factor": 2, "max_factor": 8},
    "RSI_delta_medium": {"min_factor": 10, "max_factor": 25},

    "StochRSI_period": {"min_factor": 4, "max_factor": 14},      # période de base
    "StochRSI_fastk": {"min_factor": 2, "max_factor": 8},        # souvent 3
    "StochRSI_fastd": {"min_factor": 2, "max_factor": 8},        # souvent 3

    "TEMA": {"min_factor": 3, "max_factor": 10},                 # plus lisse qu’EMA, période un peu plus longue

    "CMO": {"min_factor": 2, "max_factor": 8},
    "Sharpe_local": {"min_factor": 2, "max_factor": 8},

    "MACD_fast": {"min_factor": 2, "max_factor": 8},
    "MACD_slow": {"min_factor": 2, "max_factor": 8},
    "MACD_signal": {"min_factor": 2, "max_factor": 8},
    "MACD_delta_medium": {"min_factor": 10, "max_factor": 20},

    "EMA": {"min_factor": 2, "max_factor": 8},

    "ADX": {"min_factor": 2, "max_factor": 8},
    "ATR": {"min_factor": 2, "max_factor": 8},
    "CMF": {"min_factor": 2, "max_factor": 8},
    "CCI": {"min_factor": 2, "max_factor": 8},
    "MFI": {"min_factor": 2, "max_factor": 8},

    "VWAP": {"min_factor": 3, "max_factor": 10},                 # période de roulage du VWAP

    "Z_Score": {"min_factor": 3, "max_factor": 10},              # période pour le Z-Score

    "Momentum": {"min_factor": 2, "max_factor": 8},
    "Momentum_delta_medium": {"min_factor": 10, "max_factor": 25}
}


INDICATOR_GROUPS = {
    "MACD": {
        "features": ["MACD", "MACD_medium_delta", "MACD_medium_pct", "MACD_medium_mean"],
        "params": ["MACD_fast", "MACD_slow", "MACD_signal", "MACD_delta_medium"]
    },
    "RSI": {
        "features": ["RSI", "RSI_medium_delta", "RSI_medium_pct", "RSI_medium_mean"],
        "params": ["RSI", "RSI_delta_medium"]
    },
    "CMO": {
        "features": ["CMO"],
        "params": ["CMO"]
    },
    "Sharpe": {
        "features": ["Sharpe_local"],
        "params": ["Sharpe_local"]
    },
    "Momentum": {
        "features": ["Momentum", "Momentum_medium_delta", "Momentum_medium_pct", "Momentum_medium_mean"],
        "params": ["Momentum", "Momentum_delta_medium"]
    },
    "CCI": {
        "features": ["CCI"],
        "params": ["CCI"]
    },
    "CMF": {
        "features": ["CMF"],
        "params": ["CMF"]
    },
    "EMA": {
        "features": ["EMA"],
        "params": ["EMA"]
    },
    "BBANDS": {
        "features": ["Percent_B"],
        "params": ["BBANDS"]
    },
    "ATR": {
        "features": ["ATR"],
        "params": ["ATR"]
    },
    "ADX": {
        "features": ["ADX"],
        "params": ["ADX"]
    },
    "MFI": {
        "features": ["MFI"],
        "params": ["MFI"]
    },
    "StochRSI": {
        "features": ["StochRSI_K", "StochRSI_D", "StochRSI_CrossUp"],
        "params": ["StochRSI_period", "StochRSI_fastk", "StochRSI_fastd"]
    },
    "TEMA": {
        "features": ["TEMA"],
        "params": ["TEMA"]
    },
    "ZScore": {
        "features": ["Z_Score", "Z_Score_abs"],
        "params": ["Z_Score"]
    },
    "VWAP": {
        "features": ["VWAP", "VWAP_Diff"],
        "params": ["VWAP"]
    }
}


def suggest_indicator_params(trial, horizon):
    params = {}

    for name, cfg in INDICATOR_CONFIG_LARGE.items():
        low = int(horizon * cfg["min_factor"])
        high = int(horizon * cfg["max_factor"])
        # On ne gère pas encore les contraintes, on stocke simplement les bornes
        params[name] = trial.suggest_int(name, low, high)

    # ✅ Contraintes logiques (ex : MACD_fast < MACD_slow)
    if params["MACD_fast"] >= params["MACD_slow"]:
        fast = min(params["MACD_fast"], params["MACD_slow"] - 1)
        slow = max(params["MACD_fast"] + 1, params["MACD_slow"])
        params["MACD_fast"] = fast
        params["MACD_slow"] = slow

    return params


def prepare_df_individual_optim(group_name, candlesticks, indicator_params, uid, model, selected_features):
    # Vérification logique spécifique au groupe
    if group_name == "MACD":
        if indicator_params["MACD_fast"] >= indicator_params["MACD_slow"]:
            raise optuna.TrialPruned()

    df = None
    try:
        # Génère le DataFrame complet avec tous les indicateurs
        df = indicator.generate_training_sample(
            candlesticks, indicator_params, uid, model["shift_value"],
            model["horizon_hour"], "max", model['safe_shift_points']
        )

        # Sélectionne uniquement les features utiles
        features_to_keep = selected_features + ['log_return']
        df_reduced = models.keep_only_features(df, features_to_keep)

        # Libération mémoire de df complet
        del df

        return df_reduced

    except Exception as e:
        print(f"❌ - {uid} - Erreur préparation features : {e}")
        raise optuna.TrialPruned()


def optimize_indicator_group_from_config(
    group_name,
    group_config,
    global_config,
    candlesticks,
    uid,
    horizon_hour,
    shift_value,
    safe_shift_points=1,
    n_trials=50,
    model=None
):


    features = group_config["features"]
    param_keys = group_config["params"]

    def objective(trial):
        try:
            # Générer les paramètres dynamiquement selon INDICATOR_CONFIG_LARGE
            params = {}
            for param_key in param_keys:
                bounds = global_config[param_key]
                factor = trial.suggest_int(
                    param_key,
                    horizon_hour * bounds["min_factor"],
                    horizon_hour * bounds["max_factor"]
                )
                params[param_key] = factor

            indicator_params = suggest_indicator_params(trial, horizon_hour)
            trial.set_user_attr("indicator_params", indicator_params)


            # Génération du dataset avec uniquement les paramètres de ce groupe
            df = prepare_df_individual_optim(
                group_name=group_name,
                candlesticks=candlesticks,
                indicator_params=indicator_params,
                uid=uid,
                model=model,
                selected_features=features
            )

            if len(df) < 100:
                print(f"⚠️ - Df trop court")
                raise optuna.exceptions.TrialPruned()

            # Choix de la feature la plus corrélée dans le groupe
            best_corr = 0
            for feature in features:
                if feature not in df.columns:
                    continue
                corr = df["log_return"].corr(df[feature])
                if not pd.isna(corr):
                    best_corr = max(best_corr, abs(corr))

            return best_corr

        except Exception as e:
            print(f"⚠️ - Erreur lors du trial : {e}")
            raise optuna.exceptions.TrialPruned()

    study = optuna.create_study(direction="maximize")
    study.optimize(objective, n_trials=n_trials)

    print(f"✅ {group_name} → Corrélation max : {study.best_value:.4f} avec paramètres : {study.best_params}")
    return study.best_params, study.best_value



def optimize_indicator_from_config(
    indicator_name,
    config,
    candlesticks,
    uid,
    horizon_hour,
    shift_value,
    safe_shift_points=1,
    n_trials=100
):
    import optuna

    def objective(trial):
        try:
            # Génération des paramètres à tester
            indicator_params = suggest_indicator_params(trial, horizon_hour)
            trial.set_user_attr("indicator_params", indicator_params)

            # On injecte tous les paramètres de ce groupe dans un seul bloc
            df = indicator.generate_training_sample(
                candlesticks,
                indicator_params,
                uid=uid,
                shift_value=shift_value,
                horizon_hour=horizon_hour,
                aggregation_func="max",
                safe_shift_points=safe_shift_points,
            )
            df = df.dropna()

            if len(df) < 100:
                raise optuna.exceptions.TrialPruned()

            # Corrélation brute (absolue) à maximiser
            corr = df["log_return"].corr(df[indicator_name])
            return abs(corr)

        except Exception:

            raise optuna.exceptions.TrialPruned()

    study = optuna.create_study(direction="maximize")
    study.optimize(objective, n_trials=n_trials)

    print(f"✅ {indicator_name} → Corrélation max : {study.best_value:.4f} avec paramètres : {study.best_params}")
    return study.best_params, study.best_value



def check_temporal_alignment(df, target_col="log_return", max_lag=10):
    from scipy.stats import pearsonr

    results = []
    for col in df.columns:
        if col == target_col or not pd.api.types.is_numeric_dtype(df[col]):
            continue
        correlations = {}
        for lag in range(-max_lag, max_lag + 1):
            shifted_feature = df[col].shift(lag)
            corr = df[target_col].corr(shifted_feature)
            correlations[f"lag_{lag}"] = round(corr, 4) if not pd.isna(corr) else None
        best_lag = max(correlations, key=lambda k: abs(correlations[k]) if correlations[k] else 0)
        results.append({
            "Indicateur": col,
            "Meilleur Décalage": best_lag,
            "Corrélation max": correlations[best_lag],
            "Lag recommandé": "✔️" if best_lag.startswith("lag_0") or best_lag.startswith("lag_-") else "❌ fuite"
        })
    return pd.DataFrame(results)


def generate_indicator_report(df, target_column='log_return', min_corr=0.01):
    indicators = [
        col for col in df.columns
        if col != target_column and pd.api.types.is_numeric_dtype(df[col])
    ]
    results = []

    df = df.copy().dropna()

    # Standardisation
    X = df[indicators]
    y = df[target_column]

    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # Modèle simple
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    model.fit(X_scaled, y)

    # Importance permutation
    perm = permutation_importance(model, X_scaled, y, n_repeats=10, random_state=42)

    for idx, col in enumerate(indicators):
        values = df[col]
        corr = np.corrcoef(values, y)[0, 1]
        importance = perm.importances_mean[idx]
        stats = values.describe()
        is_constant = values.nunique() <= 1
        has_nan = values.isna().sum() > 0

        results.append({
            "Indicateur": col,
            "Corrélation": round(corr, 4),
            "Importance": round(importance, 6),
            "Min": round(stats["min"], 4),
            "Max": round(stats["max"], 4),
            "Moyenne": round(stats["mean"], 4),
            "Écart-type": round(stats["std"], 4),
            "Constante": is_constant,
            "NaN présent": has_nan
        })

    df_result = pd.DataFrame(results)
    df_result = df_result.sort_values(by="Importance", ascending=False)
    return df_result




def main():
    print(f"✅ Debug log")
    try: 
        uid = "BTCEUR"
        model = {
            "name": '3h',
            "horizon_hour": 3,
            "shift_value": 36,
            "safe_shift_points": 1
        }

        base_log = f"{uid} - {model['name']}"
        until_date = datetime.utcnow()

        try :
            candlesticks = mongo.get_candlesticks(uid, until_date)
            if len(candlesticks) == 0:
                print(f"⚠️ - {base_log} - Aucun candlesticks trouvés")
                return None
        except Exception as e:
            print(f"⚠️ - {base_log} - Erreur lors de get_candelsticks : {e}")
            return None
        
        previous_best_indicator_params = mongo.get_best_indicator_params(model['name'], uid, model['horizon_hour'])
        try: 
            df = indicator.generate_training_sample(
                candlesticks, 
                previous_best_indicator_params['indicator_params'], 
                uid, 
                model["shift_value"],
                model["horizon_hour"], 
                "max", 
                model['safe_shift_points']
            )
        except Exception as e:
            print(f"⚠️ - {base_log} - Erreur lors de la génération du df : {e}")
            return None
        
        try: 
            report = generate_indicator_report(df, target_column="log_return")
        except Exception as e:
            print(f"⚠️ - {base_log} - Erreur lors de la génération du rapport : {e}")
            return None
        #from ace_tools import display_dataframe_to_user
        #display_dataframe_to_user("Rapport des Indicateurs", report)
        report.to_html("/home/<USER>/rapport_indicateurs_avant_optim.html")
        

        best_params_optim = previous_best_indicator_params['indicator_params']

        for group_name, group_config in INDICATOR_GROUPS.items():
            try:
                best_params, best_score = optimize_indicator_group_from_config(
                    group_name=group_name,
                    group_config=group_config,
                    global_config=INDICATOR_CONFIG_LARGE,
                    candlesticks=candlesticks,
                    uid=uid,
                    horizon_hour=model["horizon_hour"],
                    shift_value=model["shift_value"],
                    safe_shift_points=model["safe_shift_points"],
                    n_trials=100,
                    model=model
                )

                for f in group_config['features']:
                    best_params_optim[f] = best_params[f]
            except Exception as e:
                print(f"⚠️ Erreur optimisation {group_name} : {e}")

        print(f"⚠️ Aperçu best_params {best_params_optim}")


        try: 
            df = indicator.generate_training_sample(
                candlesticks, best_params_optim, uid, model["shift_value"],
                model["horizon_hour"], "max", model['safe_shift_points']
            )
        except Exception as e:
            print(f"⚠️ - {base_log} - Erreur lors de la génération du df : {e}")
            return None

        try: 
            report = generate_indicator_report(df, target_column="log_return")
        except Exception as e:
            print(f"⚠️ - {base_log} - Erreur lors de la génération du rapport : {e}")
            return None
        #from ace_tools import display_dataframe_to_user
        #display_dataframe_to_user("Rapport des Indicateurs", report)
        report.to_html("/home/<USER>/rapport_indicateurs_après_optim.html")


        alignment_report = check_temporal_alignment(df, target_col="log_return")
        alignment_report.to_html("/home/<USER>/rapport_alignement.html")

        df.to_html("df.html")


    except Exception as e:
            print(f"⚠️ - {base_log} - Erreur lors du main : {e}")
            return None
    

main()
    
