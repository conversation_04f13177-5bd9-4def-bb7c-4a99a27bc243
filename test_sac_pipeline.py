#!/usr/bin/env python3
"""
Script de test pour sac_pipeline
"""

import os
import sys
import datetime
import traceback
from pathlib import Path

# Ajouter le répertoire courant au chemin Python
sys.path.append(os.getcwd())

# C<PERSON>er le répertoire de logs s'il n'existe pas
Path("/home/<USER>/cryptobot/logs").mkdir(exist_ok=True)

# Fichier de log pour le débogage
DEBUG_LOG = "/home/<USER>/cryptobot/logs/test_pipeline.log"

def log_message(msg):
    """Écrire un message dans le fichier de log et sur la console."""
    timestamp = datetime.datetime.now().isoformat()
    full_msg = f"[{timestamp}] {msg}"
    print(full_msg)
    with open(DEBUG_LOG, "a") as f:
        f.write(full_msg + "\n")

try:
    log_message("DÉBUT DU TEST DE SAC_PIPELINE")
    
    # Importer les modules nécessaires
    log_message("Importation des modules...")
    from common.mongo_utils import MongoUtils
    from common.grafana_utils import GrafanaUtils
    
    # Initialiser la connexion MongoDB
    log_message("Connexion à MongoDB...")
    mongo = MongoUtils()
    mongo.connect(service="test_pipeline")
    
    # Importer les fonctions de sac_pipeline
    log_message("Importation des fonctions de sac_pipeline...")
    from sac_pipeline import _run_export, _check_missing_models, _check_missing_features, _check_missing_jsons, _check_db_health, _cycle
    
    # Tester _run_export
    log_message("Test de _run_export...")
    try:
        result = _run_export()
        log_message(f"Résultat de _run_export: {result}")
    except Exception as e:
        log_message(f"Erreur lors de _run_export: {str(e)}")
        log_message(traceback.format_exc())
    
    # Tester _check_missing_models
    log_message("Test de _check_missing_models...")
    try:
        result = _check_missing_models()
        log_message(f"Résultat de _check_missing_models: {result}")
    except Exception as e:
        log_message(f"Erreur lors de _check_missing_models: {str(e)}")
        log_message(traceback.format_exc())
    
    # Tester _check_missing_features
    log_message("Test de _check_missing_features...")
    try:
        result = _check_missing_features(mongo)
        log_message(f"Résultat de _check_missing_features: {result}")
    except Exception as e:
        log_message(f"Erreur lors de _check_missing_features: {str(e)}")
        log_message(traceback.format_exc())
    
    # Tester _check_missing_jsons
    log_message("Test de _check_missing_jsons...")
    try:
        result = _check_missing_jsons()
        log_message(f"Résultat de _check_missing_jsons: {result}")
    except Exception as e:
        log_message(f"Erreur lors de _check_missing_jsons: {str(e)}")
        log_message(traceback.format_exc())
    
    # Tester _check_db_health
    log_message("Test de _check_db_health...")
    try:
        result = _check_db_health(mongo)
        log_message(f"Résultat de _check_db_health: {result}")
    except Exception as e:
        log_message(f"Erreur lors de _check_db_health: {str(e)}")
        log_message(traceback.format_exc())
    
    # Tester _cycle
    log_message("Test de _cycle...")
    try:
        _cycle(mongo)
        log_message("_cycle exécuté avec succès")
    except Exception as e:
        log_message(f"Erreur lors de _cycle: {str(e)}")
        log_message(traceback.format_exc())
    
    log_message("TEST TERMINÉ")
    
except Exception as e:
    log_message(f"ERREUR GLOBALE: {str(e)}")
    log_message(traceback.format_exc())
    sys.exit(1)