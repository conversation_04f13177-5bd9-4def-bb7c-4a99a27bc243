import pandas as pd
import numpy as np
from pymongo import MongoClient
from datetime import datetime, timedelta
from common.grafana_utils import GrafanaUtils
import os
from dotenv import load_dotenv
from pathlib import Path

env_path = "/home/<USER>/cryptobot/.env"
load_dotenv(dotenv_path=env_path)
SERVICE = "debug_models"
logger = GrafanaUtils(service=SERVICE)

MONGO_USER = os.getenv("MONGO_USER")
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD")
MONGO_HOST = os.getenv("MONGO_HOST")
MONGO_DB = os.getenv("MONGO_DB")
required_env = [MONGO_USER, MONGO_PASSWORD, MONGO_HOST, MONGO_DB]
if not all(required_env):
    raise ValueError("❌ Une ou plusieurs variables d'environnement Mongo sont manquantes.")
MONGO_URI = f"mongodb://{MONGO_USER}:{MONGO_PASSWORD}@{MONGO_HOST}:27017/{MONGO_DB}?authSource={MONGO_DB}"

def connect_to_mongo(uri=MONGO_URI, db_name=MONGO_DB, service=SERVICE):
    """Initialise une nouvelle connexion Mongo avec un nom de service personnalisé"""
    client = MongoClient(uri)
    return client[db_name]

def load_predictions(db, symbol, horizon):
    collection = db["predictions"]
    cursor = collection.find({
        "uid": symbol,
        "horizon": horizon,
        "regression.future_price_estimated": {"$exists": True}
    })
    data = list(cursor)
    if not data:
        logger.send_log(f"[❌] Aucune prédiction trouvée pour {symbol} - {horizon}")
        return None
    df = pd.DataFrame(data)
    df["predicted_for_time"] = pd.to_datetime(df["predicted_for_time"])
    df["future_price_estimated"] = df["regression"].apply(lambda x: x.get("future_price_estimated") if isinstance(x, dict) else None)
    df = df[["predicted_for_time", "future_price_estimated"]].dropna()
    return df

def load_real_prices(db, symbol):
    collection = db["candlesticks"]
    cursor = collection.find({"uid": symbol})
    data = list(cursor)
    if not data:
        logger.send_log(f"[❌] Aucun prix trouvé pour {symbol}")
        return None
    df = pd.DataFrame(data)
    df["timestamp"] = pd.to_datetime(df["created_at"])
    df = df.set_index("timestamp").sort_index()
    df["price"] = df["lastPrice"]
    return df[["price"]]

def log_lag_analysis_from_predictions(df_predictions, df_prices, max_lag_minutes=180, interval=5):
    results = []
    df_prices = df_prices.copy()
    df_predictions = df_predictions.copy()
    df_predictions.set_index("predicted_for_time", inplace=True)

    for lag in range(0, max_lag_minutes + interval, interval):
        shifted = df_predictions.copy()
        shifted.index = shifted.index - timedelta(minutes=lag)
        merged = pd.merge_asof(shifted.sort_index(), df_prices.sort_index(), left_index=True, right_index=True, direction="backward")

        if len(merged) > 10:
            corr = np.corrcoef(merged["future_price_estimated"], merged["price"])[0, 1]
            results.append((lag, corr))

    result_df = pd.DataFrame(results, columns=["lag_minutes", "correlation"])
    best_row = result_df.iloc[result_df["correlation"].abs().argmax()]
    return best_row["lag_minutes"], best_row["correlation"], result_df

if __name__ == "__main__":
    db = connect_to_mongo()
    symbol = "BTCEUR"   # <-- à personnaliser
    horizon = "3h"       # <-- à personnaliser

    df_preds = load_predictions(db, symbol, horizon)
    df_prices = load_real_prices(db, symbol)

    if df_preds is not None and df_prices is not None:
        best_lag, best_corr, df_lags = log_lag_analysis_from_predictions(df_preds, df_prices)
        logger.send_log(f"\n🔎 Analyse pour {symbol} ({horizon})")
        logger.send_log(f"[⏱️] Lag avec corrélation max : {best_lag} minutes")
        logger.send_log(f"[📈] Corrélation max         : {best_corr:.4f}")
        logger.send_log("\n📋 Détails :")
        logger.send_log(df_lags.sort_values("correlation", ascending=False).head(5))
    else:
        logger.send_log("[❌] Données manquantes pour effectuer l'analyse")
