import time
import os
import sys
import joblib
import gzip
import numpy as np
import pandas as pd
from tensorflow.keras.models import load_model
from datetime import datetime, timedelta, timezone
from concurrent.futures import ProcessPoolExecutor, as_completed
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from common.indicators_utils import IndicatorsUtils
from common.models_utils import ModelsUtils


MODEL_PATH_TEMPLATE = "/home/<USER>/cryptobot/models/{uid}_{horizon}_regression.keras"
SCALER_X_TEMPLATE = "/home/<USER>/cryptobot/models/{uid}_{horizon}_scaler_X.gz"
SCALER_Y_TEMPLATE = "/home/<USER>/cryptobot/models/{uid}_{horizon}_scaler_y.gz"
MODEL_DIR = "/home/<USER>/cryptobot/models/"
SCALING_Y_FACTOR = 1


SERVICE = "predict"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
indicator = IndicatorsUtils(service=SERVICE)
models = ModelsUtils(service=SERVICE)




# 🔧 Initialisation dans chaque process enfant
def init_worker():
    global logger, mongo, models, indicator
    logger = GrafanaUtils(service=SERVICE)
    mongo = MongoUtils()
    mongo.connect(service=SERVICE)
    models = ModelsUtils(service=SERVICE)
    indicator = IndicatorsUtils(service=SERVICE)




def process_crypto(uid, horizon):
    """
    Effectue une prédiction pour une crypto donnée et un horizon spécifique.
    """
    base_log = f"{uid} - {horizon['name']}"
    safe_shift_points = 0

    # 1. Chargement des hyperparamètres et des paramètres d’indicateurs
    hyperparams = mongo.get_best_hyperparams(horizon["name"], uid, horizon["horizon_hour"])
    if not hyperparams:
        logger.send_log(f"⚠️ Aucun hyperparamètre trouvé pour {base_log}", "warning")
        return

    indicator_params = hyperparams['indicator_params']
    if not indicator_params:
        logger.send_log(f"⚠️ Aucun paramètre d’indicateur trouvé pour {base_log}", "warning")
        return

    # 2. Chargement des candlesticks récents
    try:
        until_date = datetime.utcnow()
        candlesticks = mongo.get_candlesticks(uid, until_date)
        if not candlesticks:
            logger.send_log(f"⚠️ - {base_log} - Aucun candlestick trouvé", "error")
            return
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de get_candlesticks : {e}", "error")
        return

    # 3. Préparation des features pour la prédiction
    sequence_length = hyperparams['best_params'].get('n_steps', 64)
    try:
        df = indicator.generate_prediction_sample(
            raw_data=candlesticks,
            indicator_periods=indicator_params,
            uid=uid,
            sequence_length=sequence_length,
            safe_shift_points=safe_shift_points
        )
    except Exception as e:
        logger.send_log(f"❌ Erreur préparation des features pour {base_log} : {e}", "error")
        return

    if df is None or df.empty:
        logger.send_log(f"⚠️ DataFrame vide pour {base_log}, prédiction annulée", "warning")
        return

    # 4. Chargement du modèle
    model_path = MODEL_PATH_TEMPLATE.format(uid=uid, horizon=horizon["name"])
    if not os.path.exists(model_path):
        logger.send_log(f"⚠️ Modèle non trouvé : {model_path}", "warning")
        return
    model = load_model(model_path, compile=False)

    # 5. Chargement des scalers
    ## X
    scaler_X_path = SCALER_X_TEMPLATE.format(uid=uid, horizon=horizon["name"])
    if not os.path.exists(scaler_X_path):
        logger.send_log(f"⚠️ Scaler X manquant pour {base_log}", "warning")
        return
    with gzip.open(scaler_X_path, "rb") as f:
        scaler_X = joblib.load(f)

    ## y
    scaler_y_path = SCALER_Y_TEMPLATE.format(uid=uid, horizon=horizon["name"])
    if not os.path.exists(scaler_y_path):
        logger.send_log(f"⚠️ Scaler y manquant pour {base_log}", "warning")
        return
    with gzip.open(scaler_y_path, "rb") as f:
        scaler_y = joblib.load(f)

    # 6. Préparation de la séquence à prédire
    sequence_length = hyperparams['best_params'].get('n_steps', 64)
    latest_seq = df.tail(sequence_length)
    if len(latest_seq) < sequence_length:
        logger.send_log(f"⚠️ Pas assez de points ({len(latest_seq)}/{sequence_length}) pour {base_log}", "warning")
        return

    X_scaled = scaler_X.transform(latest_seq)
    X_scaled = X_scaled.reshape(1, sequence_length, X_scaled.shape[1])

    # 7. Prédiction & inverse scaling
    y_pred_s = model.predict(X_scaled, verbose=0).flatten()          # prédiction en échelle standardisée
    y_pred   = scaler_y.inverse_transform(y_pred_s.reshape(-1,1)).flatten()  # log-return réel

    logger.send_log(f"{base_log} - raw pred (scaled) = {y_pred_s[0]:.4f}", "info")
    logger.send_log(f"{base_log} - pred log_return = {y_pred[0]:.4f}", "info")

    # 8. Conversion log_return -> prix futur estimé
    price_series = pd.Series([c["lastPrice"] for c in candlesticks])
    price_now    = price_series.iloc[-(safe_shift_points+1)]
    predicted_price = price_now * np.exp(y_pred[0])

    # 9. Stockage
    predicted_for_time = (
        candlesticks[-1]["created_at"]
        .replace(tzinfo=timezone.utc)
        + timedelta(hours=horizon["horizon_hour"])
    )
    result = {
        "uid": uid,
        "horizon": horizon["name"],
        "predicted_log_return": float(y_pred[0]),
        "future_price_estimated": float(predicted_price),
        "predicted_for_time": predicted_for_time,
        "current_price": float(price_now),
        "timestamp": datetime.utcnow().isoformat()
    }
    mongo.store_prediction(result)
    logger.send_log(f"✅ Prédiction stockée pour {base_log}", "info")
    logger.send_raw_data_log(result, metric="prediction_data")


def ask_prediction(uid):
    horizons = mongo.get_all_horizons()
    for horizon in horizons:
        process_crypto(uid, horizon)

    print('prediction done', flush=True)

    return True


# 👷 Worker d'entraînement d’un modèle pour une crypto et un horizon
def predict_worker(uid, horizon):
    init_worker()
    base_log = f"{uid} - {horizon['name']}"
    try:
        process_crypto(uid, horizon)
        #logger.send_log(f"✅ Entraînement terminé : {base_log}", "info")
        return f"✅ Prédictions terminées : {base_log}"
    except Exception as e:
        #logger.send_log(f"❌ Erreur entraînement {base_log} : {e}", "error")
        return f"❌ Erreur prédiction {base_log} : {e}"
    


# ⚙️ Lancement des prédictions pour toutes les cryptos et horizons
def predict_for_all(trading_uids, horizons, max_workers=8):
    logger.send_log("🚀 Lancement des prédictions", "info")
    logger.send_log(f"📌 {len(trading_uids)} cryptos × {len(horizons)} prédictions à réaliser...", "info")

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(predict_worker, uid, horizon) for uid in trading_uids for horizon in horizons]

        for future in as_completed(futures):
            msg = future.result()
            logger.send_log(msg, "info" if "✅" in msg else "error")

    logger.send_log("🏁 Toutes les prédictions ont été réalisées.", "info")
    return True




# 🔁 Exécution d’un cycle complet de prédiction (séquentiel côté master)
def run_prediction_cycle():
    trading_uids = mongo.get_trading_pairs()
    if not trading_uids:
        logger.send_log("⚠️ Aucune crypto à entraîner !", "warning")
        return False

    horizons = mongo.get_all_horizons()
    if not horizons:
        logger.send_log("⚠️ Aucun horizon trouvé en DB !", "warning")
        return False

    try:
        predict_for_all(trading_uids, horizons, max_workers=8)
    except Exception as e:
        logger.send_log(f"❌ Erreur durant le cycle de prédictions : {e}", "error")

    logger.send_log("✅ Cycle de prédictions terminé.", "info")
    return True




# 🔁 Boucle principale qui répète les prédictions
def main_loop():
    INTERVAL = 300  # toutes les 5 minutes en secondes
    error_count = 0

    while True:
        start_time = time.time()
        
        try:
            logger.send_log("🔁 Nouveau cycle de prédictions des modèles...", "info")
            success = run_prediction_cycle()

            if success:
                logger.send_log("✅ Prédictions effectuées avec succès.", "info")
                error_count = 0
            else:
                logger.send_log("⚠️ Aucune prédiction effectuée.", "warning")

        except Exception as e:
            error_count += 1
            logger.send_log(f"❌ Erreur dans `main()` : {e} (tentative {error_count})", "error")
            if error_count >= 5:
                logger.send_log("🚨 Trop d'erreurs consécutives. Arrêt du script.", "critical")
                break

        elapsed_time = time.time() - start_time
        sleep_time = max(0, INTERVAL - elapsed_time)
        logger.send_log(f"⏳ Attente de {sleep_time:.1f} secondes avant la prochaine prédiction...", "info")
        time.sleep(sleep_time)

    logger.send_log("👋 Script de prédiction arrêté proprement.", "info")


# 🧠 Point d'entrée principal
if __name__ == "__main__":
    if len(sys.argv) > 1:
        uid = sys.argv[1]
        ask_prediction(uid) 
    else:
        main_loop()

