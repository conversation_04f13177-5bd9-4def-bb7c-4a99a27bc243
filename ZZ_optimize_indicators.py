from flask import Flask, request, jsonify
import pandas as pd
import numpy as np
import xgboost as xgb
import talib
import threading
import time
import requests
import math
import optuna
import shap
import os
import csv
from datetime import datetime, timedelta, timezone
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from common.indicators_utils import IndicatorsUtils
from common.models_utils import ModelsUtils


SERVICE = "optimize_indicators"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
indicator = IndicatorsUtils(service=SERVICE)
models = ModelsUtils(service=SERVICE)

NB_THREADS = 5
GLOBAL_FEATURE_TRIAL = 150
INDIVIDUAL_FEATURE_TRIAL = 50


app = Flask(__name__)


INDICATOR_CONFIG_LARGE = {
    "RSI": {"min_factor": 2, "max_factor": 8},
    "RSI_delta_medium": {"min_factor": 10, "max_factor": 24},

    "StochRSI_period": {"min_factor": 4, "max_factor": 14},      # période de base
    "StochRSI_fastk": {"min_factor": 2, "max_factor": 6},        # souvent 3
    "StochRSI_fastd": {"min_factor": 2, "max_factor": 6},        # souvent 3

    "TEMA": {"min_factor": 3, "max_factor": 10},                 # plus lisse qu’EMA, période un peu plus longue

    "CMO": {"min_factor": 2, "max_factor": 6},
    "Sharpe_local": {"min_factor": 2, "max_factor": 6},

    "MACD_fast": {"min_factor": 2, "max_factor": 4},
    "MACD_slow": {"min_factor": 2, "max_factor": 8},
    "MACD_signal": {"min_factor": 3, "max_factor": 6},
    "MACD_delta_medium": {"min_factor": 10, "max_factor": 20},

    "EMA": {"min_factor": 2, "max_factor": 6},

    "ADX": {"min_factor": 2, "max_factor": 6},
    "ATR": {"min_factor": 2, "max_factor": 6},
    "CMF": {"min_factor": 2, "max_factor": 6},
    "CCI": {"min_factor": 2, "max_factor": 6},
    "MFI": {"min_factor": 2, "max_factor": 6},

    "VWAP": {"min_factor": 3, "max_factor": 10},                 # période de roulage du VWAP

    "Z_Score": {"min_factor": 3, "max_factor": 10},              # période pour le Z-Score

    "Momentum": {"min_factor": 2, "max_factor": 8},
    "Momentum_delta_medium": {"min_factor": 10, "max_factor": 20}
}


INDICATOR_GROUPS = {
    "MACD": {
        "features": ["MACD", "MACD_medium_delta", "MACD_medium_pct", "MACD_medium_mean"],
        "params": ["MACD_fast", "MACD_slow", "MACD_signal", "MACD_delta_medium"]
    },
    "RSI": {
        "features": ["RSI", "RSI_medium_delta", "RSI_medium_pct", "RSI_medium_mean"],
        "params": ["RSI", "RSI_delta_medium"]
    },
    "CMO": {
        "features": ["CMO"],
        "params": ["CMO"]
    },
    "Sharpe": {
        "features": ["Sharpe_local"],
        "params": ["Sharpe_local"]
    },
    "Momentum": {
        "features": ["Momentum", "Momentum_medium_delta", "Momentum_medium_pct", "Momentum_medium_mean"],
        "params": ["Momentum", "Momentum_delta_medium"]
    },
    "CCI": {
        "features": ["CCI"],
        "params": ["CCI"]
    },
    "CMF": {
        "features": ["CMF"],
        "params": ["CMF"]
    },
    "EMA": {
        "features": ["EMA"],
        "params": ["EMA"]
    },
    "ATR": {
        "features": ["ATR"],
        "params": ["ATR"]
    },
    "ADX": {
        "features": ["ADX"],
        "params": ["ADX"]
    },
    "MFI": {
        "features": ["MFI"],
        "params": ["MFI"]
    },

    # === Nouveaux indicateurs ===

    "StochRSI": {
        "features": ["StochRSI_K", "StochRSI_D", "StochRSI_CrossUp"],
        "params": ["StochRSI_period", "StochRSI_fastk", "StochRSI_fastd"]
    },
    "TEMA": {
        "features": ["TEMA"],
        "params": ["TEMA"]
    },
    "ZScore": {
        "features": ["Z_Score", "Z_Score_abs"],
        "params": ["Z_Score"]
    },
    "VWAP": {
        "features": ["VWAP", "VWAP_Diff"],
        "params": ["VWAP"]
    }
}




def get_full_default_params(horizon_hour):
    full_params = {}
    for name, cfg in INDICATOR_CONFIG_LARGE.items():
        avg_factor = (cfg["min_factor"] + cfg["max_factor"]) / 2
        full_params[name] = int(horizon_hour * avg_factor)
    return full_params



def get_xgboost_params(horizon_hour):
    """
    Retourne des hyperparamètres XGBoost adaptés à l'horizon de prédiction via interpolation linéaire.
    Horizon attendu : en heures (par ex. 3h, 6h, ..., 24h)
    """

    def interp(val_min, val_max):
        """Interpolation linéaire selon l'horizon entre 3h (min) et 24h (max)."""
        h = min(max(horizon_hour, 3), 24)
        return val_min + (val_max - val_min) * ((h - 3) / (24 - 3))

    params = {
        "n_estimators": int(interp(300, 1000)),
        "max_depth": int(interp(4, 10)),
        "learning_rate": round(interp(0.07, 0.03), 5),
        "subsample": 0.9,
        "colsample_bytree": 0.8,
        "eval_metric": "rmse",
        "random_state": 42,
        "n_jobs": 2,
        "tree_method": "hist",
        "reg_alpha": round(interp(3.0, 0.5), 3),
        "reg_lambda": round(interp(3.0, 0.5), 3),
        "early_stopping_rounds": int(interp(25, 60))
    }

    return params

def suggest_safe_shift_points(trial, horizon_hour, interval_min=5, min_ratio=0.3, max_ratio=0.4):
    """
    Suggère une valeur de safe_shift_points (en points, pas en minutes),
    en fonction de l'horizon d'entraînement.

    - min_ratio : % min de l'horizon à considérer (ex: 20%)
    - max_ratio : % max de l'horizon à considérer (ex: 40%)
    """
    horizon_minutes = horizon_hour * 60
    min_shift_minutes = int(horizon_minutes * min_ratio)
    max_shift_minutes = int(horizon_minutes * max_ratio)

    # Suggestion en minutes, convertie en points
    shift_minutes = trial.suggest_int("safe_shift", min_shift_minutes, max_shift_minutes)
    safe_shift_points = shift_minutes // interval_min

    return max(1, safe_shift_points)



def suggest_global_params(trial, best_periods):
    params = {}

    for name, cfg in INDICATOR_CONFIG_LARGE.items():

        if name not in best_periods:
            raise optuna.TrialPruned()
        
        low = max(2, int(best_periods[name] - cfg["min_factor"]))
        high = int(best_periods[name] + cfg["max_factor"])
        # On ne gère pas encore les contraintes, on stocke simplement les bornes
        params[name] = trial.suggest_int(name, low, high)

    # ✅ Contraintes logiques (ex : MACD_fast < MACD_slow)
    if params["MACD_fast"] >= params["MACD_slow"]:
        raise optuna.TrialPruned()  # Invalide, on prune le trial

    return params




def suggest_indicator_params(trial, horizon):
    params = {}

    for name, cfg in INDICATOR_CONFIG_LARGE.items():
        low = int(horizon * cfg["min_factor"])
        high = int(horizon * cfg["max_factor"])
        # On ne gère pas encore les contraintes, on stocke simplement les bornes
        params[name] = trial.suggest_int(name, low, high)

    # ✅ Contraintes logiques (ex : MACD_fast < MACD_slow)
    if params["MACD_fast"] >= params["MACD_slow"]:
        raise optuna.TrialPruned()  # Invalide, on prune le trial

    return params



def should_prune_trial(avg_metrics, combined_score, trial, uid, horizon, strict_pruning=True):
    """
    Applique des règles de pruning conditionnelles selon le mode strict ou light.
    """
    trial_id = f"Trial #{trial.number + 1}"
    correlation_val = avg_metrics.get("correlation_val", 0)
    correlation_train = avg_metrics.get("correlation_train", 0)
    directional_accuracy = avg_metrics.get("directional_accuracy", 0)
    inv_corr = avg_metrics.get("inverted_correlation", 0)
    inv_dir_acc = avg_metrics.get("inverted_directional_accuracy", 0)

    # 🔒 STRATEGIE STRICTE
    if strict_pruning:
        if correlation_val < 0.1 and directional_accuracy < 0.6:
            logger.send_log(f"⚠️ - {uid} - {horizon['name']} - {trial_id} - Corrélation très faible ET mauvaise direction ({avg_metrics}), pruning", "warning")
            raise optuna.TrialPruned()

        if correlation_train > 0.6 and correlation_val < 0.0:
            logger.send_log(f"⚠️ - {uid} - {horizon['name']} - {trial_id} - Overfit détecté (train > 0.6 et val < 0)", "warning")
            raise optuna.TrialPruned()

        if combined_score > 1.9:
            logger.send_log(f"⚠️ - {uid} - {horizon['name']} - {trial_id} - Combined score trop élevé ({combined_score}), pruning", "warning")
            raise optuna.TrialPruned()

        if correlation_val < -0.2 and inv_corr > 0.3 and inv_dir_acc > 0.65:
            logger.send_log(f"⚠️ - {uid} - {horizon['name']} - {trial_id} - Modèle inversé détecté, pruning (strict)", "warning")
            raise optuna.TrialPruned()

    # 🧪 STRATEGIE LIGHT
    else:
        if correlation_val < -0.2 and directional_accuracy < 0.5:
            logger.send_log(f"🟡 - {uid} - {horizon['name']} - {trial_id} - Corrélation négative et mauvaise direction ({avg_metrics}), pruning (light)", "warning")
            raise optuna.TrialPruned()

        if correlation_train > 0.7 and correlation_val < -0.1:
            logger.send_log(f"🟡 - {uid} - {horizon['name']} - {trial_id} - Overfit léger détecté (train > 0.7 et val < -0.1)", "warning")
            raise optuna.TrialPruned()

        if combined_score > 2.3:
            logger.send_log(f"🟡 - {uid} - {horizon['name']} - {trial_id} - Combined score trop élevé ({combined_score}), pruning (light)", "warning")
            raise optuna.TrialPruned()

        if correlation_val < -0.3 and inv_corr > 0.3 and inv_dir_acc > 0.6:
            logger.send_log(f"🟡 - {uid} - {horizon['name']} - {trial_id} - Modèle probablement inversé, pruning (light)", "warning")
            raise optuna.TrialPruned()

'''
def should_prune_trial(avg_metrics, combined_score, trial, uid, horizon, strict_pruning=True):
    """
    Applique des règles de pruning conditionnelles selon le mode strict ou non.
    """
    trial_id = f"Trial #{trial.number + 1}"

    # 🔒 Mode strict
    if strict_pruning:
        if avg_metrics["correlation_val"] < 0.1 and avg_metrics["directional_accuracy"] < 0.6:
            logger.send_log(f"⚠️ - {uid} - {horizon['name']} - {trial_id} - Corrélation très faible ET mauvaise direction ({avg_metrics}), pruning","warning")
            raise optuna.TrialPruned()

        if avg_metrics["correlation_train"] > 0.6 and avg_metrics["correlation_val"] < 0.0:
            logger.send_log(f"⚠️ - {uid} - {horizon['name']} - {trial_id} - Overfit détecté (corrélation train > 0.6 et val < 0)","warning")
            raise optuna.TrialPruned()

        if combined_score > 3:
            logger.send_log(f"⚠️ - {uid} - {horizon['name']} - {trial_id} - Combined score trop élevé ({combined_score}), pruning","warning")
            raise optuna.TrialPruned()

    # 🧪 Mode Light 
    else:
        if avg_metrics["correlation_val"] < -0.2 and avg_metrics["directional_accuracy"] < 0.5:
            logger.send_log(f"🟡 - {uid} - {horizon['name']} - {trial_id} - Corrélation négative et mauvaise direction ({avg_metrics}), pruning (light)","warning")
            raise optuna.TrialPruned()

        if avg_metrics["correlation_train"] > 0.7 and avg_metrics["correlation_val"] < -0.1:
            logger.send_log(f"🟡 - {uid} - {horizon['name']} - {trial_id} - Overfit (light) détecté (train > 0.7 et val < -0.1)","warning")
            raise optuna.TrialPruned()

        if combined_score > 3.3:
            logger.send_log(f"🟡 - {uid} - {horizon['name']} - {trial_id} - Combined score trop élevé ({combined_score}), pruning (light)","warning")
            raise optuna.TrialPruned()
        if avg_metrics['correlation_val'] < -0.3 and avg_metrics['inverted_correlation'] > 0.3 and avg_metrics['inverted_directional_accuracy'] > 0.6:
            logger.send_log("🟡 - {uid} - {horizon['name']} - {trial_id} - Modèle probablement inversé. Pruning (light)", "warning")
            raise optuna.TrialPruned()
'''



def start_evaluate_indicator_params(trial, horizon, uid, data, xgb_params, selected_features=None, override_params=None, safe_shift_points=None):
    horizon_hour = horizon["horizon_hour"]
    shift_value = horizon["shift_value"]
    
    #logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - safe_shift_points = {safe_shift_points}", "debug")
    if override_params:
        params = override_params
    else:
        params = suggest_indicator_params(trial, horizon["horizon_hour"])

    # Génération des features via indicateurs
    #logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Debug : {params}", "debug")
    try:
        df = indicator.generate_training_sample(data.copy(), params, uid, shift_value, horizon_hour, safe_shift_points)
    except Exception as e:
        logger.send_log(f"❌ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - Erreur dans generate_training_sample : {e}", "error")
        raise optuna.TrialPruned()
    

    #logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - length df = {len(df)}", "debug")

    # Vérification target
    if "log_return" not in df.columns:
        logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - {horizon['name']} - Données invalides, 'log_return' manquant", "warning")
        raise optuna.TrialPruned()
    
    # Retrait colonnes inutiles
    df.drop(columns=["timestamp", "future_log_price", "highPrice", "lowPrice", "lastPrice", "past_base_price"], inplace=True, errors="ignore")

    y = df["log_return"]
    X = df.drop(columns=["log_return"])

    strict_pruning = True
    # Sélection unique de feature
    if selected_features is not None:
        strict_pruning = False
        missing_features = [f for f in selected_features if f not in X.columns]
        if missing_features:
            logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Features manquantes : {missing_features}", "warning")
            raise optuna.TrialPruned()
        X = X[selected_features]

    xgb_model = None  # Initialisation sécurisée avant la boucle

    n_samples = len(X)
    n_splits = min(3, n_samples - 1)  # au moins 2 samples nécessaires

    if n_splits < 2:
        logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - Données insuffisantes pour TimeSeriesSplit (samples={n_samples})", "warning")
        raise optuna.TrialPruned()

    tscv = TimeSeriesSplit(n_splits=n_splits)

    combined_scores = []
    all_metrics = []

    for train_index, val_index in tscv.split(X):
        X_train, X_val = X.iloc[train_index], X.iloc[val_index]
        y_train_raw, y_val_raw = y.iloc[train_index], y.iloc[val_index]

        # ✅ Standardisation locale (moyenne 0, std 1)
        scaler = StandardScaler()
        y_train_scaled = scaler.fit_transform(y_train_raw.values.reshape(-1, 1)).flatten()
        y_val_scaled = scaler.transform(y_val_raw.values.reshape(-1, 1)).flatten()

        xgb_model = xgb.XGBRegressor(**xgb_params)

        try:
            xgb_model.fit(
                X_train, y_train_scaled,
                eval_set=[(X_train, y_train_scaled), (X_val, y_val_scaled)],
                verbose=False
            )

            # Prédictions dans l’espace standardisé
            y_train_pred_scaled = xgb_model.predict(X_train)
            y_val_pred_scaled = xgb_model.predict(X_val)

            # ✅ Reprojection dans l’échelle d’origine
            y_train_pred = scaler.inverse_transform(y_train_pred_scaled.reshape(-1, 1)).flatten()
            y_val_pred = scaler.inverse_transform(y_val_pred_scaled.reshape(-1, 1)).flatten()

        except Exception as e:
            logger.send_log(f"❌ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - Erreur entraînement XGBoost : {e}", "error")
            raise optuna.TrialPruned()

        try:
            metrics = models.compute_combined_score(
                y_train_pred, y_train_raw,  # ⚠️ important de comparer sur l’échelle réelle
                y_val_pred, y_val_raw,
                weights = {
                    'rmse': 0.5,
                    'overfitting': 0.1,
                    'directional_accuracy': 0.2,
                    'correlation': 0.2
                }
            )

            if not isinstance(metrics, dict) or "combined_score" not in metrics:
                raise ValueError("Combined score manquant ou invalide dans les métriques.")

            combined_scores.append(metrics["combined_score"])
            all_metrics.append(metrics)

        except Exception as e:
            logger.send_log(f"❌ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - Erreur dans compute_combined_score : {e}", "error")
            raise optuna.TrialPruned()

    average_combined_score = np.mean(combined_scores)

    # Moyenne des métriques
    avg_metrics = {
        k: float(np.mean([m[k] for m in all_metrics]))
        for k in all_metrics[0].keys()
    }

    
    should_prune_trial(avg_metrics, average_combined_score, trial, uid, horizon, strict_pruning)
    
    '''
    # ⚠️ Pruning si trop peu de features utiles (selon SHAP)
    important_features, shap_importance_df = models.get_important_features_from_shap(xgb_model, X)
    ratio_features = len(important_features) / X.shape[1]
    if ratio_features < 0.1:
        logger.send_log(f"⚠️ - {uid} - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Seulement {len(important_features)} / {X.shape[1]} features utiles (ratio={ratio_features:.2f}), pruning","warning")
        raise optuna.TrialPruned()
    '''

    # Vérifie que xgb_model est bien défini avant de stocker dans Optuna
    if xgb_model is not None:
        trial.set_user_attr("best_model", xgb_model)
    else:
        logger.send_log(f"❌ - {uid} - {horizon['name']} - Aucun modèle entraîné avec succès !", "error")
        raise optuna.TrialPruned()

    # Stockage dans Optuna
    trial.set_user_attr("indicator_params", params)
    trial.set_user_attr("used_features", X)
    trial.set_user_attr("combined_score", average_combined_score)
    trial.set_user_attr("evaluation", avg_metrics)
    trial.set_user_attr("y_test", y_val_raw.tolist())  # réels
    trial.set_user_attr("y_pred", y_val_pred.tolist())  # prédits, inversés
    trial.set_user_attr("safe_shift_points", safe_shift_points) 

    if "timestamp" in X_val.columns:
        trial.set_user_attr("timestamps", [ts.isoformat() for ts in pd.to_datetime(X_val["timestamp"])])

    
    #logger.send_log(f"📊 - {uid} - {horizon['name']} - Trial #{trial.number + 1} - Combined score = {average_combined_score}", "info")
    #logger.send_log(f"ℹ️ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - Combined score du trial = {average_combined_score}", "info")
    #logger.send_log(f"🔎 - {uid} - {horizon['name']} - Params du trial réussi : {params}", "debug")
    #save_trial_to_csv(uid, horizon['name'], avg_metrics, params)
    return average_combined_score





def global_optimisation(uid, model, best_periods, candlesticks, xgb_params):
    study = optuna.create_study(
        directions=["minimize"],
        study_name="indicator_multiobj",  
        sampler=optuna.samplers.TPESampler(n_startup_trials=10, multivariate=True)
    )

    study.enqueue_trial(best_periods)

    study.optimize(
        lambda trial: start_evaluate_indicator_params(
            trial, 
            model, 
            uid, 
            candlesticks, 
            xgb_params, 
            override_params=suggest_global_params(trial, best_periods), 
            safe_shift_points=suggest_safe_shift_points(trial, model['horizon_hour'])),
        n_trials=GLOBAL_FEATURE_TRIAL
    )

    # ✅ Vérifie si aucun trial n’a réussi
    if not study.best_trials:
        logger.send_log(f"❌ - {uid} - {model['name']} - Aucun trial n'a été validé avec succès", "error")
        return False 
    
    # Choix du meilleur trial
    best_trial = sorted(
        study.best_trials,
        key=lambda t: t.values[0]
    )[0]


    return best_trial






def process_crypto(uid):
    models_data = models.get_horizon()

    for model in models_data:
        try:
            minutes_depth = model["horizon_hour"] * 20 * 5
            until_date = datetime.utcnow() #- timedelta(minutes=minutes_depth)
            
            candlesticks = mongo.get_candlesticks(uid, until_date)
            previous_best_params = mongo.get_indicatorsparams(model["name"], uid, model["horizon_hour"])
            # ✅ Si les paramètres existent ET sont récents (< 6h), on saute ce modèle
            if previous_best_params:
                updated_at = previous_best_params.get("updated_at")
                if updated_at and updated_at > datetime.utcnow() - timedelta(hours=10):
                    logger.send_log(f"⏱️ - {uid} - {model['name']} - Paramètres déjà optimisés récemment ({updated_at}). Sortie anticipée.","info")
                    continue

            xgb_params = get_xgboost_params(model["horizon_hour"])
            #logger.send_log(f"ℹ️ - {uid} - {model['name']} - Params utilisés = {xgb_params}","info")

            #logger.send_log(f"⚠️ - {uid} - {model['name']} - candlesticks trouvé = {len(candlesticks)}","error")

            if len(candlesticks) == 0:
                logger.send_log(f"⚠️ - {uid} - {model['name']} - Aucun candlesticks trouvés","error")
                continue


            logger.send_log(f"ℹ️ - {uid} - {model['name']} - Début optimisation indicateurs individuels","info")
            # Evaluation par indicateurs
            best_periods = {}

            for group_name, param_names in INDICATOR_GROUPS.items():
                selected_features = param_names["features"]  # ✅ ce sont les colonnes à garder
                param_names = param_names["params"]

                logger.send_log(f"ℹ️ - {uid} - {model['name']} - {group_name} Début optimisation","info")

                study = optuna.create_study(
                    directions=["minimize"],
                    study_name=f"opt_{group_name}",
                    sampler=optuna.samplers.TPESampler(n_startup_trials=10, multivariate=True)
                )

                def objective(trial):
                    params = get_full_default_params(model["horizon_hour"])

                    for param in param_names:
                        cfg = INDICATOR_CONFIG_LARGE[param]
                        low = int(model["horizon_hour"] * cfg["min_factor"])
                        high = int(model["horizon_hour"] * cfg["max_factor"])
                        params[param] = trial.suggest_int(param, low, high)

                    if group_name == "MACD":
                        if params["MACD_fast"] >= params["MACD_slow"]:
                            raise optuna.TrialPruned()

                    return start_evaluate_indicator_params(
                        trial=trial,
                        horizon=model,
                        uid=uid,
                        data=candlesticks,
                        xgb_params=xgb_params,
                        selected_features=selected_features,
                        override_params=params
                    )

                # ➕ (optionnel) Réinjection des meilleurs paramètres précédents du groupe
                if previous_best_params:
                    previous_params = previous_best_params.get("indicator_params", {})
                    group_params = {
                        k: v for k, v in previous_params.items() if k in param_names
                    }
                    if len(group_params) == len(param_names):
                        study.enqueue_trial(group_params)

                # 🔁 Lancement de l’optimisation Optuna
                study.optimize(objective, n_trials=INDIVIDUAL_FEATURE_TRIAL)

                # ✅ Résultat du meilleur trial
                if not study.best_trials:
                    logger.send_log(f"❌ - {uid} - {model['name']} - {group_name} - Aucun trial n'a été validé avec succès", "error")
                    continue

                best_trial = sorted(study.best_trials, key=lambda t: t.values[0])[0]
                best_params_group = best_trial.user_attrs.get("indicator_params")

                for k, v in best_params_group.items():
                    best_periods[k] = v

                evaluation = best_trial.user_attrs.get("evaluation")

                logger.send_log(f"ℹ️ - {uid} - {model['name']} - {group_name} - Combined score de l'optimisation = {evaluation['combined_score']}", "info")




            # Fine tuning global
            logger.send_log(f"ℹ️ - {uid} - {model['name']} - Début optimisation indicateurs groupé","info")
            best_trial = global_optimisation(uid, model, best_periods, candlesticks, xgb_params)

            evaluation = best_trial.user_attrs.get("evaluation")
            best_periods = best_trial.user_attrs.get("indicator_params")
            logger.send_log(f"🏆 - {uid} - {model['name']} - GLOBAL - Meilleur score ={evaluation['combined_score']}", "info")

           
            
            # ⬇️ Récupération dans des variables
            indicator_params = best_trial.user_attrs.get("indicator_params")
            evaluation = best_trial.user_attrs.get("evaluation")
            used_features = best_trial.user_attrs.get("used_features")
            best_model = best_trial.user_attrs.get("best_model")
            safe_shift_points = best_trial.user_attrs.get("safe_shift_points")

            logger.send_log(f"🏆 - {uid} - {model['name']} - Meilleur score = {evaluation['combined_score']}", "info")

            try:
                important_features, shap_df = models.get_important_features_from_shap(best_model, used_features, threshold=-100)
                shap_dict = dict(zip(shap_df["feature"], shap_df["mean_abs_shap"]))

            except Exception as e:
                logger.send_log(f"⚠️ - {uid} - {model['name']} - Erreur lors de get_important_features_from_shap : {e}","error")
                continue
            
        


            data = {
                "indicator_params": indicator_params,
                "evaluation": evaluation,
                "score_features": shap_dict,
                "safe_shift_points": safe_shift_points
            }
            mongo.create_indicatorparams(model["name"], uid, data)

            data["uid"] = uid
            data["horizon"] = model['name']
            logger.send_raw_data_log(data, metric="indicators_params")
            
            
        except Exception as e:
            logger.send_log(f"❌ - Erreur lors d'une tentative d'optimisation via Optuna : {e}", "error")
            continue

    return True

            



def optimize_features_for_all():
    logger.send_log("✅ Lancement du calcul des indicateurs", "info")

    trading_uids = mongo.get_trading_pairs()
    max_workers = 1  # À adapter selon ton CPU

    if not trading_uids:
        logger.send_log("❌ Aucune crypto en trading trouvée.", "error")
        return

    logger.send_log(f"📌 Début du calcul des indicateurs pour {len(trading_uids)} cryptos...", "info")

    futures = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for uid in trading_uids:
            futures.append(executor.submit(process_crypto, uid))

        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                logger.send_log(f"⚠️ Erreur pendant le calcul pour une crypto : {e}", "error")

    logger.send_log("✅ Calcul des indicateurs terminé.", "info")
    return True




def run_parallel_optimizations():
    """
    Exécute l'optimisation des paramètres des indicateurs pour chaque modèles pour chaque crypto en mode parallèle.
    """
    trading_uids = mongo.get_trading_pairs()

    if not trading_uids:
        logger.send_log("⚠️ Aucune crypto à entraîner (test ou trading) !","warning")
        return False

    # 🔥 Détermination dynamique du nombre de threads (évite surcharge CPU)
    max_workers = 1

    logger.send_log(f"🚀 Lancement des optimisations en parallèle (max_workers={max_workers})","info")

    # 🔥 Exécuter les optimisations en parallèle avec ThreadPoolExecutor
    try:
        optimize_features_for_all()
    except Exception as e:
        logger.send_log(f"❌ - Erreur lors de l'optimisation en parrallèle : {e}", "error")


    logger.send_log("✅ Toutes les optimisations sont terminées !","info")
    return True



def main():
    INTERVAL = 5
    error_count = 0

    while True:
        try:
            logger.send_log("🚀 Lancement d'un nouveau cycle d'optimisation des paramètres d'indicateurs...", "info")
            success = run_parallel_optimizations()

            if success:
                logger.send_log(f"✅ Cycle terminé. Prochain dans {INTERVAL} sec...", "info")
                error_count = 0
            else:
                logger.send_log("⚠️ Aucune optimisation réalisée.", "warning")

        except Exception as e:
            error_count += 1
            logger.send_log(f"❌ Erreur dans `main()` : {e} (tentative {error_count})", "error")
            if error_count >= 5:
                logger.send_log("🚨 Trop d'erreurs, arrêt forcé.", "critical")
                break

        time.sleep(60)

    logger.send_log("👋 Arrêt du programme terminé proprement.", "info")

if __name__ == "__main__":
    main()
