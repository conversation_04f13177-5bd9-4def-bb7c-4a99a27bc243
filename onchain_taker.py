import time
import requests
import math
from datetime import datetime, timezone, timedelta
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# --- Configuration ---
SERVICE = "onchain_taker"
MEMPOOL_API_BASE = "https://mempool.space/api"
SYMBOL = "BTC"
WHALE_THRESHOLD_BTC = 100  # Seuil pour définir un "whale"
NB_BLOCKS_TO_FETCH = 3     # Nombre de blocs récents à analyser
Z_SCORE_WINDOW = 12        # Nombre de périodes pour calculer le Z-score (~1h si 5min interval)

# --- Initialisations ---
logger = GrafanaUtils(service="onchain_taker_log")
mongo = MongoUtils(logger=logger)

mongo.connect(service=SERVICE)

print("[INFO] Script OnChain Taker lancé (via blocs mempool.space)")
logger.send_log("Script OnChain Taker lancé (via blocs mempool.space)", "info")

# --- Crée l'index Mongo ---
try:
    mongo.db["on_chain_datas"].create_index(
        [("timestamp", 1), ("symbol", 1)],
        unique=True
    )
    logger.send_log("Index Mongo (timestamp + symbol) créé ou déjà existant", "info")
except Exception as e:
    logger.send_log(f"Erreur création index Mongo : {e}", "error")

# --- Fonctions Utilitaires ---
def fetch_recent_blocks():
    try:
        response = requests.get(f"{MEMPOOL_API_BASE}/blocks", timeout=10)
        response.raise_for_status()
        blocks = response.json()
        logger.send_log(f"{len(blocks)} blocs récupérés", "debug")
        return blocks[:NB_BLOCKS_TO_FETCH]
    except Exception as e:
        logger.send_log(f"Erreur récupération des blocs : {e}", "error")
        return []

def fetch_block_transactions(block_id):
    try:
        response = requests.get(f"{MEMPOOL_API_BASE}/block/{block_id}/txs", timeout=10)
        response.raise_for_status()
        transactions = response.json()
        logger.send_log(f"{len(transactions)} transactions pour bloc {block_id}", "debug")
        return transactions
    except Exception as e:
        logger.send_log(f"Erreur récupération transactions du bloc {block_id} : {e}", "error")
        return []

def safe_sum_prevout_value(vins):
    total = 0
    if vins:
        for vin in vins:
            if vin and isinstance(vin, dict):
                prevout = vin.get("prevout")
                if prevout and isinstance(prevout, dict):
                    total += prevout.get("value", 0)
    return total

def safe_sum_vout_value(vouts):
    total = 0
    if vouts:
        for vout in vouts:
            if vout and isinstance(vout, dict):
                total += vout.get("value", 0)
    return total

def get_last_netflows(symbol, window):
    try:
        cursor = mongo.db["on_chain_datas"].find(
            {"symbol": symbol},
            sort=[("openTime", -1)],
            limit=window
        )
        return [doc.get("netflow", 0) for doc in cursor if doc.get("netflow") is not None]
    except Exception as e:
        logger.send_log(f"Erreur lecture historique netflows : {e}", "error")
        return []

def compute_z_score(series, value):
    if not series:
        return 0
    mean = sum(series) / len(series)
    variance = sum((x - mean) ** 2 for x in series) / len(series)
    stddev = math.sqrt(variance)
    if stddev == 0:
        return 0
    return (value - mean) / stddev

# --- Process Principal ---
def process_and_store():
    now = datetime.now(timezone.utc)
    minute = (now.minute // 5) * 5
    open_time = now.replace(minute=minute, second=0, microsecond=0)
    close_time = open_time + timedelta(minutes=5) - timedelta(milliseconds=1)
    bucket = open_time.strftime("%Y-%m-%d %H:%M:%S UTC")

    blocks = fetch_recent_blocks()
    if not blocks:
        return

    inflows = 0
    outflows = 0
    inflow_count = 0
    outflow_count = 0
    inflow_values = []
    outflow_values = []
    whale_inflows = 0
    whale_outflows = 0
    whale_activity = 0

    for block in blocks:
        block_id = block.get("id")
        if not block_id:
            continue

        transactions = fetch_block_transactions(block_id)
        for tx in transactions:
            total_output = safe_sum_vout_value(tx.get("vout", [])) / 1e8
            total_input = safe_sum_prevout_value(tx.get("vin", [])) / 1e8

            if total_output >= total_input:
                inflows += total_output
                inflow_count += 1
                inflow_values.append(total_output)
                if total_output >= WHALE_THRESHOLD_BTC:
                    whale_inflows += total_output
                    whale_activity += 1
            else:
                outflows += total_input
                outflow_count += 1
                outflow_values.append(total_input)
                if total_input >= WHALE_THRESHOLD_BTC:
                    whale_outflows += total_input
                    whale_activity += 1

    total_flow = inflows + outflows
    netflow = inflows - outflows
    netflow_ratio = (netflow / total_flow) if total_flow else 0
    whale_dominance_pct = (whale_activity / (inflow_count + outflow_count)) * 100 if (inflow_count + outflow_count) else 0

    last_netflows = get_last_netflows(SYMBOL, Z_SCORE_WINDOW)
    z_score = compute_z_score(last_netflows, netflow)

    mean_inflow = (sum(last_netflows) / len(last_netflows)) if last_netflows else 0
    inflow_spike = (inflows > 2 * mean_inflow) if mean_inflow else False
    outflow_spike = (outflows > 2 * mean_inflow) if mean_inflow else False

    document = {
        "timestamp": bucket,
        "openTime": open_time,
        "closeTime": close_time,
        "symbol": SYMBOL,
        "fetched_at": datetime.now(timezone.utc),
        "blockchain": "bitcoin",
        "bucket": bucket,
        "avg_inflow_value": (sum(inflow_values) / len(inflow_values)) if inflow_values else 0,
        "avg_outflow_value": (sum(outflow_values) / len(outflow_values)) if outflow_values else 0,
        "flow_sentiment_score": 0,
        "inflow_count": inflow_count,
        "inflows": inflows,
        "median_inflow_value": sorted(inflow_values)[len(inflow_values)//2] if inflow_values else 0,
        "median_outflow_value": sorted(outflow_values)[len(outflow_values)//2] if outflow_values else 0,
        "netflow": netflow,
        "netflow_ratio": netflow_ratio,
        "outflow_count": outflow_count,
        "outflows": outflows,
        "total_flow": total_flow,
        "whale_activity": whale_activity,
        "whale_dominance_pct": whale_dominance_pct,
        "whale_inflows": whale_inflows,
        "whale_outflows": whale_outflows,
        "whale_netflow": whale_inflows - whale_outflows,
        "netflow_z_score": z_score,
        "inflow_spike_detected": inflow_spike,
        "outflow_spike_detected": outflow_spike,
    }

    try:
        mongo.db["on_chain_datas"].insert_one(document)
        logger.send_log(f"Données agrégées insérées pour {bucket}", "info")
    except Exception as e:
        if "E11000" in str(e):
            logger.send_log(f"Doublon ignoré pour {SYMBOL} @ {bucket}", "debug")
        else:
            logger.send_log(f"Erreur insertion Mongo : {e}", "error")

# --- Boucle Principale ---
def main():
    while True:
        logger.send_log("Début nouvelle récupération mempool.space (blocs confirmés)", "info")
        process_and_store()
        logger.send_log("Attente de 5 minutes avant le prochain fetch...", "info")
        time.sleep(5 * 60)

if __name__ == "__main__":
    main()
