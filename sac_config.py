#!/usr/bin/env python3
"""
Configuration centralisée pour le système SAC
"""

import os
import sys
import pathlib
import datetime as dt
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("sac_system.log")
    ]
)
logger = logging.getLogger("sac_config")

# Chemins
try:
    PY = sys.executable
    MODEL_DIR = pathlib.Path("/home/<USER>/cryptobot/models")
    FEATURE_DIR = pathlib.Path("data/features")
    LOCK_DIR = pathlib.Path(".locks")
except Exception as e:
    logger.error(f"Erreur lors de l'initialisation des chemins: {e}")
    sys.exit(1)

# Constantes de temps
SLEEP_SECONDS = 300
STALE_LOCK_S = 3600
FALLBACK_HOURS = 48

# Constantes d'entraînement
TRAIN_EVERY_N_ROWS = 250
EVAL_FREQ = 10_000
EVAL_EPISODES = 5
EARLY_REWARD = 0.02
NO_IMPROVE_EVALS = 4

# Liste des assets par défaut
DEFAULT_ASSETS = [
    "BTCEUR", "ETHEUR", "PIVXUSDT", "DOGEEUR", "PEPEEUR",
    "XRPEUR", "AVAXEUR", "MASKUSDT", "GALAUSDT", "RLCUSDT"
]

# Commandes
EXPORT_CMD = [PY, "-m", "sac_export_features"]
WRAPPER_ENTRYPOINT = [PY, "-m", "sac_pipeline", "--_train_wrapper"]

# Fichiers
FALLBACK_FILE = "data/features/features-FALLBACK.parquet"

# Créer les répertoires nécessaires
MODEL_DIR.mkdir(parents=True, exist_ok=True)
FEATURE_DIR.mkdir(parents=True, exist_ok=True)
LOCK_DIR.mkdir(parents=True, exist_ok=True)

def get_timestamp():
    """Retourne un timestamp formaté pour les noms de fichiers."""
    return dt.datetime.now(dt.timezone.utc).strftime("%Y%m%d-%H%M%S")

def get_model_path(asset):
    """Retourne le chemin du modèle pour un asset."""
    return MODEL_DIR / f"sac_{asset}_{get_timestamp()}.zip"

def get_feature_path(asset):
    """Retourne le chemin du fichier de features pour un asset."""
    return FEATURE_DIR / f"features-{asset}.parquet"

def get_lock_path(asset):
    """Retourne le chemin du fichier de verrou pour un asset."""
    return LOCK_DIR / f"{asset}.lock"
