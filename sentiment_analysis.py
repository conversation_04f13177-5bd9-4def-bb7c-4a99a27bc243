#!/usr/bin/env python3
import time
import json
import os
import sys
import selectors
from datetime import datetime, timezone, timedelta
from newspaper import Article
import requests
import feedparser
from playwright.sync_api import sync_playwright
from dateutil import parser as dateutil_parser
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
import spacy

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
    'Accept': 'application/rss+xml, application/xml;q=0.9, */*;q=0.8'
}
COINGECKO_CACHE_FILE = os.path.join(os.path.dirname(__file__), "coingecko_cryptos.json")
COINGECKO_REFRESH_INTERVAL = timedelta(hours=24)

SERVICE = "news_taker"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)

nlp = spacy.load("en_core_web_sm")

FIFO_IN = "/tmp/sentiment_pipe_in"
FIFO_OUT = "/tmp/sentiment_pipe_out"
for fifo_path in [FIFO_IN, FIFO_OUT]:
    if not os.path.exists(fifo_path):
        os.mkfifo(fifo_path)

def load_crypto_names():
    now = datetime.now()
    try:
        if os.path.exists(COINGECKO_CACHE_FILE):
            with open(COINGECKO_CACHE_FILE, "r") as f:
                cache_data = json.load(f)
                last_update = datetime.fromisoformat(cache_data["last_update"])
                if now - last_update < COINGECKO_REFRESH_INTERVAL:
                    return set(cache_data["names"])
    except Exception as e:
        logger.send_log(f"⚠️ Erreur lecture cache CoinGecko: {e}", "warning")

    try:
        url = "https://api.coingecko.com/api/v3/coins/list"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        names = set([coin["id"].lower() for coin in data] +
                    [coin["symbol"].lower() for coin in data] +
                    [coin["name"].lower() for coin in data])
        with open(COINGECKO_CACHE_FILE, "w") as f:
            json.dump({"last_update": now.isoformat(), "names": list(names)}, f)
        logger.send_log("✅ Liste des cryptos mise à jour depuis CoinGecko", "info")
        return names
    except Exception as e:
        logger.send_log(f"❌ CoinGecko indisponible : {e}", "error")
        try:
            with open(COINGECKO_CACHE_FILE, "r") as f:
                return set(json.load(f)["names"])
        except Exception as e:
            logger.send_log(f"❌ Aucun fallback possible : {e}", "error")
            return set()

CRYPTO_NAMES = load_crypto_names()

current_dir = os.path.dirname(os.path.abspath(__file__))
rss_list_path = os.path.join(current_dir, 'rss_list.json')
if not os.path.exists(rss_list_path):
    raise FileNotFoundError(f"rss_list.json introuvable : {rss_list_path}")
with open(rss_list_path, 'r') as file:
    rss_feed_sources = json.load(file)

def get_full_article_text_with_playwright(url):
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context()
            page = context.new_page()
            logger.send_log(f"[Playwright] Trying full article fallback: {url}", "info")
            page.goto(url, timeout=20000)
            page.wait_for_timeout(5000)
            content = page.locator("article").inner_text()
            browser.close()
            return content
    except Exception as e:
        logger.send_log(f"[Playwright] ❌ Failed: {e}", "error")
        return ""

def extract_cryptos_spacy(text):
    doc = nlp(text)
    return list({ent.text.lower().strip() for ent in doc.ents if ent.label_ in ["ORG", "PRODUCT"] and ent.text.lower().strip() in CRYPTO_NAMES}) or ["general"]

def article_exists_in_mongo(url):
    return mongo.db["news_data"].find_one({"url": url}) is not None

def analyze_with_pipe(text):
    try:
        logger.send_log(f"[PIPE] Envoi du texte dans le FIFO...", "debug")

        fifo_out_fd = os.open(FIFO_OUT, os.O_RDONLY | os.O_NONBLOCK)
        fifo_out = os.fdopen(fifo_out_fd)

        with open(FIFO_IN, "w") as fifo_in:
            fifo_in.write(text.replace("\n", " ") + "\n")

        sel = selectors.DefaultSelector()
        sel.register(fifo_out, selectors.EVENT_READ)
        events = sel.select(timeout=10)

        if not events:
            logger.send_log("❌ Timeout lecture FIFO_OUT (aucune réponse dans les 10s)", "error")
            return fallback_analysis()

        response = fifo_out.readline().strip()
        fifo_out.close()

        logger.send_log(f"[PIPE] Réponse reçue : {response[:100]}...", "debug")
        return json.loads(response)

    except Exception as e:
        logger.send_log(f"❌ Erreur FIFO FinBERT : {e}", "error")
        return fallback_analysis()

def fallback_analysis():
    return {
        "sentiment": "neutral",
        "sentiment_score": 0.0,
        "geo_topic": "neutral",
        "geo_score": 0.0,
        "geo_top_labels": [],
        "predicted_trend": "neutral",
        "impact_class": "neutral",
        "summary": "No significant macroeconomic context detected.",
        "macro_impact_score": 0.0,
        "uncertain_geo_topic": False
    }

def process_article(url):
    logger.send_log(f"[🔗] Processing article: {url}", "info")
    if article_exists_in_mongo(url):
        logger.send_log(f"⏩ Déjà présent : {url}", "info")
        return
    try:
        article = Article(url)
        article.download()
        article.parse()
        full_text = article.text[:1500] if article.text else ""
        if not full_text or len(full_text) < 500:
            full_text = get_full_article_text_with_playwright(url)
        logger.send_log(f"[🧠] Longueur du texte pour analyse : {len(full_text)}", "debug")
        analysis = analyze_with_pipe(full_text)
        cryptos = extract_cryptos_spacy(full_text)
        mongo.store_news_data({
            "url": url,
            "title": article.title,
            "authors": article.authors,
            "published_date": str(article.publish_date) if article.publish_date else None,
            "content": full_text,
            "sentiment": analysis["sentiment"],
            "geo_analysis": {"topic": analysis["geo_topic"], "score": analysis["geo_score"]},
            "cryptos": cryptos,
            "fetched_at": datetime.now(timezone.utc).isoformat()
        })
    except Exception as e:
        logger.send_log(f"⚠️ Erreur article : {e}", "error")

def fetch_feed_with_user_agent(url):
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        return feedparser.parse(response.content)
    except Exception as e:
        logger.send_log(f"[!] RSS fetch failed : {e}", "error")
        return None

def fetch_rss_detailed(source):
    logger.send_log(f"🔍 Source: {source['name']} ({source.get('language', 'unknown')})", "info")
    feed = fetch_feed_with_user_agent(source['url'])
    if not feed or feed.bozo:
        logger.send_log(f"❌ RSS invalide : {getattr(feed, 'bozo_exception', 'unknown error')}", "error")
        return
    now = datetime.now(timezone.utc)
    for entry in feed.entries:
        try:
            published = None
            if hasattr(entry, 'published_parsed'):
                published = datetime(*entry.published_parsed[:6], tzinfo=timezone.utc)
            elif hasattr(entry, 'updated_parsed'):
                published = datetime(*entry.updated_parsed[:6], tzinfo=timezone.utc)
            elif 'published' in entry:
                published = dateutil_parser.parse(entry['published']).astimezone(timezone.utc)
            elif 'dc:date' in entry:
                published = dateutil_parser.parse(entry['dc:date']).astimezone(timezone.utc)
            if not published:
                published = now
            if (now - published).total_seconds() > 300:
                continue
            process_article(entry.link)
        except Exception as e:
            logger.send_log(f"⚠️ RSS entry failed : {e}", "error")

if __name__ == '__main__':
    while True:
        for source in rss_feed_sources:
            if not source.get("enabled", True):
                continue
            if source['name'].lower() == 'cryptoslate':
                continue
            fetch_rss_detailed(source)
        time.sleep(1)
