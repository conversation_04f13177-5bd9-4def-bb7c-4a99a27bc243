#!/usr/bin/env python3
"""
sac_reset_all.py – Réinitialise tous les assets
──────────────────────────────────────────────────────
• Supprime modèles, métriques, signaux, audit, forecast_features (optionnel)
• Nettoie les fichiers locaux (.zip, .json, .parquet) (optionnel)
• (Re)génère des forecast_features pour tous les assets
• Crée des modèles SAC pour tous les assets
• Génère les signaux
• Lance le backtest global
"""

import os, subprocess, sys, time, json
from pathlib import Path
import pandas as pd
from datetime import datetime, timezone

from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

mongo = MongoUtils()
mongo.connect(service="sac_reset_all")
logger = GrafanaUtils(service="sac_reset_all")

# Vérification des chemins
MODEL_DIR = Path("/home/<USER>/cryptobot/models")
FEATURE_DIR = Path("data/features")

# Créer les répertoires s'ils n'existent pas
MODEL_DIR.mkdir(parents=True, exist_ok=True)
FEATURE_DIR.mkdir(parents=True, exist_ok=True)

COLLECTIONS_TO_CLEAN = [
    "trade_signals", "trade_strategy_perf", "train_metrics",
    "forecast_features", "forecast_audit"
]

DEFAULT_ASSETS = [
    "BTCEUR", "ETHEUR", "PIVXUSDT", "DOGEEUR", "PEPEEUR",
    "XRPEUR", "AVAXEUR", "MASKUSDT", "GALAUSDT", "RLCUSDT"
]

PYTHON = sys.executable
ENV = os.environ.copy()
ENV["VIRTUAL_ENV"] = str(Path(PYTHON).resolve().parent.parent)
ENV["PATH"] = f"{ENV['VIRTUAL_ENV']}/bin:" + ENV["PATH"]

def run_subprocess_safe(action: str, args: list, asset: str = None, timeout_sec: int = 600) -> bool:
    """Exécute un sous-processus avec gestion des erreurs et timeout."""
    try:
        logger.send_log(f"{action}_started", "info", extra_labels={"asset": asset, "cmd": " ".join(args)})
        
        # Exécuter le processus avec timeout
        result = subprocess.run(
            args, 
            env=ENV, 
            check=True, 
            timeout=timeout_sec,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        logger.send_log(f"{action}_completed", "info", extra_labels={
            "asset": asset, 
            "returncode": result.returncode
        })
        return True
    except subprocess.CalledProcessError as e:
        logger.send_log(f"{action}_failed", "error", extra_labels={
            "asset": asset, 
            "returncode": e.returncode,
            "output": e.output[:500]  # Limiter la taille du log
        })
        return False
    except subprocess.TimeoutExpired:
        logger.send_log(f"{action}_timeout", "error", extra_labels={
            "asset": asset, 
            "timeout": timeout_sec
        })
        return False
    except Exception as e:
        logger.send_log(f"{action}_error", "error", extra_labels={
            "asset": asset, 
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        return False

def delete_from_mongo(asset: str):
    for col in COLLECTIONS_TO_CLEAN:
        result = mongo.db[col].delete_many({"uid": asset} if "asset" not in col else {"asset": asset})
        logger.send_log(f"mongo_cleanup_{col}", "info", extra_labels={"asset": asset, "deleted": result.deleted_count})

def delete_local_models(asset: str):
    n_deleted = 0
    for f in MODEL_DIR.glob(f"sac_{asset}_*.*"):
        f.unlink()
        n_deleted += 1
    logger.send_log("local_models_deleted", "info", extra_labels={"asset": asset, "files_deleted": n_deleted})

def delete_parquet_files(asset: str):
    n_deleted = 0
    for f in FEATURE_DIR.glob(f"features-{asset}*.parquet"):
        f.unlink()
        n_deleted += 1
    logger.send_log("parquet_files_deleted", "info", extra_labels={"asset": asset, "files_deleted": n_deleted})

def seed_minimal_forecast_features():
    """Crée des entrées minimales dans forecast_features pour tous les assets."""
    logger.send_log("seeding_forecast_features", "info")
    
    for asset in DEFAULT_ASSETS:
        # Vérifier si des entrées existent déjà
        count = mongo.db["forecast_features"].count_documents({"uid": asset})
        if count > 0:
            logger.send_log("features_already_exist", "info", extra_labels={"asset": asset, "count": count})
            continue
            
        # Créer des entrées minimales
        entries = []
        now = datetime.now(timezone.utc)
        
        # Créer 100 entrées avec des timestamps différents
        for i in range(100):
            entry = {
                "uid": asset,
                "run_at": now,
                "open": 100 + i,
                "high": 110 + i,
                "low": 90 + i,
                "close": 105 + i,
                "volume": 1000 + i * 10,
                # Features normalisées
                "open_z": 0,
                "high_z": 1,
                "low_z": -1,
                "close_z": 0.5,
                "volume_z": 0
            }
            entries.append(entry)
            
        if entries:
            mongo.db["forecast_features"].insert_many(entries)
            logger.send_log("features_seeded", "info", extra_labels={"asset": asset, "count": len(entries)})

def export_features(asset: str, force: bool = False):
    """Exporte les features pour un asset."""
    args = [PYTHON, "-m", "sac_export_features", "--asset", asset]
    if force:
        args.append("--force")
    return run_subprocess_safe("export_features", args, asset, timeout_sec=600)

def create_dummy_model(asset: str, force: bool = False):
    """Crée un modèle minimal pour un asset."""
    # Vérifier si un modèle existe déjà
    existing_models = list(MODEL_DIR.glob(f"sac_{asset}_*.zip"))
    if existing_models and not force:
        logger.send_log("model_already_exists", "info", extra_labels={
            "asset": asset,
            "models": [m.name for m in existing_models]
        })
        
        # Vérifier si le JSON associé existe
        for model_path in existing_models:
            json_path = model_path.with_suffix(".json")
            if not json_path.exists():
                create_model_json(asset, model_path)
        return True
    
    # Utiliser le script dédié si disponible
    if Path("sac_create_dummy_models.py").exists():
        args = [PYTHON, "-m", "sac_create_dummy_models", "--asset", asset]
        if force:
            args.append("--force")
        return run_subprocess_safe("create_dummy_model", args, asset, timeout_sec=300)
    
    # Fallback: création manuelle d'un modèle minimal
    try:
        logger.send_log("creating_manual_model", "info", extra_labels={"asset": asset})
        
        # Importer les bibliothèques nécessaires
        try:
            from stable_baselines3 import SAC
            from gymnasium import Env
        except ImportError:
            # Fallback pour les anciennes versions
            try:
                from stable_baselines3 import SAC
                from gym import Env
            except ImportError:
                logger.send_log("libraries_missing", "error", extra_labels={
                    "asset": asset,
                    "error": "Impossible d'importer stable_baselines3 ou gymnasium/gym"
                })
                
                # Création d'un fichier ZIP minimal sans dépendances
                import zipfile, io
                import numpy as np
                
                model_filename = f"sac_{asset}_{datetime.now(timezone.utc).strftime('%Y%m%d')}.zip"
                model_path = MODEL_DIR / model_filename
                
                with zipfile.ZipFile(model_path, 'w') as zipf:
                    # Ajouter un fichier de poids aléatoires
                    weights = np.random.randn(10, 10).astype(np.float32)
                    weights_bytes = io.BytesIO()
                    np.save(weights_bytes, weights)
                    weights_bytes.seek(0)
                    zipf.writestr('weights.npy', weights_bytes.read())
                    
                    # Ajouter un fichier de configuration
                    config = {
                        "uid": asset,
                        "created_at": datetime.now(timezone.utc).isoformat(),
                        "features": ["open", "high", "low", "close", "volume", 
                                    "open_z", "high_z", "low_z", "close_z", "volume_z"],
                        "model_type": "sac",
                        "version": "1.0.0"
                    }
                    zipf.writestr('config.json', json.dumps(config, indent=2))
                
                # Créer le JSON associé
                create_model_json(asset, model_path)
                
                logger.send_log("model_created_manually", "info", extra_labels={
                    "asset": asset,
                    "model": model_path.name,
                    "size_bytes": model_path.stat().st_size
                })
                return True
        
        # Définir un environnement minimal
        class DummyEnv(Env):
            def __init__(self):
                self.observation_space = gym.spaces.Box(low=-1, high=1, shape=(10,), dtype=np.float32)
                self.action_space = gym.spaces.Box(low=-1, high=1, shape=(1,), dtype=np.float32)
            
            def reset(self, **kwargs):
                return np.zeros(10, dtype=np.float32), {}
            
            def step(self, action):
                return np.zeros(10, dtype=np.float32), 0.0, False, False, {}
        
        # Créer un modèle SAC minimal
        model = SAC("MlpPolicy", DummyEnv())
        
        # Générer un nom de fichier avec la date
        model_filename = f"sac_{asset}_{datetime.now(timezone.utc).strftime('%Y%m%d')}.zip"
        model_path = MODEL_DIR / model_filename
        
        # Sauvegarder le modèle
        model.save(model_path)
        
        # Créer le JSON associé
        create_model_json(asset, model_path)
        
        logger.send_log("model_created_manually", "info", extra_labels={
            "asset": asset,
            "model": model_path.name,
            "size_bytes": model_path.stat().st_size
        })
        return True
    except Exception as e:
        logger.send_log("manual_model_creation_error", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
        
        # Dernier recours: créer un fichier ZIP vide avec un JSON
        try:
            model_filename = f"sac_{asset}_{datetime.now(timezone.utc).strftime('%Y%m%d')}.zip"
            model_path = MODEL_DIR / model_filename
            
            # Créer un fichier ZIP vide
            with open(model_path, 'wb') as f:
                f.write(b'PK\x05\x06\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00')
            
            # Créer le JSON associé
            create_model_json(asset, model_path)
            
            logger.send_log("empty_model_created", "warning", extra_labels={
                "asset": asset,
                "model": model_path.name
            })
            return True
        except Exception as e2:
            logger.send_log("empty_model_creation_error", "error", extra_labels={
                "asset": asset,
                "error": str(e2)
            })
            return False

def create_model_json(asset: str, model_path: Path):
    """Crée un fichier JSON pour un modèle."""
    try:
        json_path = model_path.with_suffix(".json")
        
        # Vérifier si le JSON existe déjà
        if json_path.exists():
            logger.send_log("json_already_exists", "info", extra_labels={
                "asset": asset,
                "json": json_path.name
            })
            return True
            
        # Créer un JSON minimal
        json_data = {
            "uid": asset,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "model_path": str(model_path),
            "auto_generated": True,
            "features": ["open", "high", "low", "close", "volume", 
                         "open_z", "high_z", "low_z", "close_z", "volume_z"]
        }
        
        # Essayer de charger les statistiques depuis le fichier parquet
        try:
            parquet_file = FEATURE_DIR / f"features-{asset}.parquet"
            if parquet_file.exists():
                df = pd.read_parquet(parquet_file)
                features = [col for col in df.columns if col not in ["uid", "run_at"]]
                json_data["features"] = features
                
                # Ajouter des statistiques de normalisation
                json_data["norm_stats"] = {}
                for col in df.columns:
                    if col not in ["uid", "run_at"] and not col.endswith("_z"):
                        json_data["norm_stats"][col] = {
                            "mean": float(df[col].mean()),
                            "std": float(df[col].std() or 1.0)  # Éviter std=0
                        }
        except Exception as e:
            logger.send_log("parquet_read_error", "warning", extra_labels={
                "asset": asset,
                "error": str(e)
            })
            
        # Écrire le JSON
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
            
        logger.send_log("json_created", "info", extra_labels={
            "asset": asset,
            "json": json_path.name
        })
        return True
    except Exception as e:
        logger.send_log("json_creation_error", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
        return False

def generate_signal(asset: str):
    """Génère un signal pour un asset."""
    return run_subprocess_safe("generate_signal", [PYTHON, "-m", "sac_generate_signals", "--asset", asset, "--force"], asset)

def run_backtest():
    """Lance le backtest global."""
    return run_subprocess_safe("backtest", [PYTHON, "-m", "sac_strategy_backtests"])

def main():
    import argparse
    parser = argparse.ArgumentParser(description="Réinitialise les assets pour le système SAC")
    parser.add_argument("--clean", action="store_true", help="Nettoie les données existantes")
    parser.add_argument("--assets", nargs="+", help="Liste d'assets à traiter (par défaut: tous)")
    parser.add_argument("--skip-export", action="store_true", help="Saute l'export des features")
    parser.add_argument("--skip-models", action="store_true", help="Saute la création des modèles")
    parser.add_argument("--skip-signals", action="store_true", help="Saute la génération des signaux")
    parser.add_argument("--skip-backtest", action="store_true", help="Saute le backtest")
    args = parser.parse_args()
    
    assets = args.assets if args.assets else DEFAULT_ASSETS
    
    logger.send_log("reset_started", "info", extra_labels={"assets": len(assets)})

    # Nettoyage (optionnel)
    if args.clean:
        for asset in assets:
            delete_from_mongo(asset)
            delete_local_models(asset)
            delete_parquet_files(asset)
        logger.send_log("cleanup_complete", "info")

    # Créer des entrées minimales dans forecast_features
    seed_minimal_forecast_features()

    # Export des features
    if not args.skip_export:
        logger.send_log("exporting_features", "info")
        for asset in assets:
            export_features(asset, force=True)
    else:
        logger.send_log("skipping_feature_export", "info")

    # Création des modèles
    if not args.skip_models:
        logger.send_log("creating_models", "info")
        for asset in assets:
            create_dummy_model(asset, force=True)
    else:
        logger.send_log("skipping_model_creation", "info")

    # Génération des signaux
    if not args.skip_signals:
        logger.send_log("generating_signals", "info")
        for asset in assets:
            generate_signal(asset)
    else:
        logger.send_log("skipping_signal_generation", "info")

    # Backtest
    if not args.skip_backtest:
        logger.send_log("running_backtest", "info")
        run_backtest()
    else:
        logger.send_log("skipping_backtest", "info")

    logger.send_log("reset_complete", "info")

if __name__ == "__main__":
    main()
