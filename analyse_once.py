#!/home/<USER>/cryptobot/venv/bin/python3
import sys
import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from langdetect import detect

FINBERT_NAME = "yiyanghkust/finbert-tone"
ML_MODEL_NAME = "cardiffnlp/twitter-xlm-roberta-base-sentiment"

text = sys.stdin.read().strip()

if len(text) < 10:
    print("neutral")
    sys.exit(0)

try:
    lang = detect(text)
except Exception:
    lang = "unknown"

try:
    if lang == "en":
        tokenizer = AutoTokenizer.from_pretrained(FINBERT_NAME)
        model = AutoModelForSequenceClassification.from_pretrained(FINBERT_NAME)
        label_map = ["negative", "neutral", "positive"]
    else:
        tokenizer = AutoTokenizer.from_pretrained(ML_MODEL_NAME)
        model = AutoModelForSequenceClassification.from_pretrained(ML_MODEL_NAME)
        label_map = {0: "negative", 1: "neutral", 2: "positive"}

    inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
    with torch.no_grad():
        outputs = model(**inputs)
        logits = outputs.logits
        probs = F.softmax(logits, dim=1)
        predicted = torch.argmax(probs, dim=1).item()

    sentiment = label_map[predicted] if isinstance(label_map, list) else label_map.get(predicted, "neutral")
    print(sentiment)
except Exception:
    print("error")
