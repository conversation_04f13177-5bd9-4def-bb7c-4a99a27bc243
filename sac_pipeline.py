#!/usr/bin/env python3
"""
Daemon sac_pipeline
────────────────────
• Exporte les nouvelles lignes dans data/features/*.parquet
• Déclenche l’entraînement SAC (un asset à la fois), avec ou sans Optuna
• Regénère les .json manquants pour les modèles
• Journalise tout dans Grafana / Loki
• Fallback possible en reprenant les 48 h précédentes sans curseur
"""

from __future__ import annotations
import os, sys, time, signal, subprocess, pathlib, datetime as dt, warnings, json
import psutil
import pandas as pd
import traceback  # Ajout de l'import manquant

from common.mongo_utils   import MongoUtils
from common.grafana_utils import GrafanaUtils

warnings.filterwarnings("ignore", category=DeprecationWarning)

# ────────────────────────── constantes ──────────────────────────
PY                 = sys.executable
EXPORT_CMD         = [PY, "-m", "sac_export_features"]
WRAPPER_ENTRYPOINT = [PY, "-m", "sac_pipeline", "--_train_wrapper"]

SLEEP_SECONDS        = 300
TRAIN_EVERY_N_ROWS   = 250
LOCK_DIR             = pathlib.Path(".locks")  # Assurez-vous que c'est un objet Path
STALE_LOCK_S         = 3600
FALLBACK_HOURS       = 48
FALLBACK_FILE        = "data/features/features-FALLBACK.parquet"
MODEL_DIR            = pathlib.Path("/home/<USER>/cryptobot/models")  # Assurez-vous que c'est un objet Path
FEATURE_DIR          = pathlib.Path("data/features")  # Assurez-vous que c'est un objet Path
# ─────────────────────────────────────────────────────────────────
SERVICE = "sac_pipeline"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
# ----------------------------------------------------------------

# ═════════════════════ helpers path ════════════════════════════
def ensure_path(path_or_str):
    """Convertit une chaîne en objet Path si nécessaire."""
    if isinstance(path_or_str, str):
        return pathlib.Path(path_or_str)
    return path_or_str

def ensure_directories():
    """Vérifie et crée les répertoires nécessaires."""
    directories = [
        ensure_path(MODEL_DIR),
        ensure_path(FEATURE_DIR),
        ensure_path(LOCK_DIR)
    ]
    
    for directory in directories:
        if not directory.exists():
            directory.mkdir(parents=True, exist_ok=True)
            logger.send_log("directory_created", "info", extra_labels={"path": str(directory)})
    
    return directories

# ═════════════════════ helpers lockfile ════════════════════════
def _lock(asset: str) -> pathlib.Path:
    """Crée un fichier de verrou pour un asset."""
    lock_dir = ensure_path(LOCK_DIR)
    lock_dir.mkdir(exist_ok=True)
    return lock_dir / f"{asset}.lock"


def _create_lock(asset: str, pid: int):
    ensure_path(LOCK_DIR).mkdir(exist_ok=True)
    _lock(asset).write_text(str(pid))


def _lock_is_stale(lock_path: pathlib.Path) -> bool:
    """Vérifie si un verrou est périmé."""
    try:
        mtime = lock_path.stat().st_mtime
        return (time.time() - mtime) > STALE_LOCK_S
    except FileNotFoundError:
        return True  # Si le fichier n'existe plus, considérer comme périmé


def _remove_lock(asset: str) -> None:
    """Supprime le fichier de verrou d'un asset."""
    lock_path = _lock(asset)
    try:
        if lock_path.exists():
            lock_path.unlink()
    except Exception as e:
        logger.send_log("lock_remove_error", "error", 
                       extra_labels={"asset": asset, "error": str(e)})


# ═════════════════════ export features ════════════════════════
def _run_export(asset: str = None) -> bool:
    """Lance l'export des features, avec ou sans asset spécifique."""
    cmd = EXPORT_CMD.copy()
    if asset:
        cmd.extend(["--asset", asset])
    
    # Ajouter un verrou pour éviter les exports concurrents
    lock_path = pathlib.Path(LOCK_DIR) / "export.lock"
    if lock_path.exists() and not _lock_is_stale(lock_path):
        # Ajouter des détails sur le verrou existant
        try:
            lock_content = lock_path.read_text().strip().split("\n")
            pid = int(lock_content[0]) if len(lock_content) > 0 else None
            timestamp = float(lock_content[1]) if len(lock_content) > 1 else None
            
            process_info = {}
            if pid and psutil.pid_exists(pid):
                try:
                    process = psutil.Process(pid)
                    process_info = {
                        "process_name": process.name(),
                        "process_create_time": dt.datetime.fromtimestamp(process.create_time()).isoformat(),
                        "process_status": process.status()
                    }
                except Exception:
                    process_info = {"process_error": "Failed to get process info"}
            
            logger.send_log("export_already_running", "info", extra_labels={
                "pid": pid,
                "lock_age_seconds": time.time() - (timestamp or 0),
                "process_exists": psutil.pid_exists(pid) if pid else False,
                **process_info
            })
        except Exception as e:
            logger.send_log("lock_read_error", "warning", extra_labels={"error": str(e)})
        return False
    
    # Créer le verrou
    try:
        with open(lock_path, "w") as f:
            f.write(f"{os.getpid()}\n{time.time()}")
        
        # Exécuter l'export
        try:
            logger.send_log("export_started", "info", extra_labels={"asset": asset or "all"})
            result = subprocess.run(cmd, check=True, timeout=1800,
                                   stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                   text=True)
            logger.send_log("export_completed", "info", extra_labels={
                "asset": asset or "all",
                "returncode": result.returncode
            })
            return True
        except subprocess.CalledProcessError as e:
            logger.send_log("export_failed", "error", extra_labels={
                "asset": asset or "all",
                "returncode": e.returncode,
                "output": e.output[:500]  # Limiter la taille du log
            })
            return False
        except subprocess.TimeoutExpired:
            logger.send_log("export_timeout", "error", extra_labels={"asset": asset or "all"})
            return False
    finally:
        # Supprimer le verrou
        try:
            if lock_path.exists():
                lock_path.unlink()
        except Exception as e:
            logger.send_log("lock_remove_error", "error", extra_labels={"error": str(e)})


# ═══════════════════ lancement training ═══════════════════════
def _spawn_training(asset: str, *, tune: bool = False):
    """Lance un sous-process d'entraînement (wrapper) si pas déjà actif."""
    lock_path = _lock(asset)
    if lock_path.exists() and not _lock_is_stale(lock_path):
        return
    if lock_path.exists():               # lock périmé
        logger.send_log("stale_lock", "warning", extra_labels={"asset": asset})
        lock_path.unlink()
    
    # Créer le verrou avant de lancer le processus
    with open(lock_path, "w") as f:
        f.write(f"{os.getpid()}\n{time.time()}")
    
    cmd = WRAPPER_ENTRYPOINT + [asset]   # se relance lui-même avec le wrapper
    if tune:
        cmd.append("--tune")

    try:
        proc = subprocess.Popen(
            cmd, cwd=os.getcwd(), text=True,
            stdout=subprocess.PIPE, stderr=subprocess.STDOUT, close_fds=True,
        )
        logger.send_log("spawn_training", "info", extra_labels={
            "asset": asset, "pid": proc.pid, "tune": tune
        })
    except Exception as e:
        logger.send_log("spawn_error", "error", extra_labels={
            "asset": asset, "error": str(e)
        })
        _remove_lock(asset)  # Supprimer le verrou en cas d'erreur


def _train_wrapper(asset: str, *, tune: bool = False):
    """Processus enfant : exécute sac_train_rl.py puis libère le lock."""
    max_retries = 2
    retry_count = 0
    proc = None
    
    try:
        while retry_count <= max_retries:
            try:
                # Commande sans l'argument --data_dir
                cmd = [
                    PY, "sac_train_rl.py",
                    "--asset", asset,
                    "--ep_steps", "1000",
                    "--steps", "300000",
                ]
                if tune:
                    cmd += ["--tune", "--trials", "25", "--steps_per_trial", "80000"]

                logger.send_log("train_cmd", "debug", extra_labels={
                    "asset": asset, "cmd": " ".join(cmd), "tune": tune,
                    "retry": retry_count
                })

                proc = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    cwd=os.getcwd(),
                    timeout=3600  # Timeout d'une heure pour éviter les processus bloqués
                )
                
                # Traitement du résultat...
                if proc.returncode == 0:
                    # Succès, sortie de la boucle
                    break
                else:
                    # Échec, on incrémente le compteur de tentatives
                    retry_count += 1
                    logger.send_log("train_retry", "warning", extra_labels={
                        "asset": asset, "tune": tune, "retry": retry_count,
                        "rc": proc.returncode
                    })
                    # Attente avant nouvelle tentative
                    time.sleep(30)
            except subprocess.TimeoutExpired:
                retry_count += 1
                logger.send_log("train_timeout", "warning", extra_labels={
                    "asset": asset, "tune": tune, "retry": retry_count
                })
                time.sleep(30)
            except Exception as e:
                retry_count += 1
                logger.send_log("train_exception", "error", extra_labels={
                    "asset": asset, "tune": tune, "retry": retry_count,
                    "err": str(e)
                })
                time.sleep(30)

        # Log toujours un résumé du stdout/stderr pour le debug
        if proc:
            stdout_trim = (proc.stdout or "")[-2000:]  # les 2000 derniers caractères
            stderr_trim = (proc.stderr or "")[-2000:]
            all_logs = f"STDOUT:\n{stdout_trim}\nSTDERR:\n{stderr_trim}"

            if proc.returncode != 0:
                logger.send_log("train_failed", "error", extra_labels={
                    "asset": asset,
                    "tune": tune,
                    "rc": proc.returncode,
                    "stdout_tail": stdout_trim,
                    "stderr_tail": stderr_trim,
                    "cmd": " ".join(cmd),
                })
                # Ajoute une trace courte pour repérer l'erreur dans Loki
                print(f"[ERROR][{asset}] Training failed rc={proc.returncode}\n{all_logs}")
            else:
                logger.send_log("train_done", "info", extra_labels={
                    "asset": asset,
                    "tune": tune,
                    "stdout_tail": stdout_trim,
                    "stderr_tail": stderr_trim,
                })
                print(f"[INFO][{asset}] Training OK\n{all_logs}")
        else:
            logger.send_log("train_all_attempts_failed", "error", extra_labels={
                "asset": asset, "tune": tune, "max_retries": max_retries
            })
            print(f"[ERROR][{asset}] All training attempts failed after {max_retries} retries")

    finally:
        _remove_lock(asset)


# ═══════════════ fallback export & json regen ══════════════════
def _fallback_export(mongo: MongoUtils) -> bool:
    now   = dt.datetime.utcnow()
    start = now - dt.timedelta(hours=FALLBACK_HOURS)
    cur   = mongo.db["forecast_features"].find(
        {"created_at": {"$gte": start}}, {"_id": 0}
    )
    df = pd.DataFrame(list(cur))
    if df.empty:
        logger.send_log("fallback_empty", "warning")
        return False

    df.sort_values("created_at", inplace=True)
    pathlib.Path(FALLBACK_FILE).parent.mkdir(parents=True, exist_ok=True)
    df.to_parquet(FALLBACK_FILE, index=False)
    logger.send_log("fallback_exported", "info",
                    extra_labels={"rows": len(df)})
    return True


def _regenerate_missing_jsons(min_rows: int = 10):
    """Régénère les fichiers JSON manquants pour les modèles."""
    logger.send_log("json_regeneration_start", "info")
    
    # Vérification que le répertoire des modèles existe
    if not MODEL_DIR.exists():
        logger.send_log("model_dir_missing", "warning")
        MODEL_DIR.mkdir(parents=True, exist_ok=True)
        return
        
    # Vérification que le répertoire des features existe
    if not FEATURE_DIR.exists():
        logger.send_log("feature_dir_missing", "warning")
        return
        
    # Récupération des fichiers de modèles
    model_files = list(MODEL_DIR.glob("sac_*.zip"))
    
    for zipfile in model_files:
        jsonfile = zipfile.with_suffix(".json")
        if jsonfile.exists():
            continue
        asset = zipfile.name.split("_")[1]
        for parquet in FEATURE_DIR.glob("*.parquet"):
            try:
                df = pd.read_parquet(parquet)
                if {"uid", "run_at"} - set(df.columns):
                    continue
                df_asset = df[df["uid"] == asset]
                if len(df_asset) < min_rows:
                    continue
                df_asset = df_asset.drop(
                    columns=["uid", "run_at", "horizon_h"], errors="ignore"
                )
                jsonfile.write_text(json.dumps({"features": df_asset.columns.tolist()}))
                logger.send_log("json_regenerated", "info",
                                extra_labels={"asset": asset, "json": jsonfile.name})
                break
            except Exception as e:
                logger.send_log("json_regeneration_error", "error",
                                extra_labels={"asset": asset, "err": str(e)})


def _check_missing_models():
    """Vérifie et crée les modèles manquants pour tous les assets."""
    # Liste des assets à vérifier
    DEFAULT_ASSETS = [
        "BTCEUR", "ETHEUR", "PIVXUSDT", "DOGEEUR", "PEPEEUR",
        "XRPEUR", "AVAXEUR", "MASKUSDT", "GALAUSDT", "RLCUSDT"
    ]
    
    # S'assurer que MODEL_DIR est un objet Path
    model_dir = ensure_path(MODEL_DIR)
    
    # Vérifier les modèles existants
    existing_models = set()
    for model_file in model_dir.glob("sac_*.zip"):
        parts = model_file.stem.split("_")
        if len(parts) >= 2:
            existing_models.add(parts[1])
    
    # Identifier les assets manquants
    missing_assets = [asset for asset in DEFAULT_ASSETS if asset not in existing_models]
    
    if missing_assets:
        logger.send_log("missing_models_detected", "warning", extra_labels={
            "assets": missing_assets
        })
        
        # Créer les modèles manquants
        for asset in missing_assets:
            try:
                # Vérifier si les features existent avant de créer le modèle
                feature_dir = ensure_path(FEATURE_DIR)
                feature_file = feature_dir / f"features-{asset}.parquet"
                
                if not feature_file.exists():
                    logger.send_log("missing_features", "warning", extra_labels={"asset": asset})
                    # Exporter les features d'abord
                    try:
                        subprocess.run(
                            [PY, "-m", "sac_export_features", "--asset", asset, "--force"],
                            check=True, timeout=600
                        )
                        
                        # Vérifier si l'export a réussi
                        if not feature_file.exists():
                            # Créer un fichier de features minimal
                            _create_minimal_features(asset)
                    except Exception as e:
                        logger.send_log("feature_export_failed", "error", extra_labels={
                            "asset": asset,
                            "error": str(e)
                        })
                        # Créer un fichier de features minimal
                        _create_minimal_features(asset)
                
                # Créer un modèle minimal si sac_reset_all échoue
                try:
                    # Utiliser sac_reset_all.py pour créer le modèle
                    subprocess.run(
                        [PY, "-m", "sac_reset_all", "--assets", asset, "--skip-export", "--skip-signals", "--skip-backtest"],
                        check=True, timeout=600
                    )
                    
                    # Vérifier si le modèle a été créé
                    if not any(model_dir.glob(f"sac_{asset}_*.zip")):
                        # Créer un modèle minimal
                        _create_minimal_model(asset)
                    else:
                        logger.send_log("model_created", "info", extra_labels={"asset": asset})
                except Exception as e:
                    logger.send_log("model_creation_failed", "error", extra_labels={
                        "asset": asset,
                        "error": str(e)
                    })
                    # Créer un modèle minimal
                    _create_minimal_model(asset)
            except Exception as e:
                logger.send_log("model_creation_failed", "error", extra_labels={
                    "asset": asset,
                    "error": str(e),
                    "traceback": traceback.format_exc()
                })
    
    return len(missing_assets)

def _create_minimal_features(asset):
    """Crée un fichier de features minimal pour un asset."""
    try:
        import numpy as np
        
        # Créer un DataFrame minimal
        now = dt.datetime.utcnow()
        rows = []
        
        # Générer 1000 lignes de données
        for i in range(1000):
            timestamp = now - dt.timedelta(minutes=i*5)
            for horizon in [3, 6, 9, 12, 15, 18, 21, 24]:
                row = {
                    "uid": asset,
                    "run_at": timestamp,
                    "horizon_h": horizon,
                    "expected_ret": np.random.normal(0, 0.01),
                    "open": 100 + np.random.normal(0, 1),
                    "high": 105 + np.random.normal(0, 1),
                    "low": 95 + np.random.normal(0, 1),
                    "close": 102 + np.random.normal(0, 1),
                    "volume": 1000 + np.random.normal(0, 100),
                    "open_z": np.random.normal(0, 1),
                    "high_z": np.random.normal(0, 1),
                    "low_z": np.random.normal(0, 1),
                    "close_z": np.random.normal(0, 1),
                    "volume_z": np.random.normal(0, 1)
                }
                rows.append(row)
        
        # Créer le DataFrame
        df = pd.DataFrame(rows)
        
        # Sauvegarder en parquet
        feature_dir = ensure_path(FEATURE_DIR)
        feature_file = feature_dir / f"features-{asset}.parquet"
        df.to_parquet(feature_file, index=False)
        
        logger.send_log("minimal_features_created", "info", extra_labels={
            "asset": asset,
            "rows": len(df),
            "file": str(feature_file)
        })
        
        return True
    except Exception as e:
        logger.send_log("minimal_features_creation_failed", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
        return False

def _create_minimal_model(asset):
    """Crée un modèle minimal pour un asset."""
    try:
        import zipfile
        import json
        import os
        from datetime import datetime
        
        # Créer un fichier modèle minimal
        model_dir = ensure_path(MODEL_DIR)
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        model_file = model_dir / f"sac_{asset}_{timestamp}.zip"
        
        # Créer un fichier ZIP vide avec une structure minimale
        with zipfile.ZipFile(model_file, 'w') as zipf:
            # Ajouter un fichier de métadonnées
            metadata = {
                "asset": asset,
                "created_at": datetime.now().isoformat(),
                "version": "1.0.0",
                "type": "minimal"
            }
            zipf.writestr("metadata.json", json.dumps(metadata))
            
            # Ajouter un fichier de configuration vide
            zipf.writestr("config.json", "{}")
            
            # Ajouter un fichier de modèle vide
            zipf.writestr("model.pkl", "")
        
        # Créer le fichier JSON associé
        json_file = model_file.with_suffix(".json")
        json_data = {
            "uid": asset,
            "created_at": datetime.now().isoformat(),
            "features": ["open", "high", "low", "close", "volume", 
                        "open_z", "high_z", "low_z", "close_z", "volume_z"],
            "norm_stats": {
                "open": {"mean": 100.0, "std": 10.0},
                "high": {"mean": 105.0, "std": 10.0},
                "low": {"mean": 95.0, "std": 10.0},
                "close": {"mean": 102.0, "std": 10.0},
                "volume": {"mean": 1000.0, "std": 100.0}
            }
        }
        
        with open(json_file, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        logger.send_log("minimal_model_created", "info", extra_labels={
            "asset": asset,
            "model_file": str(model_file),
            "json_file": str(json_file)
        })
        
        return True
    except Exception as e:
        logger.send_log("minimal_model_creation_failed", "error", extra_labels={
            "asset": asset,
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        return False

def _check_missing_features(mongo: MongoUtils):
    """Vérifie et crée les features manquantes pour tous les assets."""
    # Liste des assets à vérifier
    DEFAULT_ASSETS = [
        "BTCEUR", "ETHEUR", "PIVXUSDT", "DOGEEUR", "PEPEEUR",
        "XRPEUR", "AVAXEUR", "MASKUSDT", "GALAUSDT", "RLCUSDT"
    ]
    
    # Vérifier les features existantes
    existing_features = set()
    for feature_file in FEATURE_DIR.glob("features-*.parquet"):
        parts = feature_file.stem.split("-")
        if len(parts) >= 2:
            existing_features.add(parts[1])
    
    # Identifier les assets manquants
    missing_assets = [asset for asset in DEFAULT_ASSETS if asset not in existing_features]
    
    if missing_assets:
        logger.send_log("missing_features_detected", "warning", extra_labels={
            "assets": missing_assets
        })
        
        # Vérifier d'abord si des données existent dans MongoDB
        for asset in missing_assets:
            try:
                # Compter les entrées dans forecast_features
                count = mongo.db["forecast_features"].count_documents({"uid": asset})
                
                if count > 0:
                    # Exporter les features existantes
                    logger.send_log("exporting_existing_features", "info", extra_labels={
                        "asset": asset, "count": count
                    })
                    
                    # Exporter les features via sac_export_features
                    subprocess.run(
                        [PY, "-m", "sac_export_features", "--asset", asset],
                        check=True, timeout=300
                    )
                else:
                    # Créer des features synthétiques
                    logger.send_log("creating_synthetic_features", "info", extra_labels={"asset": asset})
                    
                    # Utiliser sac_reset_all pour créer des features
                    subprocess.run(
                        [PY, "-m", "sac_reset_all", "--assets", asset, "--skip-models", "--skip-signals", "--skip-backtest"],
                        check=True, timeout=300
                    )
            except Exception as e:
                logger.send_log("feature_creation_failed", "error", extra_labels={
                    "asset": asset,
                    "error": str(e)
                })
    
    return len(missing_assets)

def _check_missing_jsons():
    """Vérifie et crée les fichiers JSON manquants pour les modèles."""
    missing_count = 0
    
    # S'assurer que MODEL_DIR est un objet Path
    model_dir = ensure_path(MODEL_DIR)
    
    # Parcourir tous les modèles
    for model_file in model_dir.glob("sac_*.zip"):
        json_file = model_file.with_suffix(".json")
        
        # Si le JSON n'existe pas, le créer
        if not json_file.exists():
            try:
                # Extraire l'asset du nom du fichier
                parts = model_file.stem.split("_")
                if len(parts) >= 2:
                    asset = parts[1]
                    
                    # Créer un JSON minimal
                    json_data = {
                        "uid": asset,
                        "created_at": dt.datetime.utcnow().isoformat(),
                        "model_path": str(model_file),
                        "auto_generated": True,
                        "features": ["open", "high", "low", "close", "volume", 
                                    "open_z", "high_z", "low_z", "close_z", "volume_z"]
                    }
                    
                    # Essayer de charger les statistiques depuis le fichier parquet
                    try:
                        parquet_file = FEATURE_DIR / f"features-{asset}.parquet"
                        if parquet_file.exists():
                            df = pd.read_parquet(parquet_file)
                            features = [col for col in df.columns if col not in ["uid", "run_at"]]
                            json_data["features"] = features
                            
                            # Ajouter des statistiques de normalisation
                            json_data["norm_stats"] = {}
                            for col in df.columns:
                                if col not in ["uid", "run_at"] and not col.endswith("_z"):
                                    json_data["norm_stats"][col] = {
                                        "mean": float(df[col].mean()),
                                        "std": float(df[col].std() or 1.0)  # Éviter std=0
                                    }
                    except Exception as e:
                        logger.send_log("parquet_read_error", "warning", extra_labels={
                            "asset": asset,
                            "error": str(e)
                        })
                    
                    # Écrire le fichier JSON
                    with open(json_file, 'w') as f:
                        json.dump(json_data, f, indent=2)
                    
                    logger.send_log("json_created", "info", extra_labels={
                        "asset": asset,
                        "model": model_file.name,
                        "json": json_file.name
                    })
                    
                    missing_count += 1
            except Exception as e:
                logger.send_log("json_creation_failed", "error", extra_labels={
                    "model": model_file.name,
                    "error": str(e)
                })
    
    return missing_count

def _check_db_health(mongo: MongoUtils):
    """Vérifie l'état de la base de données et effectue des réparations si nécessaire."""
    # Liste des assets à vérifier
    DEFAULT_ASSETS = [
        "BTCEUR", "ETHEUR", "PIVXUSDT", "DOGEEUR", "PEPEEUR",
        "XRPEUR", "AVAXEUR", "MASKUSDT", "GALAUSDT", "RLCUSDT"
    ]
    
    repairs_count = 0
    
    for asset in DEFAULT_ASSETS:
        try:
            # Vérifier le nombre d'entrées dans forecast_features
            count = mongo.db["forecast_features"].count_documents({"uid": asset})
            
            if count < 10:  # Seuil minimal arbitraire
                logger.send_log("insufficient_db_entries", "warning", extra_labels={
                    "asset": asset, "count": count
                })
                
                # Tenter de réparer via sac_repair_features si disponible
                try:
                    subprocess.run(
                        [PY, "-m", "sac_repair_features", "--asset", asset],
                        check=True, timeout=300
                    )
                    repairs_count += 1
                except Exception as e:
                    # Fallback: créer des entrées minimales
                    try:
                        # Créer 20 entrées minimales
                        now = dt.datetime.utcnow()
                        entries = []
                        
                        for i in range(20):
                            timestamp = now - dt.timedelta(minutes=i*5)
                            entry = {
                                "uid": asset,
                                "created_at": timestamp,
                                "run_at": timestamp,
                                "open": 100 + i,
                                "high": 110 + i,
                                "low": 90 + i,
                                "close": 105 + i,
                                "volume": 1000 + i * 10,
                                "open_z": 0,
                                "high_z": 1,
                                "low_z": -1,
                                "close_z": 0.5,
                                "volume_z": 0
                            }
                            entries.append(entry)
                        
                        if entries:
                            mongo.db["forecast_features"].insert_many(entries)
                            logger.send_log("created_minimal_entries", "info", extra_labels={
                                "asset": asset, "count": len(entries)
                            })
                            repairs_count += 1
                    except Exception as e2:
                        logger.send_log("db_repair_failed", "error", extra_labels={
                            "asset": asset, "error": str(e2)
                        })
        except Exception as e:
            logger.send_log("db_check_failed", "error", extra_labels={
                "asset": asset, "error": str(e)
            })
    
    return repairs_count

def _check_feature_filenames():
    """Vérifie et corrige les noms des fichiers de features si nécessaire."""
    logger.send_log("checking_feature_filenames", "info")
    
    # Liste des assets à vérifier
    DEFAULT_ASSETS = [
        "BTCEUR", "ETHEUR", "PIVXUSDT", "DOGEEUR", "PEPEEUR",
        "XRPEUR", "AVAXEUR", "MASKUSDT", "GALAUSDT", "RLCUSDT"
    ]
    
    # Vérifier chaque asset
    missing_assets = []
    for asset in DEFAULT_ASSETS:
        feature_file = FEATURE_DIR / f"features-{asset}.parquet"
        if not feature_file.exists():
            missing_assets.append(asset)
    
    if missing_assets:
        logger.send_log("missing_feature_files", "warning", extra_labels={
            "assets": missing_assets
        })
        
        # Tenter de corriger les noms de fichiers
        try:
            import subprocess
            result = subprocess.run(
                [PY, "-m", "sac_fix_feature_names", "--assets"] + missing_assets,
                check=True,
                timeout=300,
                capture_output=True,
                text=True
            )
            logger.send_log("filename_correction_result", "info", extra_labels={
                "returncode": result.returncode,
                "stdout": result.stdout[:200] if result.stdout else ""
            })
        except Exception as e:
            logger.send_log("filename_correction_error", "error", extra_labels={
                "error": str(e)
            })
    
    return len(missing_assets)

# ═════════════════════ main cycle ══════════════════════════════
def _check_models_and_features(mongo: MongoUtils):
    """Vérifie et répare les modèles et features manquants."""
    # Vérifier les modèles manquants
    missing_models = _check_missing_models()
    
    # Vérifier les features manquantes
    missing_features = _check_missing_features(mongo)
    
    # Vérifier les fichiers JSON manquants
    missing_jsons = _check_missing_jsons()
    
    # Vérifier l'état de la base de données
    db_repairs = _check_db_health(mongo)
    
    # Journaliser les résultats
    logger.send_log("models_features_check", "info", extra_labels={
        "missing_models": missing_models,
        "missing_features": missing_features,
        "missing_jsons": missing_jsons,
        "db_repairs": db_repairs
    })
    
    return missing_models + missing_features + missing_jsons + db_repairs > 0

def _cycle(mongo: MongoUtils) -> None:
    """Exécute un cycle complet : export, vérification, entraînement."""
    try:
        # Vérifier si un export est nécessaire
        if not _run_export():
            return
        
        # Vérifier les modèles et les features
        _check_models_and_features(mongo)
        
        # Trouver un asset à entraîner
        pipeline = [
            {"$group": {"_id": "$uid", "n": {"$sum": 1}}},
            {"$match": {"n": {"$gt": TRAIN_EVERY_N_ROWS}}},
            {"$sample": {"size": 1}}
        ]
        
        for doc in mongo.db["forecast_features"].aggregate(pipeline):
            asset = doc["_id"]
    
            # ── décide si tuning Optuna nécessaire ───────────────────────
            last_perf = mongo.db["trade_strategy_perf"].find_one(
                {"uid": asset}, sort=[("timestamp", -1)]
            )
            
            # Vérifier si last_perf existe et contient la clé 'equity'
            tune_needed = True  # Par défaut, on fait le tuning
            
            try:
                if last_perf is not None and "equity" in last_perf:
                    tune_needed = last_perf["equity"] < 1.05
                else:
                    # Si last_perf est None ou ne contient pas 'equity'
                    logger.send_log("missing_equity_key", "warning", extra_labels={
                        "asset": asset,
                        "last_perf_keys": list(last_perf.keys()) if last_perf else None
                    })
            except Exception as e:
                logger.send_log("tune_decision_error", "error", extra_labels={
                    "asset": asset,
                    "error": str(e)
                })
                # Continuer avec la valeur par défaut
            
            # -------------------------------------------------------------
    
            logger.send_log("candidate", "debug",
                            extra_labels={"asset": asset, "rows": doc["n"],
                                          "tune": tune_needed})
            _spawn_training(asset, tune=tune_needed)
            break
    except Exception as e:
        logger.send_log("cycle_error", "error", extra_labels={
            "error": str(e),
            "traceback": traceback.format_exc()
        })

# ═════════════════════ entrypoint ══════════════════════════════
def main():
    """Point d'entrée principal."""
    # Journaliser le démarrage avec des informations système
    import platform, psutil
    system_info = {
        "python_version": platform.python_version(),
        "os": platform.platform(),
        "cpu_count": psutil.cpu_count(),
        "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "memory_available_gb": round(psutil.virtual_memory().available / (1024**3), 2)
    }
    logger.send_log("pipeline_started", "info", extra_labels=system_info)
    
    # Vérifier et créer les répertoires nécessaires
    ensure_directories()
    
    # — mode wrapper -------------------------------------------------
    if len(sys.argv) >= 3 and sys.argv[1] == "--_train_wrapper":
        asset = sys.argv[2]
        tune  = "--tune" in sys.argv[3:]
        _train_wrapper(asset, tune=tune)
        return

    # — regénération manuelle des .json -----------------------------
    if len(sys.argv) == 2 and sys.argv[1] == "--regenerate_jsons":
        _regenerate_missing_jsons()
        return

    # — daemon principal --------------------------------------------
    logger.send_log("daemon_started", "info")

    try:
        while True:
            _check_feature_filenames()
            _cycle(mongo)
            time.sleep(SLEEP_SECONDS)
    except KeyboardInterrupt:
        logger.send_log("stopped_gracefully", "info")


if __name__ == "__main__":
    main()
