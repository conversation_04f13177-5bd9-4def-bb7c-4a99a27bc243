import time
import sys
import json
import requests
import threading
from datetime import datetime, timedelta, timezone
from concurrent.futures import ThreadPoolExecutor
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils


SERVICE = "fetch_candlestick"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)

execution_lock = threading.Lock()


def datetime_serializer(obj):
    """Convertit les objets datetime et ObjectId en chaînes pour la sérialisation JSON"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    # Gérer les ObjectId de MongoDB
    if hasattr(obj, '__class__') and obj.__class__.__name__ == 'ObjectId':
        return str(obj)
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")



def fill_gaps_by_uid(params, uid):
    """
    Récupère et insère les données manquantes pour un uid donné via l'API /klines de Binance.
    """
    #logger.send_log(f"🔎 Vérification pour {uid} : {params}", "info")
    BASE_URL = "https://api.binance.com/api/v3/klines"
    results = []

    try:
        response = requests.get(BASE_URL, params=params, timeout=10)
        response.raise_for_status()
        klines = response.json()
        now = datetime.now(timezone.utc)

        for candle in klines:
            entry = {
                "openPrice": float(candle[1]),
                "highPrice": float(candle[2]),
                "lowPrice": float(candle[3]),
                "lastPrice": float(candle[4]),
                "volume": float(candle[5]),
                "quoteVolume": float(candle[7]),
                "openTime": datetime.fromtimestamp(candle[0] / 1000, tz=timezone.utc),
                "closeTime": datetime.fromtimestamp(candle[6] / 1000, tz=timezone.utc),
                "count": int(candle[8]),
                "uid": uid,
                "created_at": datetime.fromtimestamp(candle[0] / 1000, tz=timezone.utc),
                "updated_at": now
            }

            results.append(entry)

            # Envoi des prix à Grafana pour affichage sur graphique
            try:
                logger.send_raw_data_log(entry, metric="current_price")
            except Exception as e:
                logger.send_log(f"❌ - {uid} - Erreur lors de l'envoi du prix à Grafana : {e}", "error" )
                continue

        start_iso = datetime.fromtimestamp(params['startTime'] / 1000, tz=timezone.utc).isoformat()
        end_iso = datetime.fromtimestamp(params['endTime'] / 1000, tz=timezone.utc).isoformat()

        if len(klines) > 1:
            logger.send_log(f"⚠️ - {uid} - {len(klines)} bougies récupérées entre {start_iso} et {end_iso}", "warning")
        elif len(klines) == 0:
            logger.send_log(f"⚠️ - {uid} - {len(klines)} bougies récupérées entre {start_iso} et {end_iso}. Response from API = {klines}, params = {params}, uid = {uid}", "warning")

    except requests.exceptions.RequestException as e:
        logger.send_log(f"❌ - {uid} - Erreur API Binance (klines) : {e}", "error" )

    results = mongo.store_candlesticks(data=results)
    logger.send_log(f"✅ - {uid} - {len(results)} candlesticks insérés",level="info")
    #logger.send_log(f"⚠️ - {uid} - {results}", "debug")
    print(json.dumps(results, default=datetime_serializer), flush=True)
    return True




def check_data_gaps_by_uid(uid, now):
    """
    Détermine l'intervalle de temps à récupérer pour un uid donné.
    Si des données existent, commence à partir du dernier 'closeTime'.
    Sinon, récupère les 5 derniers jours.
    """
    try:
        # Définir les timestamps par défaut (en millisecondes)
        start_time = int((datetime.now(timezone.utc) - timedelta(days=5)).timestamp() * 1000)
        end_time = int((now.replace(tzinfo=timezone.utc) - timedelta(minutes=3)).timestamp() * 1000)

        # Chercher le dernier enregistrement pour ce uid
        last_entry = mongo.get_last_candlestick(uid)

        if last_entry and "closeTime" in last_entry:
            close_time = last_entry["closeTime"]
            if isinstance(close_time, datetime):
                if close_time.tzinfo is None:
                    close_time = close_time.replace(tzinfo=timezone.utc)
                start_time = int(close_time.timestamp() * 1000)
            else:
                start_time = int(close_time)  # Déjà en ms
            #start_time += 60_000  # Ajoute 1 minute
            start_time += 1  # Ajoute 1 ms

        if start_time > end_time:
            logger.send_log(f"✅ - {uid} - Aucun gap à combler : start_time > end_time", level="info")
            return []


        output = {
            "symbol": uid,
            "startTime": start_time,
            "endTime": end_time,
            "interval": "5m"
        }


        return fill_gaps_by_uid(output, uid)

    except Exception as e:
        logger.send_log(f"❌ - {uid} - Erreur lors de la vérification des gaps : {e}", "error")
        return []





def check_gaps_for_all_cryptos(now):
    """
    Vérifie les gaps pour toutes les cryptos actives en parallèle.
    Lance une tâche par crypto avec un print par crypto.
    """
    trading_pairs = mongo.get_trading_pairs()
    #logger.send_log(f"❌ - Trading Pair récupérées : {trading_pairs}", "debug")

    if not trading_pairs:
        logger.send_log("⚠️ Aucune paire active trouvée pour la vérification des gaps.", "warning")
        return False
    
    for uid in trading_pairs:
        check_data_gaps_by_uid(uid, now)

    # Lance une tâche par crypto en parallèle
    #with ThreadPoolExecutor(max_workers=1) as executor:
        #or uid in trading_pairs:
            #executor.submit(check_data_gaps_by_uid, uid, now)

    return True






def main_loop():
    """Boucle principale - exécute uniquement la vérification et le comblement des gaps toutes les 5 minutes"""
    #check_gaps_for_all_cryptos()
    # ⏳ Attendre jusqu'à la prochaine minute multiple de 5 avant de commencer
    logger.send_log(f"✅ - Service fetch_candlesticks démarré.", "info")

    now = datetime.now(timezone.utc)
    minute = (now.minute // 5 + 1) * 5
    if minute >= 60:
        first_run = now.replace(hour=(now.hour + 1) % 24, minute=0, second=0, microsecond=0)
    else:
        first_run = now.replace(minute=minute, second=0, microsecond=0)

    wait_seconds = max(0, (first_run - now).total_seconds())
    logger.send_log(f"🕒 - Attente initiale jusqu'à {first_run.strftime('%Y-%m-%d %H:%M:%S UTC')} pour démarrer...", "info")
    time.sleep(wait_seconds)

    while True:
        now = datetime.now(timezone.utc)

        # Aller à la prochaine minute multiple de 5
        minute = (now.minute // 5 + 1) * 5
        if minute >= 60:
            next_run = now.replace(hour=(now.hour + 1) % 24, minute=0, second=0, microsecond=0)
        else:
            next_run = now.replace(minute=minute, second=0, microsecond=0)

        # Exécuter uniquement la détection + comblement des gaps
        logger.send_log(f"🔁 - Cycle lancé à {now.strftime('%Y-%m-%d %H:%M:%S UTC')}", "info")
        # Vérification du verrou d'exécution
        if execution_lock.acquire(blocking=False):
            try:
                check_gaps_for_all_cryptos(now)
            finally:
                execution_lock.release()
        else:
            logger.send_log("❌ - Une exécution précédente est toujours en cours. Ce cycle est annulé.", "error")


        # Attente jusqu'à la prochaine minute multiple de 5
        sleep_time = max(0, (next_run - datetime.now(timezone.utc)).total_seconds())
        logger.send_log(f"🕒 - Attente de {sleep_time} secondes avant le prochain cycle...", "info")
        time.sleep(sleep_time)




if __name__ == "__main__":
    # sys.argv[0] est le nom du script, sys.argv[1] serait la première valeur après.
    if len(sys.argv) > 1:
        date_str = sys.argv[1]
        now = datetime.fromisoformat(sys.argv[1])
        check_gaps_for_all_cryptos(now)
    else:
        main_loop()

    #mongo.disconnect()

