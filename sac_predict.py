def _prepare_data_for_prediction(df, uid):
    """Prépare les données pour la prédiction."""
    try:
        # Vérifier si le DataFrame est vide
        if df.empty:
            logger.send_log("empty_dataframe", "warning", extra_labels={"uid": uid})
            return pd.DataFrame()
            
        # S'assurer que les colonnes requises sont présentes
        required_columns = ["uid", "run_at", "horizon_h", "expected_ret"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.send_log("adding_required_columns", "info", extra_labels={
                "uid": uid, "missing": missing_columns
            })
            
            # Ajouter les colonnes manquantes
            if "uid" not in df.columns:
                df["uid"] = uid
                
            if "run_at" not in df.columns:
                import datetime as dt
                df["run_at"] = dt.datetime.now()
                
            if "horizon_h" not in df.columns:
                df["horizon_h"] = 3  # Valeur par défaut
                
            if "expected_ret" not in df.columns:
                df["expected_ret"] = 0.0  # Valeur par défaut
        
        # Convertir les colonnes de date en timestamp
        if "run_at" in df.columns and not pd.api.types.is_numeric_dtype(df["run_at"]):
            try:
                df["run_at"] = pd.to_datetime(df["run_at"])
            except:
                # Si la conversion échoue, créer une nouvelle colonne avec la date actuelle
                import datetime as dt
                df["run_at"] = dt.datetime.now()
        
        return df
        
    except Exception as e:
        logger.send_log("prepare_data_error", "error", extra_labels={"uid": uid, "error": str(e)})
        return pd.DataFrame()