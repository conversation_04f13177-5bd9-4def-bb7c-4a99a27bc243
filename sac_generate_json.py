#!/usr/bin/env python3
"""
sac_generate_json.py - Génère les fichiers JSON de normalisation pour les features
"""

import os
import sys
import json
import pathlib
import pandas as pd
import datetime as dt
from common.grafana_utils import GrafanaUtils

# Configuration
FEATURE_DIR = pathlib.Path("data/features")
FEATURE_DIR.mkdir(parents=True, exist_ok=True)

# Logger
logger = GrafanaUtils(service="sac_generate_json")

def parse_args():
    """Parse les arguments de ligne de commande."""
    import argparse
    parser = argparse.ArgumentParser(description="Génère les fichiers JSON de normalisation")
    parser.add_argument("--force", action="store_true", help="Force la génération même si les fichiers existent déjà")
    parser.add_argument("--assets", nargs="+", help="Liste d'assets à traiter (par défaut: tous)")
    return parser.parse_args()

def generate_json_files(assets=None, force=False):
    """Génère les fichiers JSON de normalisation pour les features."""
    # Si aucun asset n'est spécifié, traiter tous les fichiers parquet
    if not assets:
        parquet_files = list(FEATURE_DIR.glob("features-*.parquet"))
        assets = [f.stem.replace("features-", "") for f in parquet_files]
    
    logger.send_log("generation_started", "info", extra_labels={"assets": len(assets)})
    
    processed_assets = []
    
    for asset in assets:
        parquet_file = FEATURE_DIR / f"features-{asset}.parquet"
        json_file = FEATURE_DIR / f"features-{asset}.norm.json"
        
        # Vérifier si le fichier JSON existe déjà
        if json_file.exists() and not force:
            logger.send_log("json_already_exists", "info", extra_labels={
                "asset": asset, "path": str(json_file)
            })
            continue
        
        # Vérifier si le fichier parquet existe
        if not parquet_file.exists():
            logger.send_log("parquet_not_found", "warning", extra_labels={
                "asset": asset, "path": str(parquet_file)
            })
            continue
        
        try:
            # Lire le fichier parquet
            df = pd.read_parquet(parquet_file)
            
            # Créer le dictionnaire JSON
            json_data = {
                "uid": asset,
                "created_at": dt.datetime.now(dt.timezone.utc).isoformat(),
                "features": [col for col in df.columns if col not in ["uid", "run_at"]],
                "norm_stats": {}
            }
            
            # Calculer les statistiques de normalisation
            for col in df.columns:
                if col not in ["uid", "run_at"] and not col.endswith("_z"):
                    json_data["norm_stats"][col] = {
                        "mean": float(df[col].mean()),
                        "std": float(df[col].std() or 1.0)  # Éviter std=0
                    }
            
            # Écrire le fichier JSON
            with open(json_file, 'w') as f:
                json.dump(json_data, f, indent=2)
            
            logger.send_log("json_created", "info", extra_labels={
                "asset": asset,
                "path": str(json_file),
                "stats_count": len(json_data["norm_stats"])
            })
            
            processed_assets.append(asset)
        except Exception as e:
            logger.send_log("json_generation_error", "error", extra_labels={
                "asset": asset, "error": str(e)
            })
    
    logger.send_log("generation_completed", "info", extra_labels={
        "processed": len(processed_assets),
        "total": len(assets)
    })
    
    return processed_assets

def main():
    args = parse_args()
    processed_assets = generate_json_files(assets=args.assets, force=args.force)
    
    print(f"✅ Génération terminée pour {len(processed_assets)} assets")
    if processed_assets:
        print("\nAssets traités:")
        for asset in processed_assets[:10]:  # Limiter à 10 pour éviter un affichage trop long
            print(f"- {asset}")
        if len(processed_assets) > 10:
            print(f"... et {len(processed_assets) - 10} autres")

if __name__ == "__main__":
    main()