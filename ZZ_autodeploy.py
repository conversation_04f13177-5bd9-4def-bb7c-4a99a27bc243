import subprocess
import time
import os
import sys
from common.grafana_utils import GrafanaUtils

REPO_PATH = "/home/<USER>/cryptobot"
BRANCH = "main"
CHECK_INTERVAL = 15  # secondes

SERVICE = "auto_deploy"
logger = GrafanaUtils(service=SERVICE)


# Config globale
SSH_KEY_PATH = "/home/<USER>/.ssh/id_ed25519"  # <- ta clé privée dédiée
GIT_ENV = os.environ.copy()
GIT_ENV["GIT_SSH_COMMAND"] = f"ssh -i {SSH_KEY_PATH} -o IdentitiesOnly=yes"


SERVICE_MAP = {
    #"compute_indicators": ["ZZ_compute_indicators.py", "common/mongo_utils.py", "common/grafana_utils.py", "common/indicators_utils.py"],
    #"predictions_grafana": ["ZZ_predictions_grafana.py", "common/mongo_utils.py", "common/grafana_utils.py"],
    #"fetch-candlesticks": ["ZZ_fetch_candlesticks.py", "common/mongo_utils.py", "common/grafana_utils.py"],
    #"optimize-models": ["ZZ_optimize_models.py", "common/mongo_utils.py", "common/grafana_utils.py", "common/models_utils.py", "common/indicators_utils.py"],
    "train-models": ["train_models.py", "common/mongo_utils.py", "common/grafana_utils.py", "common/models_utils.py", "common/indicators_utils.py"],
    "predict": ["predict.py", "common/mongo_utils.py", "common/grafana_utils.py", "common/indicators_utils.py"],
    #"optimize-indicators": ["optimize_indicators.py", "common/mongo_utils.py", "common/grafana_utils.py", "common/models_utils.py", "common/indicators_utils.py"],
    "news_taker": ["news_taker.py", "common/mongo_utils.py", "common/grafana_utils.py"],
    #"market-pairs": ["ZZ_market_pairs.py", "common/mongo_utils.py", "common/grafana_utils.py"],
    "whitelist": ["whitelist.py", "common/grafana_utils.py", "whitelist.json"],
    "auto-deploy": ["ZZ_autodeploy.py"],
    "sentiment_analysis": ["sentiment_analysis.py"],
    "audit_forecasts": ["audit_forecasts.py", "common/grafana_utils.py"],
    "sac_pipeline": ["sac_pipeline.py", "common/grafana_utils.py"],
    "sac_generate_signals": ["sac_generate_signals.py", "common/grafana_utils.py"],
    "sac_force_audit": ["sac_force_audit_all.py", "common/mongo_utils.py", "common/grafana_utils.py", "audit_forecasts.py"],
    "sac_backtest": ["sac_strategy_backtests.py", "common/mongo_utils.py", "common/grafana_utils.py"]
    #"error_rate": ["error_rate.py"]
}



def get_current_branch():
    try:
        result = subprocess.check_output(["git", "rev-parse", "--abbrev-ref", "HEAD"], cwd=REPO_PATH, env=GIT_ENV).decode()
        return result.strip()
    except subprocess.CalledProcessError:
        return "inconnu"

def get_remote_commit():
    result = subprocess.check_output(["git", "ls-remote", "origin", BRANCH], cwd=REPO_PATH, env=GIT_ENV).decode()
    return result.split()[0]

def get_local_commit():
    try:
        # HEAD = commit local actuel
        result = subprocess.check_output(["git", "rev-parse", "HEAD"], cwd=REPO_PATH, env=GIT_ENV).decode()
        return result.strip()
    except subprocess.CalledProcessError as e:
        logger.send_log(f"Erreur get_local_commit : {e}", "error")
        return None
    
def ensure_local_branch():
    try:
        branches = subprocess.check_output(["git", "branch", "--list", BRANCH], cwd=REPO_PATH, env=GIT_ENV).decode()
        if not branches.strip():
            subprocess.run(["git", "checkout", "-b", BRANCH, f"origin/{BRANCH}"], cwd=REPO_PATH, check=True, env=GIT_ENV)
    except subprocess.CalledProcessError as e:
        logger.send_log(f"🔥 Erreur lors de l'initialisation de la branche locale : {e}", "error")
        raise

def git_pull():
    subprocess.run(["git", "checkout", BRANCH], cwd=REPO_PATH, check=True, env=GIT_ENV)
    subprocess.run(["git", "pull", "origin", BRANCH], cwd=REPO_PATH, check=True, env=GIT_ENV)

def get_modified_files(old_commit, new_commit):
    try:
        result = subprocess.check_output(["git", "diff", "--name-only", old_commit, new_commit, "--"], cwd=REPO_PATH, env=GIT_ENV).decode()
        return result.strip().splitlines()
    except subprocess.CalledProcessError as e:
        logger.send_log(f"Erreur récupération fichiers modifiés : {e}", "error")
        return []

def detect_services_to_restart(modified_files):
    to_restart = set()
    for service, paths in SERVICE_MAP.items():
        for file in modified_files:
            if any(path in file for path in paths):
                to_restart.add(service)
    return list(to_restart)

def restart_services(services):
    for service in services:
        try:
            subprocess.run(["sudo", "systemctl", "restart", service], check=True)
            logger.send_log(f"🔁 Service redémarré : `{service}`", "info")
        except subprocess.CalledProcessError as e:
            logger.send_log(f"❌ Échec redémarrage `{service}` : {e}", "error")
            return

def loop_deploy():
    logger.send_log("✅ Auto-Deploy service started", "info")
    while True:
        try:
            subprocess.run(["git", "fetch", "origin"], cwd=REPO_PATH, check=True, env=GIT_ENV)
            ensure_local_branch()

            old_commit = get_local_commit()
            new_commit = get_remote_commit()

            if old_commit != new_commit:
                logger.send_log("🌀 Nouveau commit détecté, déploiement en cours...", "info")
                git_pull()

                # ⚠️ Le HEAD a changé après le pull → on utilise old_commit vs HEAD
                modified_files = get_modified_files(old_commit, "HEAD")

                logger.send_log(f"📝 Fichiers modifiés : {modified_files}", "info")
                services = detect_services_to_restart(modified_files)
                if services:
                    logger.send_log(f"🔧 Redémarrage des services : {services}", "info")
                    restart_services(services)
                else:
                    logger.send_log("✅ Aucun service concerné, pas de redémarrage", "info")

                current_branch = get_current_branch()
                logger.send_log(f"📌 Branche actuelle sur le Raspberry : `{current_branch}`", "info")
            time.sleep(CHECK_INTERVAL)

        except Exception as e:
            logger.send_log(f"🔥 Erreur dans la boucle de déploiement : {e}", "error")
            time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    loop_deploy()
