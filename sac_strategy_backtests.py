#!/usr/bin/env python3
"""
Script de backtest automatique pour les stratégies SAC
- Exécute des backtests réguliers sur tous les actifs
- Calcule des métriques de performance détaillées
- Stocke les résultats dans MongoDB
- Journalise les performances dans Grafana/Loki
"""

import pandas as pd
import numpy as np
import datetime as dt
import time
import traceback
from typing import Dict, List, Optional
from pathlib import Path

from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# Configuration
SIG_COLL = "trade_signals"
ARCHIVE = "trade_strategy_perf"
SLEEP_SECONDS = 3600  # Exécution toutes les heures

logger = GrafanaUtils(service="sac_backtest")
mongo = MongoUtils()
mongo.connect(service="sac_backtest")
DB = mongo.db  # Initialiser DB correctement

def _max_drawdown(equity_curve):
    """Calcule le drawdown maximum d'une courbe d'équité."""
    if len(equity_curve) == 0:
        return 0.0
    
    # Calcul du drawdown
    peak = equity_curve.expanding().max()
    drawdown = (equity_curve / peak - 1)
    
    return drawdown.min()

def _sharpe(returns, risk_free=0.0):
    """Calcule le ratio de Sharpe annualisé."""
    excess_returns = returns - risk_free
    
    if len(excess_returns) == 0 or excess_returns.std() == 0:
        return 0.0
    
    return excess_returns.mean() / excess_returns.std() * np.sqrt(252)

def _sortino(returns, risk_free=0.0, target_return=0.0):
    """Calcule le ratio de Sortino (comme Sharpe mais pénalise seulement la volatilité négative)."""
    excess_returns = returns - risk_free
    downside_returns = excess_returns[excess_returns < target_return]
    
    if len(downside_returns) == 0 or downside_returns.std() == 0:
        return 0.0
    
    return (excess_returns.mean() - target_return) / downside_returns.std() * np.sqrt(252)

def compute_metrics(df: pd.DataFrame, uid: str) -> Dict:
    """Calcule les métriques de performance pour un actif."""
    start_time = time.time()
    logger.send_log("computing_metrics_started", "info", extra_labels={
        "uid": uid, "rows": len(df)
    })
    
    try:
        # Vérifier les données
        required_columns = ["signal", "price", "created_at"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.send_log("missing_columns", "warning", extra_labels={
                "uid": uid, "missing": missing_columns
            })
            return {"uid": uid, "error": f"Missing columns: {missing_columns}"}
        
        # Trier par date
        df = df.sort_values("created_at")
        
        # Convertir les signaux textuels en valeurs numériques si nécessaire
        if df["signal"].dtype == object:  # Si signal est une chaîne de caractères
            # Mapper les signaux textuels en valeurs numériques
            signal_map = {"BUY": 1, "SELL": -1, "NEUTRAL": 0}
            # Convertir en majuscules pour gérer les variations de casse
            df["signal_numeric"] = df["signal"].str.upper().map(signal_map).fillna(0)
        else:
            # Si déjà numérique, utiliser tel quel
            df["signal_numeric"] = df["signal"]
        
        # Calculer les métriques
        df["equity"] = (1 + df["gain"]).cumprod()
        
        # Calcul des métriques détaillées
        total_return = float(df["equity"].iloc[-1] - 1)
        max_dd = _max_drawdown(df["equity"])
        
        # Calcul des rendements quotidiens pour les métriques avancées
        df["date"] = df["created_at"].dt.date
        daily_returns = df.groupby("date")["equity"].last().pct_change().fillna(0)
        
        # Métriques avancées
        sharpe = _sharpe(daily_returns)
        sortino = _sortino(daily_returns)
        calmar = round((total_return) / abs(max_dd) if max_dd else 0, 3)
        
        # Analyse des trades
        win_rate = sum(df["gain"] > 0) / len(df) if len(df) > 0 else 0
        avg_win = df[df["gain"] > 0]["gain"].mean() if sum(df["gain"] > 0) > 0 else 0
        avg_loss = df[df["gain"] < 0]["gain"].mean() if sum(df["gain"] < 0) > 0 else 0
        profit_factor = abs(sum(df[df["gain"] > 0]["gain"]) / sum(df[df["gain"] < 0]["gain"])) if sum(df[df["gain"] < 0]["gain"]) != 0 else float('inf')
        
        # Analyse des périodes de marché
        # Diviser en quartiles basés sur la volatilité
        df["rolling_vol"] = df["gain"].rolling(10).std().fillna(0)
        df["vol_quartile"] = pd.qcut(df["rolling_vol"], 4, labels=False, duplicates="drop").fillna(0)
        
        # Performance par quartile de volatilité
        vol_performance = {}
        for q in range(4):
            q_df = df[df["vol_quartile"] == q]
            if not q_df.empty:
                q_return = (1 + q_df["gain"]).prod() - 1
                vol_performance[f"q{q+1}_return"] = round(float(q_return), 4)
        
        # Journaliser les métriques calculées
        metrics_computed = {
            "uid": uid,
            "start_date": df["created_at"].min().isoformat(),
            "end_date": df["created_at"].max().isoformat(),
            "total_signals": len(df),
            "buy_signals": (df["signal_numeric"] > 0).sum(),  # Utiliser signal_numeric ici
            "sell_signals": (df["signal_numeric"] < 0).sum(),  # Utiliser signal_numeric ici
            "neutral_signals": (df["signal_numeric"] == 0).sum(),  # Utiliser signal_numeric ici
            "computation_time_ms": round((time.time() - start_time) * 1000, 2)
        }
        logger.send_log("metrics_computed", "info", extra_labels=metrics_computed)
        
        # Métriques finales
        metrics = {
            "uid"          : uid,
            "trades"       : len(df),
            "final_equity" : round(df["equity"].iloc[-1], 5),
            "mean_gain"    : round(df["gain"].mean(), 5),
            "sharpe"       : round(sharpe, 4),
            "sortino"      : round(sortino, 4),
            "max_dd"       : round(max_dd, 4),
            "calmar"       : calmar,
            "win_rate"     : round(win_rate, 4),
            "avg_win"      : round(avg_win, 5) if not pd.isna(avg_win) else 0,
            "avg_loss"     : round(avg_loss, 5) if not pd.isna(avg_loss) else 0,
            "profit_factor": round(profit_factor, 3),
            "duration_days": round(
                (df["created_at"].iloc[-1] - df["created_at"].iloc[0]
                ).total_seconds() / 86_400, 1),
            "timestamp"    : dt.datetime.utcnow(),
            **vol_performance  # Ajouter les performances par quartile de volatilité
        }
        return metrics
    except Exception as e:
        logger.send_log("metrics_computation_error", "error", extra_labels={
            "uid": uid, "error": str(e), "traceback": traceback.format_exc()[:500]
        })
        return {"uid": uid, "error": str(e)}

def run_backtest() -> None:
    """Exécute les backtests pour tous les actifs."""
    global DB, mongo
    
    # Vérifier que DB est valide
    if DB is None:
        logger.send_log("mongodb_connection_lost", "warning")
        mongo = MongoUtils()
        mongo.connect(service="sac_backtest")
        DB = mongo.db
        
        if DB is None:
            raise ConnectionError("Impossible de se connecter à MongoDB")
    
    try:
        uids = DB[SIG_COLL].distinct("uid")
        if not uids:
            logger.send_log("no_signals_found", "warning")
            return

        logger.send_log("backtest_started", "info",
                        extra_labels={"assets": len(uids)})

        for uid in uids:
            cur = DB[SIG_COLL].find({"uid": uid, "gain": {"$ne": None}})
            df  = pd.DataFrame(list(cur))
            if df.empty:
                logger.send_log("no_data_for_backtest","warning",
                                extra_labels={"uid":uid})
                continue

            try:
                metrics = compute_metrics(df, uid)
                DB[ARCHIVE].insert_one(metrics)
                logger.send_log("bt_res", "info", extra_labels=metrics)
            except Exception as e:
                logger.send_log("backtest_error","error",
                                extra_labels={"uid":uid,"err":str(e)})

        logger.send_log("backtest_complete","info")
    except Exception as e:
        logger.send_log("backtest_execution_error", "error", 
                       extra_labels={"error": str(e), "traceback": traceback.format_exc()[:500]})
        raise

def run_backtest_loop():
    """Exécute les backtests en boucle avec un intervalle défini."""
    global DB, mongo
    
    # S'assurer que la connexion MongoDB est établie
    if DB is None:
        logger.send_log("reconnecting_to_mongodb", "info")
        mongo = MongoUtils()
        mongo.connect(service="sac_backtest")
        DB = mongo.db
    
    logger.send_log("backtest_service_started", "info", 
                   extra_labels={"interval_seconds": SLEEP_SECONDS})
    
    while True:
        try:
            start_time = time.time()
            logger.send_log("backtest_cycle_started", "info")
            
            # Vérifier que DB est toujours valide
            if DB is None:
                logger.send_log("mongodb_connection_lost", "warning")
                mongo = MongoUtils()
                mongo.connect(service="sac_backtest")
                DB = mongo.db
                
                if DB is None:
                    raise ConnectionError("Impossible de se connecter à MongoDB")
            
            # Exécuter le backtest
            run_backtest()
            
            # Calculer le temps d'exécution
            execution_time = time.time() - start_time
            logger.send_log("backtest_cycle_completed", "info", 
                           extra_labels={"execution_time_seconds": round(execution_time, 2)})
            
            # Attendre jusqu'au prochain cycle
            sleep_time = max(1, SLEEP_SECONDS - execution_time)
            logger.send_log("waiting_for_next_cycle", "debug", 
                           extra_labels={"sleep_seconds": round(sleep_time, 2)})
            time.sleep(sleep_time)
        except Exception as e:
            logger.send_log("backtest_cycle_error", "error", 
                           extra_labels={"error": str(e), "traceback": traceback.format_exc()[:500]})
            time.sleep(60)  # Attendre 1 minute en cas d'erreur

if __name__ == "__main__":
    run_backtest_loop()
