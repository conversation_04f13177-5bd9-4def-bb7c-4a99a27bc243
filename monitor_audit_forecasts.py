#!/usr/bin/env python3
"""
Script de surveillance pour audit_forecasts
"""

import os
import sys
import time
import datetime
import subprocess
import traceback
from pathlib import Path

# Ajouter le répertoire courant au chemin Python
sys.path.append(os.getcwd())

# C<PERSON>er le répertoire de logs s'il n'existe pas
Path("/home/<USER>/cryptobot/logs").mkdir(exist_ok=True)

# Fichier de log pour le débogage
DEBUG_LOG = "/home/<USER>/cryptobot/logs/monitor_audit_forecasts.log"

def log_message(msg):
    """Écrire un message dans le fichier de log et sur la console."""
    timestamp = datetime.datetime.now().isoformat()
    full_msg = f"[{timestamp}] {msg}"
    print(full_msg)
    with open(DEBUG_LOG, "a") as f:
        f.write(full_msg + "\n")

def check_service_activity():
    """Vérifie si le service a produit des logs récemment."""
    try:
        # Vérifier les logs des 10 dernières minutes
        result = subprocess.run(
            ["journalctl", "-u", "audit_forecasts", "--since", "10 minutes ago", "--no-pager"],
            capture_output=True,
            text=True
        )
        
        logs = result.stdout.strip()
        log_lines = logs.split("\n") if logs else []
        
        # Compter les lignes non vides
        log_count = len([line for line in log_lines if line.strip()])
        
        # Vérifier spécifiquement les heartbeats
        heartbeat_count = len([line for line in log_lines if "keepalive_heartbeat" in line])
        
        log_message(f"Nombre de lignes de log dans les 10 dernières minutes: {log_count}")
        log_message(f"Nombre de heartbeats dans les 10 dernières minutes: {heartbeat_count}")
        
        # Si aucun heartbeat dans les 10 dernières minutes, le service est probablement bloqué
        if heartbeat_count == 0:
            log_message("Aucun heartbeat récent, le service est probablement bloqué")
            return False
            
        # Si moins de 5 lignes, le service pourrait être bloqué
        if log_count < 5:
            log_message("Peu de logs récents, le service pourrait être bloqué")
            return False
            
        return True
    except Exception as e:
        log_message(f"Erreur lors de la vérification des logs: {str(e)}")
        log_message(traceback.format_exc())
        return False

def restart_service():
    """Redémarre le service audit_forecasts."""
    try:
        log_message("Redémarrage du service audit_forecasts...")
        
        # Arrêter le service
        subprocess.run(["sudo", "systemctl", "stop", "audit_forecasts"])
        
        # Attendre que le service s'arrête
        time.sleep(5)
        
        # Vérifier s'il y a des processus zombies
        result = subprocess.run(
            ["pgrep", "-f", "audit_forecasts.py"],
            capture_output=True,
            text=True
        )
        
        if result.stdout.strip():
            log_message(f"Processus zombies trouvés: {result.stdout.strip()}")
            for pid in result.stdout.strip().split("\n"):
                if pid:
                    log_message(f"Arrêt forcé du processus {pid}...")
                    subprocess.run(["sudo", "kill", "-9", pid])
        
        # Démarrer le service
        subprocess.run(["sudo", "systemctl", "start", "audit_forecasts"])
        
        # Vérifier que le service est actif
        time.sleep(5)
        result = subprocess.run(
            ["systemctl", "is-active", "audit_forecasts"],
            capture_output=True,
            text=True
        )
        
        if result.stdout.strip() == "active":
            log_message("Service redémarré avec succès")
            return True
        else:
            log_message(f"Échec du redémarrage du service: {result.stdout.strip()}")
            return False
    except Exception as e:
        log_message(f"Erreur lors du redémarrage du service: {str(e)}")
        log_message(traceback.format_exc())
        return False

def main():
    """Fonction principale."""
    log_message("DÉBUT DE LA SURVEILLANCE")
    
    try:
        # Vérifier l'état du service
        result = subprocess.run(
            ["systemctl", "is-active", "audit_forecasts"],
            capture_output=True,
            text=True
        )
        
        service_status = result.stdout.strip()
        log_message(f"État du service: {service_status}")
        
        if service_status != "active":
            log_message("Service inactif, redémarrage...")
            restart_service()
        else:
            # Vérifier l'activité récente
            if not check_service_activity():
                log_message("Service inactif, redémarrage...")
                restart_service()
        
        log_message("SURVEILLANCE TERMINÉE")
    except Exception as e:
        log_message(f"ERREUR: {str(e)}")
        log_message(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
