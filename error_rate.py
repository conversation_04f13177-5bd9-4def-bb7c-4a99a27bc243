import time
from datetime import datetime
import pytz
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

SERVICE = "error_rate"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)

def trend_from_prices(start_price, end_price):
    return "up" if end_price > start_price else "down"

def quality_from_score(score):
    if score > 0.9:
        return "excellent"
    elif score > 0.6:
        return "good"
    elif score > 0.3:
        return "fair"
    elif score > 0:
        return "poor"
    else:
        return "bad"

def update_prediction_errors():
    predictions_col = mongo.db["predictions"]
    candles_col = mongo.db["candlesticks"]
    now = datetime.now(pytz.utc)

    predictions = predictions_col.find({
        "predicted_for_time": {"$lt": now},
        "error_pct": {"$exists": False}
    })

    for pred in predictions:
        try:
            # Ignore silencieusement les documents incomplets
            required_keys = ["uid", "horizon", "predicted_for_time", "future_price_estimated", "base_price"]
            if any(key not in pred for key in required_keys):
                continue

            uid = pred["uid"]
            horizon = pred["horizon"]
            predicted_time = pred["predicted_for_time"]
            predicted_price = pred["future_price_estimated"]
            base_price = pred["base_price"]

            candle = candles_col.find_one({
                "uid": uid,
                "openTime": {"$lte": predicted_time},
                "closeTime": {"$gte": predicted_time}
            })

            if not candle:
                continue  # Ignore aussi si pas de prix observé dispo

            observed_price = candle["lastPrice"]
            error_pct = ((observed_price - predicted_price) / predicted_price) * 100
            abs_error_pct = abs(error_pct)

            trend_predicted = trend_from_prices(base_price, predicted_price)
            trend_actual = trend_from_prices(base_price, observed_price)
            trend_respected = trend_predicted == trend_actual

            delta_pred = predicted_price - base_price
            delta_actual = observed_price - base_price
            trend_score = 0.0 if delta_pred == 0 or delta_actual == 0 else (delta_pred * delta_actual) / (abs(delta_pred) * abs(delta_actual))
            trend_quality = quality_from_score(trend_score)

            # Mise à jour en base
            update_fields = {
                "error_pct": error_pct,
                "abs_error_pct": abs_error_pct,
                "actual_price": observed_price,
                "evaluated_at": now,
                "trend_predicted": trend_predicted,
                "trend_actual": trend_actual,
                "trend_respected": trend_respected,
                "trend_score": trend_score,
                "trend_quality": trend_quality
            }
            predictions_col.update_one({"_id": pred["_id"]}, {"$set": update_fields})

            # Envoi Grafana uniquement avec JSON structuré
            evaluation_results = {
                "error_pct": round(error_pct, 4),
                "abs_error_pct": round(abs_error_pct, 4),
                "trend_score": round(trend_score, 4),
                "trend_quality": trend_quality,
                "trend_respected": trend_respected,
                "predicted_price": round(predicted_price, 2),
                "actual_price": round(observed_price, 2)
            }

            log_data = {
                "uid": uid,
                "horizon": horizon,
                "predicted_for_time": predicted_time.astimezone(pytz.utc).isoformat(),
                "evaluation_results": evaluation_results
            }

            logger.send_raw_data_log(log_data, metric="prediction_error", service=SERVICE)

        except Exception:
            pass  # Ne rien logguer, silence total si erreur

# Boucle infinie toutes les 5 min
if __name__ == "__main__":
    while True:
        update_prediction_errors()
        time.sleep(5 * 60)
