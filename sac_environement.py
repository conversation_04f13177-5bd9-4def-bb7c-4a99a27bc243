#!/usr/bin/env python3
# sac_environement.py – v2025-05-20

from __future__ import annotations
import gymnasium as gym
import numpy as np
import pandas as pd
from common.grafana_utils import GrafanaUtils
import psutil
import time

logger = GrafanaUtils(service="sac_environment")

# ───────────────────────── Hyper-paramètres globaux ──────────────────────────
DRAW_PENALTY_LAMBDA = 1e-3   # pénalité ∝ draw-down (réduite davantage)
DIR_BONUS           = 8e-3   # bonus si bonne direction (augmenté davantage)
PNL_BONUS_FACTOR    = 1.2    # facteur multiplicatif pour les PnL positifs
# -----------------------------------------------------------------------------

class MultiHorizonEnv(gym.Env):
    """
    Environnement de trading multi-horizon (Stable-Baselines 3 / SAC).

        action : vecteur d'allocations cibles ∈ [-1, +1] pour chaque horizon
        obs    : [ features_h1…hH , positions , cash ]
        reward : PnL – cost – λ·|pos| – drawdown + direction_bonus
    """

    metadata         = {"render_modes": []}
    REQUIRED_COLUMNS = {"uid", "run_at", "horizon_h", "expected_ret"}
    NON_NUMERIC_OK   = {"uid"}  # Colonnes non numériques autorisées

    # ──────────────────────────── ctor ───────────────────────────────────────
    def __init__(
        self,
        df_features : pd.DataFrame,
        horizons    = (3, 6, 9, 12, 15, 18, 21, 24),
        episode_steps : int   = 2_016,
        fee_bp       : float  = 8e-4,   # 0.08 % (aller / retour)
        spread_bp    : float  = 4e-4,   # 0.04 % half-spread
        slippage_bp  : float  = 1e-4,   # quadratique
        risk_penalty : float  = 1e-3,
    ):
        super().__init__()

        # Validation des données d'entrée
        if df_features.empty:
            raise ValueError("DataFrame vide fourni à l'environnement")

        # ── validation DataFrame ────────────────────────────────────────────
        # Ajouter les colonnes requises si elles sont manquantes
        for col in self.REQUIRED_COLUMNS:
            if col not in df_features.columns:
                if col == "uid":
                    # Essayer de trouver un uid dans le DataFrame
                    if hasattr(df_features, 'name') and df_features.name:
                        df_features[col] = df_features.name
                    else:
                        df_features[col] = "unknown"
                    logger.send_log("added_uid_column", "info", extra_labels={"uid": df_features[col].iloc[0]})
                elif col == "run_at":
                    import datetime as dt
                    df_features[col] = dt.datetime.now()
                    logger.send_log("added_run_at_column", "info")
                elif col == "horizon_h":
                    df_features[col] = horizons[0]  # Utiliser le premier horizon comme valeur par défaut
                    logger.send_log("added_horizon_h_column", "info", extra_labels={"value": horizons[0]})
                elif col == "expected_ret":
                    df_features[col] = 0.0  # Valeur par défaut
                    logger.send_log("added_expected_ret_column", "info")

        # Vérifier à nouveau les colonnes requises
        miss = self.REQUIRED_COLUMNS - set(df_features.columns)
        if miss:
            logger.send_log("missing_columns", "error", extra_labels={"missing": list(miss)})
            raise ValueError(f"Colonnes manquantes : {miss}")

        # Convertir les colonnes de date en timestamp
        datetime_cols = ["time", "closeTime", "created_at", "updated_at", "date", "timestamp", "run_at"]
        for col in datetime_cols:
            if col in df_features.columns and not pd.api.types.is_numeric_dtype(df_features[col]):
                try:
                    # Convertir en timestamp (secondes depuis l'epoch)
                    df_features[col] = pd.to_datetime(df_features[col]).astype(int) / 10**9
                    logger.send_log("converted_datetime", "info", extra_labels={"column": col})
                except Exception as e:
                    # Si la conversion échoue, supprimer la colonne
                    logger.send_log("datetime_conversion_failed", "warning", extra_labels={
                        "column": col, "error": str(e)
                    })
                    df_features = df_features.drop(columns=[col])

        # Vérifier les colonnes non numériques (sauf celles autorisées)
        non_num = [c for c in df_features.columns
                   if c not in self.NON_NUMERIC_OK
                   and not pd.api.types.is_numeric_dtype(df_features[c])]
        if non_num:
            logger.send_log("non_numeric_features", "warning", extra_labels={"cols": non_num})
            # Supprimer les colonnes non numériques au lieu de lever une exception
            df_features = df_features.drop(columns=non_num)
            logger.send_log("dropped_non_numeric", "info", extra_labels={"columns": non_num})

        if len(df_features) < (episode_steps + 1) * len(horizons):
            logger.send_log("dataset_too_small", "warning", extra_labels={
                "rows": len(df_features),
                "required": (episode_steps + 1) * len(horizons)
            })
            # Continuer avec les données disponibles au lieu de lever une exception
            episode_steps = min(episode_steps, len(df_features) // len(horizons) - 1)
            if episode_steps < 1:
                episode_steps = 1
                logger.send_log("adjusted_episode_steps", "warning", extra_labels={"new_value": episode_steps})

        self.feat_cols = [c for c in df_features.columns if c not in self.REQUIRED_COLUMNS]
        self.F = len(self.feat_cols)

        self.df = (df_features
                   .sort_values(["run_at", "horizon_h"])
                   .reset_index(drop=True))

        # Initialisation de l'attribut start
        self.start = 0
        
        # Initialisation des attributs manquants
        self.H = len(horizons)
        self.h = list(horizons)
        self.ep_steps = episode_steps
        self.fee = fee_bp
        self.spread = spread_bp
        self.slip = slippage_bp
        self.lambda_r = risk_penalty

        # espaces RL
        self.action_space = gym.spaces.Box(-1.0, 1.0, shape=(self.H,))
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(self.H * self.F + self.H + 1,),
            dtype=np.float32,
        )

    # ───────────────────── helpers internes ─────────────────────────────────
    def _state_feat(self, t: int) -> np.ndarray:
        """Extrait les features d'état à partir du DataFrame."""
        try:
            # Vérifier les limites d'indice
            start_idx = t * self.H
            end_idx = (t + 1) * self.H
            
            if start_idx >= len(self.df) or end_idx > len(self.df):
                logger.send_log("state_feat_index_error", "warning", extra_labels={
                    "t": t, "start_idx": start_idx, "end_idx": end_idx, "df_len": len(self.df)
                })
                # Utiliser un indice sûr
                start_idx = max(0, min(start_idx, len(self.df) - self.H))
                end_idx = min(start_idx + self.H, len(self.df))
            
            # Vérifier que les colonnes de features existent
            missing_feat_cols = [col for col in self.feat_cols if col not in self.df.columns]
            if missing_feat_cols:
                logger.send_log("missing_feature_columns", "warning", extra_labels={
                    "missing": missing_feat_cols
                })
                # Ajouter les colonnes manquantes avec des zéros
                for col in missing_feat_cols:
                    self.df[col] = 0.0
            
            # Extraire les features
            feat = (self.df
                    .iloc[start_idx:end_idx][self.feat_cols]
                    .to_numpy(dtype=np.float32, copy=False))
            
            # Vérifier la forme
            if len(feat) != self.H:
                logger.send_log("state_feat_wrong_rows", "warning", extra_labels={
                    "expected": self.H, "actual": len(feat)
                })
                # Ajuster la taille si nécessaire
                if len(feat) < self.H:
                    # Padding si trop petit
                    padding = np.zeros((self.H - len(feat), len(self.feat_cols)), dtype=np.float32)
                    feat = np.vstack([feat, padding])
                else:
                    # Troncature si trop grand
                    feat = feat[:self.H]
            
            # Nettoyer les valeurs NaN/Inf
            feat = np.nan_to_num(feat, nan=0.0, posinf=0.0, neginf=0.0)
            
            return feat  # Retourne une matrice de forme (H, F)
        except Exception as e:
            logger.send_log("state_feat_error", "error", extra_labels={"error": str(e)})
            return np.zeros((self.H, len(self.feat_cols)), dtype=np.float32)

    # ───────────────────────── interface Gym ────────────────────────────────
    def reset(self, seed=None, options=None):
        """Réinitialise l'environnement pour un nouvel épisode."""
        try:
            # Journaliser la réinitialisation
            reset_info = {
                "env_id": id(self),
                "seed": seed,
                "options": options,
                "memory_usage_mb": round(psutil.Process().memory_info().rss / (1024 * 1024), 2)
            }
            logger.send_log("env_reset", "info", extra_labels=reset_info)
            
            # Réinitialisation des variables d'état
            self.t = 0
            self.cash = 1.0
            self.nav_peak = 1.0
            self.pos = np.zeros(self.H, dtype=np.float32)
            self.episode_pnl = 0.0
            self.rewards = []  # Liste pour stocker les récompenses
            
            # Initialisation de l'attribut start manquant
            # Choisir un point de départ aléatoire dans les données
            # qui permet au moins un épisode complet
            max_start = max(0, len(self.df) // self.H - self.ep_steps - 1)
            if max_start > 0:
                self.start = self.np_random.integers(0, max_start)
            else:
                self.start = 0
                logger.send_log("small_dataset", "warning", extra_labels={
                    "df_len": len(self.df), "H": self.H, "ep_steps": self.ep_steps
                })
            
            # Récupération de l'observation initiale
            idx = self.start * self.H
            obs = self._get_obs(idx)
            
            # Vérification de la validité de l'observation
            if np.isnan(obs).any():
                logger.send_log("nan_in_observation", "warning", extra_labels={
                    "start": self.start, "nan_count": np.isnan(obs).sum()
                })
                # Remplacement des NaN par des zéros
                obs = np.nan_to_num(obs, nan=0.0)
            
            return obs, {}
        except Exception as e:
            logger.send_log("reset_error", "error", extra_labels={"error": str(e)})
            # Fallback à une réinitialisation sûre
            self.start = 0
            self.t = 0
            self.cash = 1.0
            self.nav_peak = 1.0
            self.pos = np.zeros(self.H, dtype=np.float32)
            self.episode_pnl = 0.0
            self.rewards = []
            return np.zeros(self.observation_space.shape, dtype=np.float32), {}

    # ────────────────────────────────────────────────────────────────────────
    def step(self, action):
        """Exécute une action dans l'environnement."""
        try:
            # 1) exécution du trade + coût réaliste
            tgt = np.clip(action, -1.0, 1.0)
            vol = np.abs(tgt - self.pos).sum()          # 0-1 scale

            # Utiliser self.fee au lieu de self.fee_bp
            fee    = vol * self.fee    * self.cash
            spread = vol * self.spread * self.cash
            slip   = (vol ** 2) * self.slip * self.cash / self.H
            cost   = fee + spread + slip

            self.cash -= cost
            self.pos   = tgt.copy()

            # 2) passage du temps + PnL à maturité
            self.t += 1
            idx  = self.start + self.t
            pnl  = 0.0

            # Vérification de fin d'épisode
            terminated = self.t >= self.ep_steps
            truncated = False
            
            # Vérification des horizons arrivés à maturité
            # Utiliser np.array(self.h) pour convertir la liste en array
            # et diviser chaque élément par 3
            maturity_periods = np.array([h // 3 for h in self.h])
            matured = np.array([self.t % period == 0 for period in maturity_periods if period > 0])
            
            if matured.any():
                try:
                    # Vérification des indices valides
                    if idx * self.H >= len(self.df) or (idx + 1) * self.H > len(self.df):
                        logger.send_log("index_out_of_bounds", "warning", extra_labels={
                            "t": self.t, "idx": idx, "df_len": len(self.df), "H": self.H
                        })
                        # Utilisation d'un indice sûr
                        safe_idx = min(idx, max(0, len(self.df) // self.H - 1))
                        rets = (self.df
                                .iloc[safe_idx * self.H : min((safe_idx + 1) * self.H, len(self.df))]
                                ["expected_ret"]
                                .to_numpy(np.float32))
                    else:
                        rets = (self.df
                                .iloc[idx * self.H : (idx + 1) * self.H]
                                ["expected_ret"]
                                .to_numpy(np.float32))
                
                    # S'assurer que rets a la bonne taille
                    if len(rets) < self.H:
                        # Padding avec des zéros
                        rets = np.pad(rets, (0, self.H - len(rets)), 'constant')
                    elif len(rets) > self.H:
                        # Troncature
                        rets = rets[:self.H]
                
                    # Calcul du PnL pour les horizons arrivés à maturité
                    # S'assurer que matured a la bonne taille
                    if len(matured) < self.H:
                        # Padding avec False
                        matured = np.pad(matured, (0, self.H - len(matured)), 'constant')
                    elif len(matured) > self.H:
                        # Troncature
                        matured = matured[:self.H]
                
                    pnl = (self.pos * rets * matured).sum()
                    self.cash += pnl
                
                    # Réinitialisation des positions arrivées à maturité
                    self.pos[matured] = 0.0
                
                except Exception as e:
                    logger.send_log("maturity_error", "warning", extra_labels={
                        "error": str(e), "t": self.t, "idx": idx
                    })

            # 3) reward shaping avec protection contre les valeurs extrêmes
            self.nav_peak = max(self.nav_peak, self.cash)
            dd_pen   = DRAW_PENALTY_LAMBDA * max(0.0, (self.nav_peak - self.cash) / self.nav_peak)
            risk_pen = self.lambda_r * np.abs(self.pos).sum()

            direction_bonus = 0.0
            try:
                # Vérification de l'indice valide
                if idx * self.H < len(self.df):
                    inst_ret = float(self.df.iloc[idx * self.H]["expected_ret"])
                    if not np.isnan(inst_ret):
                        # Bonus de direction augmenté et plus nuancé
                        sign_match = np.sign(inst_ret) == np.sign(self.pos)
                        direction_bonus = DIR_BONUS * sign_match.sum() / max(1, np.sum(self.pos != 0))
            except Exception as e:
                logger.send_log("direction_bonus_error", "warning", extra_labels={
                    "error": str(e), "t": self.t
                })

            # Calcul de la récompense avec clipping pour stabilité
            # Amplification des PnL positifs pour encourager les bonnes décisions
            if pnl > 0:
                pnl_reward = pnl * PNL_BONUS_FACTOR
            else:
                pnl_reward = pnl

            # Réduction de l'impact des pénalités pour encourager l'exploration
            raw_reward = pnl_reward - cost - 0.5 * risk_pen - 0.5 * dd_pen + direction_bonus
            
            # Détection des récompenses anormales
            if abs(raw_reward) > 0.1:  # Seuil arbitraire pour les récompenses très grandes
                logger.send_log("large_reward", "warning", extra_labels={
                    "t": self.t, "reward": float(raw_reward), "pnl": float(pnl),
                    "cost": float(cost), "risk_pen": float(risk_pen),
                    "dd_pen": float(dd_pen), "dir_bonus": float(direction_bonus)
                })
            
            # Clipping de la récompense pour éviter l'explosion du gradient
            reward = np.clip(raw_reward, -0.5, 0.5)

            # Stockage de la récompense pour les métriques de performance
            self.rewards.append(float(reward))
            
            self.episode_pnl += pnl

            # Log des récompenses importantes
            if abs(reward) > 0.01 or terminated:
                logger.send_log("env_reward", "debug", extra_labels={
                    "t": self.t, "reward": float(reward), "pnl": float(pnl), 
                    "cost": float(cost), "risk_pen": float(risk_pen),
                    "dd_pen": float(dd_pen), "dir_bonus": float(direction_bonus)
                })

            # 4) observation (plus de normalisation à chaud)
            obs = self._get_obs(idx)
            
            # 5) métriques de performance si épisode terminé
            if terminated:
                # Calculer les métriques de performance
                final_return = (self.cash - 1.0) * 100  # Pourcentage
                
                # Calculer le drawdown maximum
                if len(self.rewards) > 0:
                    cumulative_returns = np.cumsum(self.rewards)
                    peak = np.maximum.accumulate(cumulative_returns)
                    drawdown = peak - cumulative_returns
                    max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0
                else:
                    max_drawdown = 0
                
                # Calculer le ratio de Sharpe
                if len(self.rewards) > 1:
                    mean_reward = np.mean(self.rewards)
                    std_reward = np.std(self.rewards)
                    sharpe_ratio = mean_reward / std_reward if std_reward > 0 else 0
                else:
                    sharpe_ratio = 0
                
                # Log des métriques de performance
                logger.send_log("episode_performance", "info", extra_labels={
                    "total_pnl": float(self.episode_pnl),
                    "cash_end": float(self.cash),
                    "return": float(final_return),
                    "max_drawdown": float(max_drawdown),
                    "sharpe_ratio": float(sharpe_ratio),
                    "positive_rewards": float(sum(1 for r in self.rewards if r > 0) / max(1, len(self.rewards))),
                    "mean_reward": float(np.mean(self.rewards)) if self.rewards else 0.0
                })

            return obs, float(reward), terminated, truncated, {}
        except Exception as e:
            import traceback
            logger.send_log("step_error", "error", extra_labels={
                "error": str(e),
                "traceback": traceback.format_exc()[:500]  # Limiter la taille
            })
            # Retourner une observation sûre en cas d'erreur
            safe_obs = np.zeros(self.observation_space.shape, dtype=np.float32)
            return safe_obs, 0.0, True, True, {}

    # -----------------------------------------------------------------------
    def render(self, mode="human"):
        print(f"t={self.t:04d}  NAV={self.cash: .4f}  pos={self.pos}")

    def _get_obs(self, idx: int) -> np.ndarray:
        """Construit l'observation complète à partir de l'indice temporel."""
        try:
            # Extraire les features d'état
            features = self._state_feat(idx)
            
            # Vérification de la forme des features
            if features.shape != (self.H, self.F):
                logger.send_log("features_shape_mismatch", "warning", extra_labels={
                    "expected": f"({self.H}, {self.F})", 
                    "actual": str(features.shape),
                    "idx": idx
                })
                
                # Correction de la forme si nécessaire
                if len(features) < self.H:
                    # Padding avec des zéros
                    padding = np.zeros((self.H - len(features), self.F), dtype=np.float32)
                    features = np.vstack([features, padding])
                elif len(features) > self.H:
                    # Troncature
                    features = features[:self.H]
                    
                if features.shape[1] != self.F:
                    # Recréer un tableau de la bonne forme
                    features = np.zeros((self.H, self.F), dtype=np.float32)
            
            # Aplatissement des features pour les concaténer avec les positions et le cash
            flat_features = features.reshape(-1)
            
            # Concaténation des features, positions et cash
            obs = np.concatenate([flat_features, self.pos, [self.cash]])
            
            # Vérification de la forme finale
            expected_shape = self.observation_space.shape[0]
            if len(obs) != expected_shape:
                logger.send_log("obs_shape_mismatch", "error", extra_labels={
                    "actual": len(obs), "expected": expected_shape,
                    "features_shape": features.shape, "pos_shape": self.pos.shape,
                    "idx": idx
                })
                
                # Correction de la forme
                if len(obs) < expected_shape:
                    # Padding avec des zéros
                    obs = np.pad(obs, (0, expected_shape - len(obs)), 'constant')
                else:
                    # Troncature
                    obs = obs[:expected_shape]
            
            return obs
        except Exception as e:
            import traceback
            logger.send_log("get_obs_error", "error", extra_labels={
                "error": str(e), "idx": idx, "traceback": traceback.format_exc()
            })
            # Retourner une observation par défaut en cas d'erreur
            return np.zeros(self.observation_space.shape[0], dtype=np.float32)

# ───────────────────────── helper CLI/debug ────────────────────────────────
def make_env_from_parquet(path: str, asset: str, **kw) -> MultiHorizonEnv:
    df = pd.read_parquet(path)
    df_asset = df[df["uid"] == asset]
    if df_asset.empty:
        raise ValueError(f"Aucune ligne trouvée pour {asset} dans {path}")
    return MultiHorizonEnv(df_asset.copy(), **kw)

class TradingEnv(gym.Env):
    def __init__(self, df, initial_cash=1.0, commission=0.001, slippage=0.0005, 
                 reward_scaling=1.0, use_position_features=True, risk_penalty=0.1):
        super().__init__()
        self.df = df
        self.initial_cash = initial_cash
        self.commission = commission
        self.slippage = slippage
        self.reward_scaling = reward_scaling
        self.use_position_features = use_position_features
        self.risk_penalty = risk_penalty  # Pénalité pour le risque excessif
        
        # Espaces d'observation et d'action améliorés
        n_features = len(df.columns)
        if self.use_position_features:
            n_features += 3  # Position actuelle, P&L, durée de la position
        
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(n_features,), dtype=np.float32
        )
        
        # Action continue entre -1 (vente max) et 1 (achat max)
        self.action_space = gym.spaces.Box(
            low=-1, high=1, shape=(1,), dtype=np.float32
        )
        
        # Métriques pour le suivi des performances
        self.metrics = {
            "trades": 0,
            "profitable_trades": 0,
            "max_drawdown": 0,
            "sharpe_ratio": 0,
            "volatility": 0
        }
        
        self.reset()
    
    def reset(self, **kwargs):
        # Réinitialisation de l'environnement
        self.current_step = 0
        self.cash = self.initial_cash
        self.position = 0
        self.entry_price = 0
        self.nav_peak = self.initial_cash
        self.episode_pnl = 0
        self.rewards = []
        self.position_duration = 0
        self.returns = []  # Pour calculer Sharpe et volatilité
        
        # Réinitialiser les métriques
        self.metrics = {
            "trades": 0,
            "profitable_trades": 0,
            "max_drawdown": 0,
            "sharpe_ratio": 0,
            "volatility": 0
        }
        
        return self._get_observation(), {}
    
    def _get_observation(self):
        # Récupérer les features du marché
        features = self.df.iloc[self.current_step].values
        
        if self.use_position_features:
            # Ajouter des features sur la position actuelle
            position_features = np.array([
                self.position,  # Position actuelle (-1, 0, 1)
                self._calculate_unrealized_pnl(),  # P&L non réalisé
                self.position_duration / 100.0  # Durée normalisée
            ], dtype=np.float32)
            
            return np.concatenate([features, position_features])
        
        return features
    
    def _calculate_unrealized_pnl(self):
        if self.position == 0:
            return 0.0
            
        current_price = self.df.iloc[self.current_step]['close']
        if self.position > 0:
            return (current_price / self.entry_price) - 1.0
        else:
            return 1.0 - (current_price / self.entry_price)
    
    def step(self, action):
        # Récupérer le prix actuel
        current_price = self.df.iloc[self.current_step]['close']
        
        # Convertir l'action continue en décision de trading
        new_position = np.clip(action[0], -1.0, 1.0)
        
        # Calculer le changement de position
        position_change = new_position - self.position
        
        # Exécuter le trade si nécessaire
        reward = 0
        if abs(position_change) > 0.1:  # Seuil minimal pour trader
            # Simuler le slippage
            execution_price = current_price * (1 + self.slippage * np.sign(position_change))
            
            # Calculer le P&L si on ferme une position existante
            if self.position != 0:
                if self.position > 0:
                    pnl = (execution_price / self.entry_price) - 1.0
                else:
                    pnl = 1.0 - (execution_price / self.entry_price)
                
                # Appliquer les frais
                pnl -= self.commission
                
                # Mettre à jour le cash
                self.cash *= (1 + pnl * abs(self.position))
                
                # Enregistrer le trade
                self.metrics["trades"] += 1
                if pnl > 0:
                    self.metrics["profitable_trades"] += 1
                
                # Réinitialiser la durée de position
                self.position_duration = 0
            
            # Ouvrir la nouvelle position
            if abs(new_position) > 0.1:
                self.position = new_position
                self.entry_price = execution_price
                
                # Appliquer les frais d'entrée
                self.cash *= (1 - self.commission)
            else:
                self.position = 0
                self.entry_price = 0
        
        # Mettre à jour les métriques
        self.nav_peak = max(self.nav_peak, self.cash)
        current_drawdown = 1.0 - (self.cash / self.nav_peak)
        self.metrics["max_drawdown"] = max(self.metrics["max_drawdown"], current_drawdown)
        
        # Calculer la récompense (multi-objectif)
        # 1. Rendement instantané
        if self.position != 0:
            price_change = self.df.iloc[self.current_step+1]['close'] / current_price - 1
            instant_return = price_change * self.position
            reward += instant_return
        
        # 2. Pénalité pour le risque excessif
        risk_penalty = self.risk_penalty * current_drawdown
        reward -= risk_penalty
        
        # 3. Bonus pour les positions rentables
        if self._calculate_unrealized_pnl() > 0:
            reward += 0.001  # Petit bonus pour encourager les positions rentables
        
        # 4. Pénalité pour le trading excessif
        if abs(position_change) > 0.1:
            reward -= 0.0005  # Petite pénalité pour décourager le trading excessif
        
        # Mettre à jour les statistiques
        self.returns.append(reward)
        if len(self.returns) > 1:
            self.metrics["volatility"] = np.std(self.returns)
            if self.metrics["volatility"] > 0:
                self.metrics["sharpe_ratio"] = np.mean(self.returns) / self.metrics["volatility"]
        
        # Mettre à jour les compteurs
        self.current_step += 1
        if self.position != 0:
            self.position_duration += 1
        
        # Vérifier si l'épisode est terminé
        done = self.current_step >= len(self.df) - 1
        
        # Appliquer le scaling de la récompense
        scaled_reward = reward * self.reward_scaling
        self.rewards.append(scaled_reward)
        
        return self._get_observation(), float(scaled_reward), done, False, self.metrics
