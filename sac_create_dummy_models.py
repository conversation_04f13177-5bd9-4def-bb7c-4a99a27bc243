#!/usr/bin/env python3
# sac_create_dummy_models.py - Crée des modèles SAC minimaux pour les UIDs sans modèles

import os
import sys
import time
import pathlib
import argparse
import json
import datetime as dt
import tempfile
import shutil
import numpy as np
import pandas as pd
import gym
from stable_baselines3 import SAC

from common.mongo_utils import MongoUtils, CRYPTO_TEST
from common.grafana_utils import GrafanaUtils

# Configuration
MODEL_DIR = pathlib.Path("/home/<USER>/cryptobot/models")
PARQUET_DIR = pathlib.Path("data/features")

# Créer les répertoires s'ils n'existent pas
MODEL_DIR.mkdir(parents=True, exist_ok=True)
PARQUET_DIR.mkdir(parents=True, exist_ok=True)

# Logger
logger = GrafanaUtils(service="sac_create_models")

# Connexion MongoDB
mongo = MongoUtils(logger=logger)
mongo.connect(service="sac_create_models")

def parse_args():
    """Parse les arguments de ligne de commande."""
    parser = argparse.ArgumentParser(description="Crée des modèles SAC minimaux")
    parser.add_argument("--asset", type=str, help="Asset spécifique pour lequel créer un modèle")
    parser.add_argument("--force", action="store_true", help="Force la création même si un modèle existe déjà")
    return parser.parse_args()

def get_assets_to_process():
    """Récupère la liste des assets à traiter."""
    args = parse_args()
    
    if args.asset:
        logger.send_log("single_asset_mode", "info", extra_labels={"asset": args.asset})
        return [args.asset]
    
    try:
        # Récupérer les UIDs depuis MongoDB
        uids = set(mongo.db["candlesticks"].distinct("uid"))
        
        # Si aucun UID n'est trouvé, utiliser la liste par défaut
        if not uids:
            logger.send_log("using_default_assets", "warning")
            return CRYPTO_TEST
            
        logger.send_log("assets_found", "info", extra_labels={
            "count": len(uids),
            "assets": list(uids)[:10]  # Limiter à 10 pour éviter des logs trop grands
        })
        return list(uids)
    except Exception as e:
        logger.send_log("get_assets_error", "error", extra_labels={"error": str(e)})
        return CRYPTO_TEST

class DummyEnv(gym.Env):
    """Environnement minimal pour créer un modèle SAC."""
    def __init__(self):
        self.action_space = gym.spaces.Box(low=-1, high=1, shape=(1,))
        self.observation_space = gym.spaces.Box(low=-np.inf, high=np.inf, shape=(10,))
        
    def reset(self):
        return np.zeros(10)
        
    def step(self, action):
        return np.zeros(10), 0, True, {}

def create_model_for_asset(uid: str, force: bool = False) -> bool:
    """Crée un modèle SAC minimal pour un asset spécifique."""
    try:
        logger.send_log("creating_model", "info", extra_labels={"uid": uid})
        
        # Vérifier si un modèle existe déjà
        existing_models = list(MODEL_DIR.glob(f"sac_{uid}_*.zip"))
        if existing_models and not force:
            logger.send_log("model_already_exists", "info", extra_labels={
                "uid": uid,
                "models": [m.name for m in existing_models]
            })
            
            # Vérifier si le JSON associé existe
            for model_path in existing_models:
                json_path = model_path.with_suffix(".json")
                if not json_path.exists():
                    # Créer le JSON manquant
                    create_json_for_model(uid, model_path)
                    
            return True
            
        # Créer un répertoire temporaire
        with tempfile.TemporaryDirectory() as temp_dir:
            # Créer un modèle SAC minimal
            model = SAC("MlpPolicy", DummyEnv())
            
            # Générer un nom de fichier avec la date
            model_filename = f"sac_{uid}_{dt.datetime.utcnow().strftime('%Y%m%d')}.zip"
            temp_model_path = pathlib.Path(temp_dir) / model_filename
            
            # Sauvegarder le modèle
            model.save(temp_model_path)
            
            # Copier le modèle dans le répertoire des modèles
            final_model_path = MODEL_DIR / model_filename
            shutil.copy(temp_model_path, final_model_path)
            
            # Créer le JSON associé
            create_json_for_model(uid, final_model_path)
            
            logger.send_log("model_created", "info", extra_labels={
                "uid": uid,
                "model": final_model_path.name,
                "size_bytes": final_model_path.stat().st_size
            })
            
            return True
    except Exception as e:
        logger.send_log("create_model_error", "error", extra_labels={
            "uid": uid,
            "error": str(e)
        })
        return False

def create_json_for_model(uid: str, model_path: pathlib.Path) -> bool:
    """Crée un fichier JSON pour un modèle."""
    try:
        json_path = model_path.with_suffix(".json")
        
        # Vérifier si le JSON existe déjà
        if json_path.exists():
            logger.send_log("json_already_exists", "info", extra_labels={
                "uid": uid,
                "json": json_path.name
            })
            return True
            
        # Créer un JSON minimal
        json_data = {
            "uid": uid,
            "created_at": dt.datetime.utcnow().isoformat(),
            "model_path": str(model_path),
            "dummy": True,
            "norm_stats": {}
        }
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
            
        logger.send_log("json_created", "info", extra_labels={
            "uid": uid,
            "json": json_path.name
        })
        return True
    except Exception as e:
        logger.send_log("create_json_error", "error", extra_labels={
            "uid": uid,
            "error": str(e)
        })
        return False        