#!/usr/bin/env python3
"""
sac_fix_feature_names.py - Corrige les noms des fichiers de features
"""

import os
import sys
import pathlib
import pandas as pd
import datetime as dt
import traceback
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# Configuration
FEATURE_DIR = pathlib.Path("data/features")
FEATURE_DIR.mkdir(parents=True, exist_ok=True)

# Logger
logger = GrafanaUtils(service="sac_fix_features")

# MongoDB
mongo = MongoUtils()
mongo.connect(service="sac_fix_features")

def parse_args():
    """Parse les arguments de ligne de commande."""
    import argparse
    parser = argparse.ArgumentParser(description="Corrige les noms des fichiers de features")
    parser.add_argument("--force", action="store_true", help="Force le renommage même si les fichiers existent déjà")
    parser.add_argument("--assets", nargs="+", help="Liste d'assets à traiter (par défaut: tous)")
    parser.add_argument("--create-missing", action="store_true", help="Crée les fichiers manquants à partir de MongoDB")
    return parser.parse_args()

def get_assets_to_process(specified_assets=None):
    """Récupère la liste des assets à traiter."""
    if specified_assets:
        return specified_assets
    
    # Récupérer tous les UIDs depuis MongoDB
    try:
        uids = set(mongo.db["candlesticks"].distinct("uid"))
        if not uids:
            logger.send_log("no_uids_found", "warning")
            return []
        
        logger.send_log("uids_found", "info", extra_labels={"count": len(uids)})
        return list(uids)
    except Exception as e:
        logger.send_log("get_uids_error", "error", extra_labels={"error": str(e)})
        return []

def create_feature_file_from_mongo(asset):
    """Crée un fichier de features à partir des données MongoDB."""
    try:
        logger.send_log("creating_from_mongo", "info", extra_labels={"asset": asset})
        
        # Récupérer les bougies
        candles = list(mongo.db["candlesticks"].find(
            {"uid": asset},
            sort=[("openTime", -1)],
            limit=2000
        ))
        
        if not candles:
            logger.send_log("no_candles_found", "warning", extra_labels={"asset": asset})
            return False
        
        logger.send_log("candles_found", "info", extra_labels={
            "asset": asset, "count": len(candles)
        })
        
        # Convertir en DataFrame
        df = pd.DataFrame(candles)
        
        # Renommer les colonnes
        if "openTime" in df.columns:
            df = df.rename(columns={
                "openTime": "time",
                "openPrice": "open",
                "highPrice": "high",
                "lowPrice": "low",
                "lastPrice": "close",
                "volume": "volume"
            })
        
        # Ajouter l'UID à chaque ligne
        df["uid"] = asset
        
        # Ajouter un timestamp run_at
        df["run_at"] = dt.datetime.now(dt.timezone.utc)
        
        # Sauvegarder en parquet
        target_file = FEATURE_DIR / f"features-{asset}.parquet"
        df.to_parquet(target_file, index=False)
        
        logger.send_log("file_created", "info", extra_labels={
            "asset": asset,
            "path": str(target_file),
            "size_bytes": target_file.stat().st_size,
            "rows": len(df)
        })
        
        return True
    except Exception as e:
        logger.send_log("create_file_error", "error", extra_labels={
            "asset": asset,
            "error": str(e),
            "traceback": traceback.format_exc()[:500]
        })
        return False

def rename_feature_files(assets=None, force=False, create_missing=False):
    """Renomme les fichiers de features avec le format correct."""
    assets_to_process = get_assets_to_process(assets)
    
    if not assets_to_process:
        logger.send_log("no_assets_to_process", "warning")
        return []
    
    logger.send_log("rename_started", "info", extra_labels={"assets": len(assets_to_process)})
    
    # Récupérer tous les fichiers de features avec l'ancien format (basé sur la date)
    old_files = list(FEATURE_DIR.glob("features-2*.parquet"))
    logger.send_log("old_files_found", "info", extra_labels={"count": len(old_files)})
    
    # Fichiers déjà traités
    processed_assets = set()
    created_assets = set()
    
    # Traiter chaque asset
    for asset in assets_to_process:
        target_file = FEATURE_DIR / f"features-{asset}.parquet"
        
        # Vérifier si le fichier existe déjà
        if target_file.exists() and not force:
            logger.send_log("file_already_exists", "info", extra_labels={
                "asset": asset, "path": str(target_file)
            })
            processed_assets.add(asset)
            continue
        
        # Chercher dans les fichiers existants
        asset_data = None
        source_file = None
        
        for file in old_files:
            try:
                # Lire le fichier parquet
                df = pd.read_parquet(file)
                
                # Vérifier si l'asset est présent dans le fichier
                if "uid" in df.columns and asset in df["uid"].unique():
                    # Filtrer les données pour cet asset
                    asset_data = df[df["uid"] == asset]
                    source_file = file
                    break
            except Exception as e:
                logger.send_log("read_error", "warning", extra_labels={
                    "file": str(file), "error": str(e)
                })
        
        if asset_data is not None and len(asset_data) > 0:
            # Sauvegarder les données dans le nouveau fichier
            try:
                asset_data.to_parquet(target_file, index=False)
                logger.send_log("file_renamed", "info", extra_labels={
                    "asset": asset,
                    "source": str(source_file),
                    "target": str(target_file),
                    "rows": len(asset_data)
                })
                processed_assets.add(asset)
            except Exception as e:
                logger.send_log("save_error", "error", extra_labels={
                    "asset": asset, "error": str(e)
                })
        elif create_missing:
            # Créer le fichier à partir des données MongoDB
            if create_feature_file_from_mongo(asset):
                created_assets.add(asset)
        else:
            logger.send_log("asset_not_found", "warning", extra_labels={"asset": asset})
    
    # Rapport final
    logger.send_log("rename_completed", "info", extra_labels={
        "processed": len(processed_assets),
        "created": len(created_assets),
        "missing": len(assets_to_process) - len(processed_assets) - len(created_assets)
    })
    
    return list(processed_assets.union(created_assets))

def main():
    args = parse_args()
    processed_assets = rename_feature_files(
        assets=args.assets, 
        force=args.force,
        create_missing=args.create_missing
    )
    
    print(f"✅ Traitement terminé pour {len(processed_assets)} assets")
    if processed_assets:
        print("\nAssets traités:")
        for asset in processed_assets[:10]:  # Limiter à 10 pour éviter un affichage trop long
            print(f"- {asset}")
        if len(processed_assets) > 10:
            print(f"... et {len(processed_assets) - 10} autres")

if __name__ == "__main__":
    main()
