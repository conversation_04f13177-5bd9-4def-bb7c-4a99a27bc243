#!/usr/bin/env python3
# sac_initialize_assets.py - Initialise les modèles et features pour les assets

import os
import sys
import time
import pathlib
import subprocess
import json
from datetime import datetime, timezone
import pandas as pd
import re

from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# Répertoires
PARQUET_DIR = pathlib.Path("data/features")
MODEL_DIR = pathlib.Path("/home/<USER>/cryptobot/models")

# Créer les répertoires s'ils n'existent pas
PARQUET_DIR.mkdir(parents=True, exist_ok=True)
MODEL_DIR.mkdir(parents=True, exist_ok=True)

# Logger
logger = GrafanaUtils(service="sac_initialize")

# Connexion MongoDB
mongo = MongoUtils(logger=logger)
mongo.connect(service="sac_initialize")

def get_assets_to_initialize():
    """Récupère la liste des assets à initialiser."""
    try:
        # Récupérer les UIDs depuis MongoDB (plusieurs sources)
        uids = set()
        
        # 1. Depuis forecast_features
        uids.update(mongo.db["forecast_features"].distinct("uid"))
        
        # 2. Depuis candlesticks
        uids.update(mongo.db["candlesticks"].distinct("uid"))
        
        # 3. Depuis market_pairs
        uids.update(mongo.db["market_pairs"].distinct("symbol"))
        
        # 4. Ajouter la liste par défaut dans tous les cas
        default_assets = CRYPTO_TEST if "CRYPTO_TEST" in globals() else ["BTCEUR", "ETHEUR", "XRPEUR", "BNBEUR", "ADAEUR"]
        uids.update(default_assets)
        
        # Filtrer les UIDs invalides
        valid_uids = [uid for uid in uids if re.match(r"^[A-Z]{2,6}(EUR|USD|USDT)$", uid)]
            
        logger.send_log("assets_found", "info", extra_labels={
            "count": len(valid_uids),
            "assets": valid_uids[:15]  # Limiter à 15 pour éviter des logs trop grands
        })
        
        return valid_uids
    except Exception as e:
        logger.send_log("get_assets_error", "error", extra_labels={"error": str(e)})
        # Fallback sur une liste par défaut
        return CRYPTO_TEST if "CRYPTO_TEST" in globals() else ["BTCEUR", "ETHEUR"]

def export_features(asset):
    """Exporte les features pour un asset."""
    try:
        logger.send_log("exporting_features", "info", extra_labels={"asset": asset})
        
        # Vérifier si le fichier existe déjà
        parquet_file = PARQUET_DIR / f"features-{asset}.parquet"
        if parquet_file.exists():
            logger.send_log("features_already_exist", "info", extra_labels={
                "asset": asset,
                "path": str(parquet_file)
            })
            return True
            
        # Exécuter le script d'export
        result = subprocess.run(
            ["python", "-m", "sac_export_features", "--asset", asset],
            capture_output=True,
            text=True,
            timeout=600  # 10 minutes max
        )
        
        if result.returncode == 0 and parquet_file.exists():
            logger.send_log("features_exported", "info", extra_labels={"asset": asset})
            return True
        else:
            logger.send_log("features_export_failed", "error", extra_labels={
                "asset": asset,
                "returncode": result.returncode,
                "stdout": result.stdout[:200],
                "stderr": result.stderr[:200]
            })
            return False
    except Exception as e:
        logger.send_log("export_features_error", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
        return False

def train_model(asset):
    """Entraîne un modèle pour un asset."""
    try:
        logger.send_log("training_model", "info", extra_labels={"asset": asset})
        
        # Vérifier si un modèle existe déjà
        existing_models = list(MODEL_DIR.glob(f"sac_{asset}_*.zip"))
        if existing_models:
            logger.send_log("model_already_exists", "info", extra_labels={
                "asset": asset,
                "models": [m.name for m in existing_models]
            })
            return True
            
        # Exécuter le script d'entraînement
        result = subprocess.run(
            [
                "python", "sac_train_rl.py",
                "--asset", asset,
                "--ep_steps", "1000",
                "--steps", "100000",  # Réduit pour l'initialisation
                "--tune"  # Activer le tuning pour trouver les meilleurs hyperparamètres
            ],
            capture_output=True,
            text=True,
            timeout=3600  # 1 heure max
        )
        
        # Vérifier si un modèle a été créé
        new_models = list(MODEL_DIR.glob(f"sac_{asset}_*.zip"))
        if new_models and (result.returncode == 0 or len(new_models) > len(existing_models)):
            logger.send_log("model_trained", "info", extra_labels={
                "asset": asset,
                "models": [m.name for m in new_models]
            })
            return True
        else:
            logger.send_log("model_training_failed", "error", extra_labels={
                "asset": asset,
                "returncode": result.returncode,
                "stdout": result.stdout[:200],
                "stderr": result.stderr[:200]
            })
            return False
    except Exception as e:
        logger.send_log("train_model_error", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
        return False

def create_minimal_json(asset):
    """Crée un fichier JSON minimal pour un modèle."""
    try:
        # Trouver le modèle le plus récent
        models = sorted(MODEL_DIR.glob(f"sac_{asset}_*.zip"), reverse=True)
        if not models:
            logger.send_log("no_model_for_json", "warning", extra_labels={"asset": asset})
            return False
            
        model_path = models[0]
        json_path = model_path.with_suffix(".json")
        
        # Vérifier si le JSON existe déjà
        if json_path.exists():
            logger.send_log("json_already_exists", "info", extra_labels={
                "asset": asset,
                "json": json_path.name
            })
            return True
            
        # Créer un JSON minimal
        json_data = {
            "uid": asset,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "model_path": str(model_path),
            "auto_generated": True,
            "norm_stats": {}
        }
        
        # Essayer de charger les statistiques depuis le fichier parquet
        try:
            parquet_file = PARQUET_DIR / f"features-{asset}.parquet"
            if parquet_file.exists():
                df = pd.read_parquet(parquet_file)
                for col in df.columns:
                    if col != "run_at" and not col.endswith("_z"):
                        json_data["norm_stats"][col] = {
                            "mean": float(df[col].mean()),
                            "std": float(df[col].std() or 1.0)  # Éviter std=0
                        }
        except Exception as e:
            logger.send_log("stats_calculation_error", "warning", extra_labels={
                "asset": asset,
                "error": str(e)
            })
            
        # Écrire le JSON
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
            
        logger.send_log("json_created", "info", extra_labels={
            "asset": asset,
            "json": json_path.name,
            "stats_count": len(json_data["norm_stats"])
        })
        return True
    except Exception as e:
        logger.send_log("create_json_error", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
        return False

def generate_signal(asset):
    """Génère un signal initial pour un asset."""
    try:
        logger.send_log("generating_signal", "info", extra_labels={"asset": asset})
        
        # Exécuter le script de génération de signaux
        result = subprocess.run(
            ["python", "sac_generate_signals.py", "--asset", asset, "--force"],
            capture_output=True,
            text=True,
            timeout=300  # 5 minutes max
        )
        
        if result.returncode == 0:
            logger.send_log("signal_generated", "info", extra_labels={"asset": asset})
            return True
        else:
            logger.send_log("signal_generation_failed", "warning", extra_labels={
                "asset": asset,
                "returncode": result.returncode,
                "stdout": result.stdout[:200],
                "stderr": result.stderr[:200]
            })
            return False
    except Exception as e:
        logger.send_log("generate_signal_error", "error", extra_labels={
            "asset": asset,
            "error": str(e)
        })
        return False

def main():
    logger.send_log("initialization_started", "info")
    
    # Récupérer les assets à initialiser
    assets = get_assets_to_initialize()
    logger.send_log("assets_to_initialize", "info", extra_labels={
        "count": len(assets),
        "assets": assets
    })
    
    results = {
        "success": [],
        "failed": []
    }
    
    # Traiter chaque asset
    for asset in assets:
        try:
            logger.send_log("processing_asset", "info", extra_labels={"asset": asset})
            
            # Étape 1: Exporter les features
            if not export_features(asset):
                logger.send_log("asset_initialization_failed", "error", extra_labels={
                    "asset": asset,
                    "step": "export_features"
                })
                results["failed"].append(asset)
                continue
                
            # Étape 2: Entraîner le modèle
            if not train_model(asset):
                logger.send_log("asset_initialization_failed", "error", extra_labels={
                    "asset": asset,
                    "step": "train_model"
                })
                results["failed"].append(asset)
                continue
                
            # Étape 3: Créer le JSON
            if not create_minimal_json(asset):
                logger.send_log("asset_initialization_failed", "error", extra_labels={
                    "asset": asset,
                    "step": "create_json"
                })
                results["failed"].append(asset)
                continue
                
            # Étape 4: Générer un signal initial
            if not generate_signal(asset):
                logger.send_log("asset_initialization_failed", "warning", extra_labels={
                    "asset": asset,
                    "step": "generate_signal"
                })
                # Ne pas considérer comme un échec complet si seule la génération de signal échoue
                
            # Asset initialisé avec succès
            logger.send_log("asset_initialized", "info", extra_labels={"asset": asset})
            results["success"].append(asset)
            
        except Exception as e:
            logger.send_log("asset_processing_error", "error", extra_labels={
                "asset": asset,
                "error": str(e)
            })
            results["failed"].append(asset)
    
    # Résumé
    logger.send_log("initialization_complete", "info", extra_labels={
        "success_count": len(results["success"]),
        "failed_count": len(results["failed"]),
        "success": results["success"],
        "failed": results["failed"]
    })

if __name__ == "__main__":
    main()
