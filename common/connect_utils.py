import requests
import websockets
import asyncio
import json
import time
import threading
from typing import Optional, Dict, Any, Union, Callable
from datetime import datetime, timezone
from common.grafana_utils import GrafanaUtils


class ConnectUtils:
    """
    Classe utilitaire pour gérer les connexions HTTP et WebSocket vers localhost:1880
    """

    def __init__(self, service="connect_utils", default_timeout=30, host="localhost", port=1880):
        """
        Initialise la classe ConnectUtils

        Args:
            service (str): Nom du service pour les logs
            default_timeout (int): Timeout par défaut en secondes
            host (str): Host de destination (par défaut localhost)
            port (int): Port de destination (par défaut 1880)
        """
        self.logger = GrafanaUtils(service=service)
        self.default_timeout = default_timeout
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.base_ws_url = f"ws://{host}:{port}"
        self.session = requests.Session()
        self.websocket_connections = {}
        self.websocket_listeners = {}

        # Headers par défaut
        self.default_headers = {
            'User-Agent': 'CryptoBot-ConnectUtils/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }

        self.logger.send_log(f"🔧 ConnectUtils initialisé pour {self.base_url}", "info")

    def __del__(self):
        """Nettoyage lors de la destruction de l'objet"""
        self.close_all_websockets()
        if hasattr(self, 'session'):
            self.session.close()

    #####################################################################################################
    # FONCTIONS HTTP
    #####################################################################################################

    def send_get_request(self, endpoint: str, params: Optional[Dict] = None,
                        headers: Optional[Dict] = None, timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Envoie une requête GET HTTP vers le serveur configuré (par défaut localhost:1880)

        Args:
            endpoint (str): Endpoint de destination (ex: "/api/data")
            params (dict, optional): Paramètres de requête
            headers (dict, optional): Headers personnalisés
            timeout (int, optional): Timeout en secondes

        Returns:
            dict: Réponse avec status_code, data, headers, etc.
        """
        return self.send_request('GET', endpoint, params=params, headers=headers, timeout=timeout)

    def send_post_request(self, endpoint: str, data: Optional[Union[Dict, str]] = None,
                         json_data: Optional[Dict] = None, headers: Optional[Dict] = None,
                         timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Envoie une requête POST HTTP vers le serveur configuré (par défaut localhost:1880)

        Args:
            endpoint (str): Endpoint de destination (ex: "/api/create")
            data (dict/str, optional): Données à envoyer (form data)
            json_data (dict, optional): Données JSON à envoyer
            headers (dict, optional): Headers personnalisés
            timeout (int, optional): Timeout en secondes

        Returns:
            dict: Réponse avec status_code, data, headers, etc.
        """
        return self.send_request('POST', endpoint, data=data, json_data=json_data,
                               headers=headers, timeout=timeout)

    def send_put_request(self, endpoint: str, data: Optional[Union[Dict, str]] = None,
                        json_data: Optional[Dict] = None, headers: Optional[Dict] = None,
                        timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Envoie une requête PUT HTTP vers le serveur configuré (par défaut localhost:1880)

        Args:
            endpoint (str): Endpoint de destination (ex: "/api/update")
            data (dict/str, optional): Données à envoyer (form data)
            json_data (dict, optional): Données JSON à envoyer
            headers (dict, optional): Headers personnalisés
            timeout (int, optional): Timeout en secondes

        Returns:
            dict: Réponse avec status_code, data, headers, etc.
        """
        return self.send_request('PUT', endpoint, data=data, json_data=json_data,
                               headers=headers, timeout=timeout)

    def send_delete_request(self, endpoint: str, headers: Optional[Dict] = None,
                           timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Envoie une requête DELETE HTTP vers le serveur configuré (par défaut localhost:1880)

        Args:
            endpoint (str): Endpoint de destination (ex: "/api/delete")
            headers (dict, optional): Headers personnalisés
            timeout (int, optional): Timeout en secondes

        Returns:
            dict: Réponse avec status_code, data, headers, etc.
        """
        return self.send_request('DELETE', endpoint, headers=headers, timeout=timeout)

    def send_request(self, method: str, endpoint: str, params: Optional[Dict] = None,
                    data: Optional[Union[Dict, str]] = None, json_data: Optional[Dict] = None,
                    headers: Optional[Dict] = None, timeout: Optional[int] = None,
                    allow_redirects: bool = True) -> Dict[str, Any]:
        """
        Méthode générique pour envoyer des requêtes HTTP vers le serveur configuré (par défaut localhost:1880)

        Args:
            method (str): Méthode HTTP (GET, POST, PUT, DELETE, etc.)
            endpoint (str): Endpoint de destination (ex: "/api/data")
            params (dict, optional): Paramètres de requête
            data (dict/str, optional): Données à envoyer (form data)
            json_data (dict, optional): Données JSON à envoyer
            headers (dict, optional): Headers personnalisés
            timeout (int, optional): Timeout en secondes
            allow_redirects (bool): Autoriser les redirections

        Returns:
            dict: Réponse structurée avec toutes les informations
        """
        start_time = time.time()
        timeout = timeout or self.default_timeout

        # Construction de l'URL complète
        if not endpoint.startswith('/'):
            endpoint = '/' + endpoint
        full_url = f"{self.base_url}{endpoint}"

        # Fusion des headers
        request_headers = self.default_headers.copy()
        if headers:
            request_headers.update(headers)

        try:
            self.logger.send_log(f"🌐 {method} {full_url}", "debug")

            response = self.session.request(
                method=method,
                url=full_url,
                params=params,
                data=data,
                json=json_data,
                headers=request_headers,
                timeout=timeout,
                allow_redirects=allow_redirects
            )

            duration = time.time() - start_time

            # Tentative de parsing JSON
            try:
                response_data = response.json()
            except (ValueError, json.JSONDecodeError):
                response_data = response.text

            result = {
                'success': response.status_code < 400,
                'status_code': response.status_code,
                'data': response_data,
                'headers': dict(response.headers),
                'url': response.url,
                'duration': duration,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            if result['success']:
                self.logger.send_log(f"✅ {method} {full_url} - {response.status_code} ({duration:.2f}s)", "info")
            else:
                self.logger.send_log(f"❌ {method} {full_url} - {response.status_code} ({duration:.2f}s)", "warning")
                result['error'] = f"HTTP {response.status_code}: {response.reason}"

            return result

        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            error_msg = f"Timeout après {timeout}s"
            self.logger.send_log(f"⏰ {method} {full_url} - {error_msg}", "error")
            return {
                'success': False,
                'error': error_msg,
                'duration': duration,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

        except requests.exceptions.ConnectionError as e:
            duration = time.time() - start_time
            error_msg = f"Erreur de connexion: {str(e)}"
            self.logger.send_log(f"🔌 {method} {full_url} - {error_msg}", "error")
            return {
                'success': False,
                'error': error_msg,
                'duration': duration,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"Erreur inattendue: {str(e)}"
            self.logger.send_log(f"💥 {method} {full_url} - {error_msg}", "error")
            return {
                'success': False,
                'error': error_msg,
                'duration': duration,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

    #####################################################################################################
    # FONCTIONS WEBSOCKET
    #####################################################################################################

    async def connect_websocket(self, endpoint: str, connection_id: str = "default",
                               headers: Optional[Dict] = None,
                               ping_interval: Optional[int] = 20,
                               ping_timeout: Optional[int] = 10) -> Dict[str, Any]:
        """
        Établit une connexion WebSocket vers le serveur configuré (par défaut localhost:1880)

        Args:
            endpoint (str): Endpoint WebSocket (ex: "/ws/data")
            connection_id (str): Identifiant unique pour la connexion
            headers (dict, optional): Headers personnalisés
            ping_interval (int, optional): Intervalle de ping en secondes
            ping_timeout (int, optional): Timeout de ping en secondes

        Returns:
            dict: Résultat de la connexion
        """
        try:
            # Construction de l'URL WebSocket complète
            if not endpoint.startswith('/'):
                endpoint = '/' + endpoint
            full_ws_url = f"{self.base_ws_url}{endpoint}"

            self.logger.send_log(f"🔌 Connexion WebSocket à {full_ws_url}", "info")

            websocket = await websockets.connect(
                full_ws_url,
                extra_headers=headers,
                ping_interval=ping_interval,
                ping_timeout=ping_timeout
            )

            self.websocket_connections[connection_id] = websocket

            self.logger.send_log(f"✅ WebSocket connecté: {connection_id}", "info")

            return {
                'success': True,
                'connection_id': connection_id,
                'url': full_ws_url,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            error_msg = f"Erreur connexion WebSocket: {str(e)}"
            self.logger.send_log(f"❌ {error_msg}", "error")
            return {
                'success': False,
                'error': error_msg,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

    async def send_websocket_message(self, message: Union[str, Dict],
                                   connection_id: str = "default") -> Dict[str, Any]:
        """
        Envoie un message via WebSocket

        Args:
            message (str/dict): Message à envoyer
            connection_id (str): Identifiant de la connexion

        Returns:
            dict: Résultat de l'envoi
        """
        try:
            if connection_id not in self.websocket_connections:
                return {
                    'success': False,
                    'error': f"Connexion WebSocket '{connection_id}' non trouvée"
                }

            websocket = self.websocket_connections[connection_id]

            # Conversion en JSON si nécessaire
            if isinstance(message, dict):
                message = json.dumps(message)

            await websocket.send(message)

            self.logger.send_log(f"📤 Message envoyé via WebSocket {connection_id}", "debug")

            return {
                'success': True,
                'connection_id': connection_id,
                'message_sent': True,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            error_msg = f"Erreur envoi WebSocket: {str(e)}"
            self.logger.send_log(f"❌ {error_msg}", "error")
            return {
                'success': False,
                'error': error_msg,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

    async def receive_websocket_message(self, connection_id: str = "default",
                                      timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Reçoit un message via WebSocket

        Args:
            connection_id (str): Identifiant de la connexion
            timeout (int, optional): Timeout en secondes

        Returns:
            dict: Message reçu et métadonnées
        """
        try:
            if connection_id not in self.websocket_connections:
                return {
                    'success': False,
                    'error': f"Connexion WebSocket '{connection_id}' non trouvée"
                }

            websocket = self.websocket_connections[connection_id]

            if timeout:
                message = await asyncio.wait_for(websocket.recv(), timeout=timeout)
            else:
                message = await websocket.recv()

            # Tentative de parsing JSON
            try:
                parsed_message = json.loads(message)
            except (ValueError, json.JSONDecodeError):
                parsed_message = message

            self.logger.send_log(f"📥 Message reçu via WebSocket {connection_id}", "debug")

            return {
                'success': True,
                'connection_id': connection_id,
                'message': parsed_message,
                'raw_message': message,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

        except asyncio.TimeoutError:
            return {
                'success': False,
                'error': f"Timeout lors de la réception (WebSocket {connection_id})",
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            error_msg = f"Erreur réception WebSocket: {str(e)}"
            self.logger.send_log(f"❌ {error_msg}", "error")
            return {
                'success': False,
                'error': error_msg,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

    async def close_websocket(self, connection_id: str = "default") -> Dict[str, Any]:
        """
        Ferme une connexion WebSocket

        Args:
            connection_id (str): Identifiant de la connexion

        Returns:
            dict: Résultat de la fermeture
        """
        try:
            if connection_id not in self.websocket_connections:
                return {
                    'success': False,
                    'error': f"Connexion WebSocket '{connection_id}' non trouvée"
                }

            websocket = self.websocket_connections[connection_id]
            await websocket.close()

            # Arrêter le listener s'il existe
            if connection_id in self.websocket_listeners:
                self.websocket_listeners[connection_id]['stop'] = True
                del self.websocket_listeners[connection_id]

            del self.websocket_connections[connection_id]

            self.logger.send_log(f"🔌 WebSocket fermé: {connection_id}", "info")

            return {
                'success': True,
                'connection_id': connection_id,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            error_msg = f"Erreur fermeture WebSocket: {str(e)}"
            self.logger.send_log(f"❌ {error_msg}", "error")
            return {
                'success': False,
                'error': error_msg,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

    def close_all_websockets(self):
        """Ferme toutes les connexions WebSocket"""
        for connection_id in list(self.websocket_connections.keys()):
            asyncio.create_task(self.close_websocket(connection_id))

    async def websocket_listener(self, connection_id: str, message_handler: Callable,
                               error_handler: Optional[Callable] = None,
                               auto_reconnect: bool = True,
                               reconnect_delay: int = 5) -> None:
        """
        Écoute en continu les messages WebSocket

        Args:
            connection_id (str): Identifiant de la connexion
            message_handler (callable): Fonction appelée pour chaque message reçu
            error_handler (callable, optional): Fonction appelée en cas d'erreur
            auto_reconnect (bool): Reconnexion automatique en cas de déconnexion
            reconnect_delay (int): Délai avant reconnexion en secondes
        """
        self.websocket_listeners[connection_id] = {'stop': False}

        while not self.websocket_listeners[connection_id]['stop']:
            try:
                if connection_id not in self.websocket_connections:
                    if auto_reconnect:
                        self.logger.send_log(f"🔄 Tentative de reconnexion WebSocket {connection_id}", "warning")
                        await asyncio.sleep(reconnect_delay)
                        continue
                    else:
                        break

                result = await self.receive_websocket_message(connection_id)

                if result['success']:
                    try:
                        await message_handler(result['message'], connection_id)
                    except Exception as e:
                        self.logger.send_log(f"❌ Erreur dans message_handler: {str(e)}", "error")
                        if error_handler:
                            await error_handler(e, connection_id)
                else:
                    if error_handler:
                        await error_handler(result.get('error', 'Erreur inconnue'), connection_id)

                    if auto_reconnect:
                        await asyncio.sleep(reconnect_delay)
                    else:
                        break

            except Exception as e:
                self.logger.send_log(f"❌ Erreur dans websocket_listener: {str(e)}", "error")
                if error_handler:
                    await error_handler(e, connection_id)

                if auto_reconnect:
                    await asyncio.sleep(reconnect_delay)
                else:
                    break

        self.logger.send_log(f"🛑 WebSocket listener arrêté: {connection_id}", "info")

    #####################################################################################################
    # MÉTHODES UTILITAIRES
    #####################################################################################################

    def set_default_headers(self, headers: Dict[str, str]):
        """
        Définit les headers par défaut pour les requêtes HTTP

        Args:
            headers (dict): Headers à définir par défaut
        """
        self.default_headers.update(headers)
        self.logger.send_log("🔧 Headers par défaut mis à jour", "debug")

    def set_target_server(self, host: str = "localhost", port: int = 1880):
        """
        Change le serveur de destination

        Args:
            host (str): Nouveau host (par défaut localhost)
            port (int): Nouveau port (par défaut 1880)
        """
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.base_ws_url = f"ws://{host}:{port}"
        self.logger.send_log(f"🔧 Serveur de destination changé pour {self.base_url}", "info")

    def get_connection_status(self, connection_id: str = "default") -> Dict[str, Any]:
        """
        Obtient le statut d'une connexion WebSocket

        Args:
            connection_id (str): Identifiant de la connexion

        Returns:
            dict: Statut de la connexion
        """
        if connection_id in self.websocket_connections:
            websocket = self.websocket_connections[connection_id]
            return {
                'connected': not websocket.closed,
                'connection_id': connection_id,
                'state': str(websocket.state),
                'has_listener': connection_id in self.websocket_listeners
            }
        else:
            return {
                'connected': False,
                'connection_id': connection_id,
                'error': 'Connexion non trouvée'
            }

    def list_connections(self) -> Dict[str, Any]:
        """
        Liste toutes les connexions WebSocket actives

        Returns:
            dict: Liste des connexions et leur statut
        """
        connections = {}
        for connection_id in self.websocket_connections:
            connections[connection_id] = self.get_connection_status(connection_id)

        return {
            'total_connections': len(connections),
            'connections': connections,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

    #####################################################################################################
    # MÉTHODES SYNCHRONES POUR WEBSOCKET (WRAPPERS)
    #####################################################################################################

    def connect_websocket_sync(self, endpoint: str, connection_id: str = "default",
                              headers: Optional[Dict] = None) -> Dict[str, Any]:
        """Version synchrone de connect_websocket"""
        return asyncio.run(self.connect_websocket(endpoint, connection_id, headers))

    def send_websocket_message_sync(self, message: Union[str, Dict],
                                   connection_id: str = "default") -> Dict[str, Any]:
        """Version synchrone de send_websocket_message"""
        return asyncio.run(self.send_websocket_message(message, connection_id))

    def receive_websocket_message_sync(self, connection_id: str = "default",
                                      timeout: Optional[int] = None) -> Dict[str, Any]:
        """Version synchrone de receive_websocket_message"""
        return asyncio.run(self.receive_websocket_message(connection_id, timeout))

    def close_websocket_sync(self, connection_id: str = "default") -> Dict[str, Any]:
        """Version synchrone de close_websocket"""
        return asyncio.run(self.close_websocket(connection_id))

    def start_websocket_listener_thread(self, connection_id: str, message_handler: Callable,
                                       error_handler: Optional[Callable] = None,
                                       auto_reconnect: bool = True) -> threading.Thread:
        """
        Démarre un listener WebSocket dans un thread séparé

        Args:
            connection_id (str): Identifiant de la connexion
            message_handler (callable): Fonction appelée pour chaque message
            error_handler (callable, optional): Fonction appelée en cas d'erreur
            auto_reconnect (bool): Reconnexion automatique

        Returns:
            threading.Thread: Thread du listener
        """
        def run_listener():
            asyncio.run(self.websocket_listener(connection_id, message_handler,
                                              error_handler, auto_reconnect))

        thread = threading.Thread(target=run_listener, daemon=True)
        thread.start()

        self.logger.send_log(f"🎯 WebSocket listener démarré dans un thread: {connection_id}", "info")

        return thread