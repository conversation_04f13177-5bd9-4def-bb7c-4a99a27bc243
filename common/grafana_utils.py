import requests
import time
import json
import requests
import queue
import threading
import random
from bson import ObjectId
from datetime import datetime, timezone, timedelta
import psutil

GRAFANA_URL = "http://localhost:3100/loki/api/v1/push"
#GRAFANA_USER = "1145415"
#GRAFANA_API_TOKEN = "***********************************************************************************************************************************************************************9fQ=="

class GrafanaUtils:
    def __init__(self, service="cryptobot", url=GRAFANA_URL):
        self.url = url
        #self.auth = (user, api_token)
        self.service = service
        self.default_labels = {"app": "cryptobot", "service": self.service}
        self.log_queue = queue.Queue()
        self._start_log_thread()

    def _start_log_thread(self):
        thread = threading.Thread(target=self._log_worker, daemon=True)
        thread.start()

    def _log_worker(self):
        """Worker thread pour envoyer les logs à Grafana avec plus de robustesse."""
        retry_queue = queue.Queue()  # File d'attente pour les logs qui ont échoué
        
        while True:
            try:
                # Vérifier d'abord s'il y a des logs à réessayer
                if not retry_queue.empty():
                    message, level, extra_labels, override_service, override_time = retry_queue.get()
                    success = self._send_log_sync(message, level, extra_labels, override_service, override_time, is_retry=True)
                    if not success:
                        # Si l'envoi échoue à nouveau, on le remet en file d'attente avec un délai
                        threading.Timer(5.0, lambda: retry_queue.put((message, level, extra_labels, override_service, override_time))).start()
                    retry_queue.task_done()
                    continue
                
                # Sinon, traiter les nouveaux logs
                message, level, extra_labels, override_service, override_time = self.log_queue.get()
                success = self._send_log_sync(message, level, extra_labels, override_service, override_time)
                
                if not success:
                    # Si l'envoi échoue, on le met dans la file d'attente de réessai
                    retry_queue.put((message, level, extra_labels, override_service, override_time))
                
                self.log_queue.task_done()
                
            except Exception as e:
                #print(f"Error in grafana log worker: {e}")
                # Attendre un peu avant de continuer pour éviter une boucle d'erreur rapide
                time.sleep(1)

    def _send_log_sync(self, message, level, extra_labels=None, override_service=None, override_time=None, is_retry=False):
        """Envoie un log à Grafana de manière synchrone avec plus de robustesse."""
        try:
            labels = self.default_labels.copy()
            if override_service:
                labels["service"] = override_service
            if extra_labels:
                labels.update(extra_labels)

            if override_time is None:
                timestamp = str(int(time.time() * 1e9))
            else: 
                timestamp = str(int(datetime.fromisoformat(override_time)
                    .astimezone(timezone.utc)
                    .timestamp() * 1e9))

            log_payload = {
                "streams": [
                    {
                        "stream": labels,
                        "values": [
                            [timestamp, f"[{level.upper()}] {message}"]
                        ]
                    }
                ]
            }

            headers = {
                "Content-Type": "application/json"
            }

            # Ajouter un timeout plus court pour éviter les blocages
            response = requests.post(
                self.url,
                data=json.dumps(log_payload),
                headers=headers,
                #auth=self.auth,
                timeout=2
            )
            
            # Vérifier le code de statut et afficher plus d'informations en cas d'erreur
            if response.status_code != 204:
                error_msg = f"Grafana log error: status={response.status_code}, response={response.text[:100]}"
                #if not is_retry:  # Éviter de spammer les logs d'erreur pour les réessais
                    #print(error_msg)
                return False
            
            return True
            
        except requests.exceptions.RequestException as e:
            #if not is_retry:  # Éviter de spammer les logs d'erreur pour les réessais
                #(f"Grafana request error: {e}")
            return False
        except Exception as e:
            #if not is_retry:  # Éviter de spammer les logs d'erreur pour les réessais
                #print(f"Grafana log error: {e}")
            return False

    def send_log(self, message, level="info", extra_labels=None, override_service=None, override_time=None):
        """Envoie un log à Grafana via la file d'attente et affiche également dans la console."""
        # Ajouter un log dans la console pour le débogage
        #print(f"[GRAFANA] [{level.upper()}] {message} {extra_labels or ''}")
        
        # Mettre le log dans la file d'attente pour l'envoi à Grafana
        self.log_queue.put((message, level, extra_labels, override_service, override_time))



    def send_system_metrics(self, service_name=None):
        """Envoie des métriques système à Grafana."""
        import psutil
        
        # Utiliser le service par défaut si non spécifié
        service = service_name or self.service
        
        # Collecter les métriques système
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Collecter les informations sur le processus Python actuel
        process = psutil.Process()
        process_memory = process.memory_info()
        
        # Envoyer les métriques
        metrics = {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used_gb": round(memory.used / (1024**3), 2),
            "disk_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2),
            "process_memory_mb": round(process_memory.rss / (1024**2), 2),
            "process_cpu_percent": process.cpu_percent(interval=0.1),
            "process_threads": process.num_threads(),
            "process_open_files": len(process.open_files()),
            "process_connections": len(process.connections())
        }
        
        self.send_log("system_metrics", "info", extra_labels=metrics, override_service=f"{service}_metrics")



    def send_raw_data_log(self, data, metric="raw", service='raw_data'):
        """
        Convertit les données techniques en chaîne JSON sérialisée et les envoie via send_log().
        Tous les objets datetime sont convertis en UTC avec timezone spécifiée.
        """
        try:
            def normalize_dates(obj):
                if isinstance(obj, dict):
                    return {
                        k: normalize_dates(v) for k, v in obj.items()
                    }
                elif isinstance(obj, list):
                    return [normalize_dates(v) for v in obj]
                elif isinstance(obj, datetime):
                    if obj.tzinfo is None:
                        return obj.replace(tzinfo=timezone.utc)
                    else:
                        return obj.astimezone(timezone.utc)
                return obj

            def safe_encoder(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, ObjectId):
                    return str(obj)
                return str(obj)

            normalized_data = normalize_dates(data)
            serialized_data = json.dumps(normalized_data, default=safe_encoder)

            message = f"{serialized_data}"
            metrics = {
                "metric": metric,
                "uid": data["uid"]
            }
            if "horizon" in data:
                metrics["horizon"] = data["horizon"]

            override_time = None
            if data.get('predicted_for_time') is not None:
                override_time = data.get('predicted_for_time')
            
            if isinstance(override_time, datetime):
                now_ms = datetime.now(timezone.utc).microsecond // 1000
                override_time = override_time + timedelta(milliseconds=now_ms)
                override_time = override_time.isoformat()

            self.send_log(message, level="debug", extra_labels=metrics, override_service=service, override_time=None)

        except Exception as e:
            self.send_log(f"❌ Erreur lors de l'envoi du log raw_data : {e}", "error")



    '''
    def send_raw_data_log(self, data, metric="raw", service='raw_data'):
        """
        Convertit les données techniques en chaîne JSON sérialisée et les envoie via send_log().
        Gère automatiquement les datetime.
        """
        try:
            def custom_encoder(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                return str(obj)

            # Sérialisation JSON safe (datetime & autres types)
            serialized_data = json.dumps(data, default=custom_encoder)

            # On structure le message log comme une ligne lisible
            message = f"{serialized_data}"
            metrics = {}
            metrics["metric"] = metric
            metrics["uid"] = data["uid"]
            if "horizon" in data:
                metrics["horizon"] = data["horizon"]

            # Envoi via la queue asynchrone de send_log
            self.send_log(message, level="debug", extra_labels=metrics, override_service=service)

        except Exception as e:
            self.send_log(f"❌ Erreur lors de l'envoi du log raw_data : {e}", "error")
    '''





























    def _send_prediction_sync(self, doc, override_service=None):
        try:
            pred = doc
            if not pred:
                return False

            uid = doc.get("uid", "unknown")
            horizon = doc.get('horizon', "3h")

            ts_created_ns = str(int(doc["created_at"].replace(tzinfo=timezone.utc).timestamp() * 1e9))
            predicted_for_ns = str(int(pred["predicted_for_time"].astimezone(timezone.utc).timestamp() * 1e9))


            fields = {
                "future_price_estimated": (pred.get("future_price_estimated"), ts_created_ns)  # On utilise ts_created_iso
            }

            service_label = override_service if override_service else self.service

            streams = []

            for field, (value, timestamp_iso) in fields.items():
                if value is not None:
                    stream = {
                        "stream": {
                            **self.default_labels,
                            "service": service_label,
                            "uid": uid,
                            "level": "info",
                            "metric": field,
                            "horizon": horizon,
                            "predicted_price": str(value),
                            "predicted_for": predicted_for_ns
                        },
                        "values": [
                            [timestamp_iso, str(value)]
                        ]
                    }
                    streams.append(stream)

            if not streams:
                return False

            payload = { "streams": streams }
            #self.send_log(f"streams = {streams}")
            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(
                self.url,
                data=json.dumps(payload),
                headers=headers,
                #auth=self.auth,
                timeout=3
            )

            if response.status_code != 204:
                self.send_log(f"❌ Erreur envoi prediction Grafana : {response.status_code} - {response.text}","error")
                return False

        except Exception as e:
            self.send_log(f"❌ Exception lors de l'envoi de la prédiction : {e}","error")
            return False
        
        return True


    def send_prediction(self, doc):
        override_service = "prediction_data"
        threading.Thread(
            target=self._send_prediction_sync,
            args=(doc, override_service),
            daemon=True
        ).start()


    def _send_current_sync(self, uid, current_price, date, override_service=None):
        try:
            timestamp_ns = str(int(date.astimezone(timezone.utc).timestamp() * 1e9))

            fields = {
                "current_price": (str(current_price), timestamp_ns)
            }
            service_label = override_service if override_service else self.service

            streams = []

            for field, (value, timestamp_iso) in fields.items():
                if value is not None:
                    stream = {
                        "stream": {
                            **self.default_labels,
                            "service": service_label,
                            "uid": uid,
                            "level": "info",
                            "metric": field,
                            "current_price": str(value),
                            "predicted_for": timestamp_iso
                        },
                        "values": [
                            [timestamp_iso, str(value)]
                        ]
                    }
                    streams.append(stream)

            if not streams:
                return

            payload = { "streams": streams }
            #self.send_log(f"streams: {streams}", "debug")
            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(
                self.url,
                data=json.dumps(payload),
                headers=headers,
                #auth=self.auth,
                timeout=3
            )

            #if response.status_code != 204:
                #self.send_log(f"❌ Erreur envoi prediction Grafana : {response.status_code} - {response.text}","error")

        except Exception as e:
            #self.send_log(f"❌ Exception lors de l'envoi de la prédiction : {e}","error")
            return


    def send_current_price(self, uid, current_price, date):
        override_service = "prediction_data"
        threading.Thread(
            target=self._send_current_sync,
            args=(uid, current_price, date, override_service),
            daemon=True
        ).start()


    def _send_rmse_curve(self, uid, y_test, y_pred, timestamps=None, rmse=None, horizon="3h"):
        #self.send_log(f"Dans _send_rmse_curve fonction", "debug")
        try:
            if len(y_test) != len(y_pred):
                #self.send_log(f"❌ y_test et y_pred ont des longueurs différentes ({len(y_test)} vs {len(y_pred)})", "error")
                return

            if timestamps and len(timestamps) != len(y_test):
                #self.send_log(f"❌ Timestamps ne correspond pas à la taille des données", "error")
                return

            run_id = f"run_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            now_ns = str(int(time.time()))
            streams = []

            # Courbes de prédiction
            for i in range(len(y_test)):
                if timestamps and isinstance(timestamps[i], str):
                    self.send_log(f"Dans _send_rmse_curve / fromisoformat", "debug")
                    ts_ns = str(int(datetime.fromisoformat(timestamps[i]).timestamp()))
                elif timestamps and isinstance(timestamps[i], datetime):
                    self.send_log(f"Dans _send_rmse_curve / timestamp()", "debug")
                    ts_ns = str(int(timestamps[i].timestamp()))
                else:
                    #self.send_log(f"Dans _send_rmse_curve / timestamp pas touché", "debug")
                    ts_ns = now_ns
                

                for metric_name, value in {
                    "actual": y_test[i],
                    "predicted": y_pred[i]
                }.items():
                    streams.append({
                        "stream": {
                            "app": "cryptobot",
                            "service": "prediction_curve",
                            "uid": uid,
                            "level": "info",
                            "run_id": run_id,
                            "metric": metric_name,
                            "horizon": horizon,
                            "timestamp": ts_ns
                        },
                        "values": [[str(int(time.time() * 1e9)), str(value)]]
                    })

            # RMSE global
            if rmse is not None:
                streams.append({
                    "stream": {
                        "app": "cryptobot",
                        "service": "prediction_curve",
                        "uid": uid,
                        "run_id": run_id,
                        "metric": "rmse_over_time",
                        "horizon": horizon
                    },
                    "values": [[now_ns, str(rmse)]]
                })

            # Envoi
            payload = {"streams": streams}
            headers = {"Content-Type": "application/json"}

            response = requests.post(
                self.url,
                data=json.dumps(payload),
                headers=headers,
                #auth=self.auth,
                timeout=3
            )

            #if response.status_code != 204:
                #self.send_log(f"❌ Erreur envoi prediction curve : {response.status_code} - {response.text}", "error")
            return True

        except Exception as e:
            #self.send_log(f"❌ Exception lors de l'envoi RMSE curve : {e}", "error")
            return False



        
    def send_prediction_curve(self, uid, y_test, y_pred, timestamps=None, rmse=None, horizon="3h"):
        threading.Thread(
            target=self._send_rmse_curve,
            args=(uid, y_test, y_pred, timestamps, rmse, horizon),
            daemon=True
        ).start()


    def log_script_lifecycle(self, script_name, status="started", extra_info=None):
        """Journalise le cycle de vie d'un script."""
        import platform, socket, getpass
        
        # Informations de base sur l'environnement
        env_info = {
            "hostname": socket.gethostname(),
            "ip": socket.gethostbyname(socket.gethostname()),
            "python_version": platform.python_version(),
            "os": platform.platform(),
            "user": getpass.getuser(),
            "pid": os.getpid(),
            "script": script_name
        }
        
        # Ajouter des informations supplémentaires si fournies
        if extra_info:
            env_info.update(extra_info)
        
        # Envoyer le log
        self.send_log(f"script_{status}", "info", extra_labels=env_info)


    def log_dependencies(self):
        """Journalise les versions des dépendances critiques."""
        import pkg_resources
        
        # Liste des packages critiques à surveiller
        critical_packages = [
            "pandas", "numpy", "torch", "stable-baselines3", "gymnasium",
            "scikit-learn", "pymongo", "pyarrow", "optuna"
        ]
        
        # Collecter les versions
        dependencies = {}
        for package in critical_packages:
            try:
                version = pkg_resources.get_distribution(package).version
                dependencies[package] = version
            except pkg_resources.DistributionNotFound:
                dependencies[package] = "not_installed"
        
        # Envoyer le log
        self.send_log("dependencies", "info", extra_labels=dependencies)


    def log_exception(self, e, context=None):
        """Journalise une exception avec un contexte enrichi."""
        import traceback, inspect
        
        # Obtenir la trace d'appel
        stack = inspect.stack()
        caller = stack[1] if len(stack) > 1 else None
        
        # Préparer les informations sur l'exception
        exception_info = {
            "exception_type": e.__class__.__name__,
            "exception_message": str(e),
            "traceback": traceback.format_exc()[:1000],  # Limiter la taille
            "file": caller.filename if caller else "unknown",
            "function": caller.function if caller else "unknown",
            "line": caller.lineno if caller else 0
        }
        
        # Ajouter le contexte si disponible
        if context:
            exception_info.update(context)
        
        # Envoyer le log
        self.send_log("exception", "error", extra_labels=exception_info)


    
