"""
Configuration TensorFlow optimisée pour CPU
============================================

Ce module configure TensorFlow pour des performances optimales sur CPU uniquement.
Il détecte automatiquement les ressources système et configure les paramètres appropriés.

Utilisation:
    from common.tensorflow_config import configure_tensorflow_for_cpu
    configure_tensorflow_for_cpu()
"""

import os
import multiprocessing
import psutil
import platform
import warnings
from typing import Optional, Dict, Any

# Supprimer les warnings TensorFlow non critiques
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
warnings.filterwarnings('ignore', category=FutureWarning)


def get_system_info() -> Dict[str, Any]:
    """
    Récupère les informations système pour optimiser la configuration.
    
    Returns:
        Dict contenant les informations système
    """
    try:
        # Informations CPU
        cpu_count_logical = psutil.cpu_count(logical=True)
        cpu_count_physical = psutil.cpu_count(logical=False)
        cpu_freq = psutil.cpu_freq()
        
        # Informations mémoire
        memory = psutil.virtual_memory()
        
        # Informations système
        system_info = {
            'cpu_logical': cpu_count_logical,
            'cpu_physical': cpu_count_physical,
            'cpu_freq_max': cpu_freq.max if cpu_freq else None,
            'memory_total_gb': round(memory.total / (1024**3), 2),
            'memory_available_gb': round(memory.available / (1024**3), 2),
            'platform': platform.system(),
            'architecture': platform.machine(),
            'python_version': platform.python_version()
        }
        
        return system_info
        
    except Exception as e:
        print(f"⚠️ Erreur lors de la détection système: {e}")
        # Valeurs par défaut conservatrices
        return {
            'cpu_logical': multiprocessing.cpu_count(),
            'cpu_physical': multiprocessing.cpu_count(),
            'cpu_freq_max': None,
            'memory_total_gb': 8.0,
            'memory_available_gb': 4.0,
            'platform': 'Unknown',
            'architecture': 'Unknown',
            'python_version': 'Unknown'
        }


def calculate_optimal_threads(system_info: Dict[str, Any]) -> Dict[str, int]:
    """
    Calcule le nombre optimal de threads selon les ressources système.
    
    Args:
        system_info: Informations système
        
    Returns:
        Dict avec les configurations de threads optimales
    """
    cpu_logical = system_info['cpu_logical']
    cpu_physical = system_info['cpu_physical']
    memory_gb = system_info['memory_available_gb']
    
    # Configuration adaptative selon les ressources
    if cpu_logical >= 16 and memory_gb >= 16:
        # Système haute performance
        intra_op = min(cpu_logical, 12)  # Limiter pour éviter la surcharge
        inter_op = max(2, cpu_logical // 4)
        omp_threads = min(cpu_logical, 8)
        mkl_threads = min(cpu_logical, 8)
        
    elif cpu_logical >= 8 and memory_gb >= 8:
        # Système moyen-haut de gamme
        intra_op = min(cpu_logical, 8)
        inter_op = max(2, cpu_logical // 6)
        omp_threads = min(cpu_logical, 6)
        mkl_threads = min(cpu_logical, 6)
        
    elif cpu_logical >= 4 and memory_gb >= 4:
        # Système standard
        intra_op = min(cpu_logical, 6)
        inter_op = 2
        omp_threads = min(cpu_logical, 4)
        mkl_threads = min(cpu_logical, 4)
        
    else:
        # Système limité
        intra_op = min(cpu_logical, 4)
        inter_op = 1
        omp_threads = min(cpu_logical, 2)
        mkl_threads = min(cpu_logical, 2)
    
    return {
        'intra_op': intra_op,
        'inter_op': inter_op,
        'omp_threads': omp_threads,
        'mkl_threads': mkl_threads,
        'openblas_threads': mkl_threads,
        'numexpr_threads': mkl_threads
    }


def configure_environment_variables(thread_config: Dict[str, int], enable_optimizations: bool = True):
    """
    Configure les variables d'environnement pour optimiser les performances.
    
    Args:
        thread_config: Configuration des threads
        enable_optimizations: Activer les optimisations avancées
    """
    # Configuration des threads
    os.environ['TF_INTRA_OP_PARALLELISM_THREADS'] = str(thread_config['intra_op'])
    os.environ['TF_INTER_OP_PARALLELISM_THREADS'] = str(thread_config['inter_op'])
    os.environ['OMP_NUM_THREADS'] = str(thread_config['omp_threads'])
    os.environ['MKL_NUM_THREADS'] = str(thread_config['mkl_threads'])
    os.environ['OPENBLAS_NUM_THREADS'] = str(thread_config['openblas_threads'])
    os.environ['NUMEXPR_NUM_THREADS'] = str(thread_config['numexpr_threads'])
    
    # Désactiver GPU (CPU uniquement)
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
    
    # Optimisations TensorFlow
    if enable_optimizations:
        # Activer XLA (Accelerated Linear Algebra)
        os.environ['TF_XLA_FLAGS'] = '--tf_xla_auto_jit=2 --tf_xla_cpu_global_jit'
        
        # Optimisations OneDNN (Intel MKL-DNN)
        os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
        
        # Optimisations mémoire
        os.environ['TF_GPU_ALLOCATOR'] = 'cuda_malloc_async'  # Même pour CPU, améliore la gestion mémoire
        
        # Optimisations de compilation
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # Réduire les logs
        
        # Optimisations threading avancées
        os.environ['TF_NUM_INTRAOP_THREADS'] = str(thread_config['intra_op'])
        os.environ['TF_NUM_INTEROP_THREADS'] = str(thread_config['inter_op'])
        
        # Optimisations BLAS
        os.environ['BLIS_NUM_THREADS'] = str(thread_config['mkl_threads'])
        os.environ['VECLIB_MAXIMUM_THREADS'] = str(thread_config['mkl_threads'])
    
    # Configuration pour la reproductibilité (optionnel)
    os.environ['TF_DETERMINISTIC_OPS'] = '1'
    os.environ['PYTHONHASHSEED'] = '42'


def configure_tensorflow_runtime(thread_config: Dict[str, int], enable_mixed_precision: bool = False):
    """
    Configure TensorFlow au runtime pour optimiser les performances.
    
    Args:
        thread_config: Configuration des threads
        enable_mixed_precision: Activer la précision mixte (expérimental sur CPU)
    """
    try:
        import tensorflow as tf
        
        # Configuration des threads
        tf.config.threading.set_intra_op_parallelism_threads(thread_config['intra_op'])
        tf.config.threading.set_inter_op_parallelism_threads(thread_config['inter_op'])
        
        # Activer JIT compilation
        tf.config.optimizer.set_jit(True)
        
        # Optimisations expérimentales
        tf.config.optimizer.set_experimental_options({
            'layout_optimizer': True,
            'constant_folding': True,
            'shape_optimization': True,
            'remapping': True,
            'arithmetic_optimization': True,
            'dependency_optimization': True,
            'loop_optimization': True,
            'function_optimization': True,
            'debug_stripper': True,
            'disable_model_pruning': False,
            'scoped_allocator_optimization': True,
            'pin_to_host_optimization': True,
            'implementation_selector': True,
            'auto_mixed_precision': enable_mixed_precision,
            'disable_meta_optimizer': False,
        })
        
        # Configuration mémoire (même pour CPU, améliore la gestion)
        try:
            # Désactiver GPU explicitement
            tf.config.set_visible_devices([], 'GPU')
        except:
            pass  # GPU non disponible, c'est normal
            
        # Activer le déterminisme pour la reproductibilité
        tf.config.experimental.enable_op_determinism()
        
        print(f"✅ TensorFlow configuré avec {thread_config['intra_op']} threads intra-op et {thread_config['inter_op']} threads inter-op")
        
    except ImportError:
        print("⚠️ TensorFlow non disponible, configuration des variables d'environnement uniquement")
    except Exception as e:
        print(f"⚠️ Erreur lors de la configuration TensorFlow: {e}")


def configure_tensorflow_for_cpu(
    enable_optimizations: bool = True,
    enable_mixed_precision: bool = False,
    custom_thread_config: Optional[Dict[str, int]] = None,
    verbose: bool = True
) -> Dict[str, Any]:
    """
    Configure TensorFlow pour des performances optimales sur CPU.
    
    Args:
        enable_optimizations: Activer les optimisations avancées
        enable_mixed_precision: Activer la précision mixte (expérimental)
        custom_thread_config: Configuration personnalisée des threads
        verbose: Afficher les informations de configuration
        
    Returns:
        Dict avec les informations de configuration appliquées
    """
    # Détecter les ressources système
    system_info = get_system_info()
    
    if verbose:
        print("🔧 Configuration TensorFlow pour CPU")
        print(f"💻 Système: {system_info['platform']} {system_info['architecture']}")
        print(f"🧠 CPU: {system_info['cpu_logical']} cœurs logiques, {system_info['cpu_physical']} cœurs physiques")
        print(f"💾 Mémoire: {system_info['memory_available_gb']:.1f}GB disponible / {system_info['memory_total_gb']:.1f}GB total")
    
    # Calculer la configuration optimale des threads
    if custom_thread_config:
        thread_config = custom_thread_config
        if verbose:
            print("🎛️ Utilisation de la configuration personnalisée des threads")
    else:
        thread_config = calculate_optimal_threads(system_info)
        if verbose:
            print("🎯 Configuration automatique des threads")
    
    if verbose:
        print(f"🔄 Threads intra-op: {thread_config['intra_op']}")
        print(f"🔄 Threads inter-op: {thread_config['inter_op']}")
        print(f"🔄 Threads OMP: {thread_config['omp_threads']}")
        print(f"🔄 Threads MKL: {thread_config['mkl_threads']}")
    
    # Configurer les variables d'environnement
    configure_environment_variables(thread_config, enable_optimizations)
    
    # Configurer TensorFlow au runtime
    configure_tensorflow_runtime(thread_config, enable_mixed_precision)
    
    if verbose:
        optimizations_status = "activées" if enable_optimizations else "désactivées"
        print(f"⚡ Optimisations avancées: {optimizations_status}")
        print("✅ Configuration TensorFlow terminée")
    
    return {
        'system_info': system_info,
        'thread_config': thread_config,
        'optimizations_enabled': enable_optimizations,
        'mixed_precision_enabled': enable_mixed_precision
    }


def get_performance_recommendations(system_info: Dict[str, Any]) -> list:
    """
    Génère des recommandations pour améliorer les performances.
    
    Args:
        system_info: Informations système
        
    Returns:
        Liste de recommandations
    """
    recommendations = []
    
    cpu_logical = system_info['cpu_logical']
    memory_gb = system_info['memory_available_gb']
    
    if cpu_logical < 4:
        recommendations.append("💡 Considérez un processeur avec plus de cœurs pour améliorer les performances")
    
    if memory_gb < 8:
        recommendations.append("💡 Augmenter la RAM à 8GB+ améliorerait significativement les performances")
    
    if memory_gb < 4:
        recommendations.append("⚠️ Mémoire insuffisante - réduisez la taille des batches et des modèles")
    
    if cpu_logical >= 8 and memory_gb >= 16:
        recommendations.append("🚀 Système performant - vous pouvez utiliser des modèles plus complexes")
    
    return recommendations


if __name__ == "__main__":
    # Test de la configuration
    config_info = configure_tensorflow_for_cpu(verbose=True)
    
    # Afficher les recommandations
    recommendations = get_performance_recommendations(config_info['system_info'])
    if recommendations:
        print("\n📋 Recommandations:")
        for rec in recommendations:
            print(f"   {rec}")
