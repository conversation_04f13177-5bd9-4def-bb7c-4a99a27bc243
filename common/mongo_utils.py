import os
from pymongo import MongoClient
from bson import ObjectId
from dotenv import load_dotenv
from pathlib import Path
from datetime import datetime, timedelta
from common.grafana_utils import GrafanaUtils  # Nom corrigé pour respecter les conventions Python


env_path = Path(__file__).resolve().parent.parent / ".env"
load_dotenv(dotenv_path=env_path)

MONGO_USER = os.getenv("MONGO_USER")
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD")
MONGO_HOST = os.getenv("MONGO_HOST")
MONGO_DB = os.getenv("MONGO_DB")
required_env = [MONGO_USER, MONGO_PASSWORD, MONGO_HOST, MONGO_DB]
if not all(required_env):
    raise ValueError("❌ Une ou plusieurs variables d'environnement Mongo sont manquantes.")
MONGO_URI = f"mongodb://{MONGO_USER}:{MONGO_PASSWORD}@{MONGO_HOST}:27017/{MONGO_DB}?authSource={MONGO_DB}"


CRYPTO_TEST = [
    "BTCEUR",
    "ETHEUR",
    "PIVXUSDT",
    "DOGEEUR",
    "PEPEEUR",
    "XRPEUR",
    "AVAXEUR",
    "MASKUSDT",
    "GALAUSDT",
    "RLCUSDT"
]




class MongoUtils:
    def __init__(self, logger=None):
        self.client = None
        self.db = None
        self.logger = logger


    def connect(self, uri=MONGO_URI, db_name=MONGO_DB, service="cryptobot"):
        """Initialise une nouvelle connexion Mongo avec un nom de service personnalisé"""
        self.client = MongoClient(uri)
        self.db = self.client[db_name]
        self.service = service
        if not self.logger:
            self.logger = GrafanaUtils(service)
        #self.logger.send_log(f"✅ Connexion Mongo ouverte pour le service '{service}'", level="info")

    def disconnect(self):
        """Ferme proprement la connexion MongoDB"""
        try:
            if self.client:
                self.client.close()
                self.client = None
                self.db = None
                if self.logger:
                    self.logger.send_log(f"✅ Connexion MongoDB fermée pour le service '{self.service}'", level="info")
                return True
            else:
                if self.logger:
                    self.logger.send_log("⚠️ Aucune connexion MongoDB active à fermer", level="warning")
                return False
        except Exception as e:
            if self.logger:
                self.logger.send_log(f"❌ Erreur lors de la fermeture de la connexion MongoDB : {e}", level="error")
            return False

    def get_active_pairs(self):
        """Récupère les UID des paires en statut 'TRADING' depuis MongoDB"""
        try:
            collection = self.db["market_pairs"]
            trading_pairs = collection.find({"status": "TRADING"}, {"_id": 0, "uid": 1})
            return [pair["uid"] for pair in trading_pairs]
        except Exception as e:
            self.logger.send_log(f"❌ Erreur lors de la récupération des paires de trading actives : {e}", "error")
            return []



    def get_trading_pairs(self):
        """Récupère les UID des paires en statut 'TRADING' depuis MongoDB"""
        return CRYPTO_TEST

        '''
        try:

            collection = self.db["market_pairs"]
            trading_pairs = collection.find({"status": "TRADING"}, {"_id": 0, "uid": 1})
            return [pair["uid"] for pair in trading_pairs]
        except Exception as e:
            self.logger.send_log(f"❌ Erreur lors de la récupération des paires de trading actives : {e}", "error")
            return []
        '''

    def get_all_horizons(self):
        """
        Récupère tous les documents de la collection 'horizons'.
        Retourne une liste vide en cas d'erreur.
        """
        collection_name = "horizons"
        try:
            collection = self.db[collection_name]

            projection = {
                "_id": 0,  # Tu peux adapter ça selon ce que tu veux exclure
            }

            cursor = collection.find({}, projection).sort("created_at", 1)  # tri par nom si dispo, sinon adapte

            data = list(cursor)

            if not data:
                self.logger.send_log(
                    "⚠️ - Aucun document trouvé dans la collection 'horizons'",
                    level="warning"
                )
                return []

            return data

        except Exception as e:
            self.logger.send_log(
                f"❌ - Erreur lors de la récupération des horizons : {e}",
                level="error"
            )
            return []


    def update_market_pairs(self, pairs):
        """
        Met à jour ou insère les paires dans la collection 'market_pairs' en se basant sur le champ 'uid'.
        Utilise upsert=True pour créer la paire si elle n'existe pas encore.
        """
        try:
            collection = self.db["market_pairs"]
            updated = 0
            inserted = 0
            ignored = 0

            for pair in pairs:
                result = collection.update_one(
                    {"uid": pair["uid"]},  # 🔑 clé de recherche
                    {
                        "$set": {
                            **pair,
                            "updated_at": datetime.utcnow()
                        }
                    },
                    upsert=True  # ✅ Création si la paire n'existe pas
                )

                if result.matched_count == 0:
                    inserted += 1
                    #self.logger.send_log(f"➕ - Nouvelle paire ajoutée : {pair['uid']}", "debug")
                elif result.modified_count > 0:
                    updated += 1
                    #self.logger.send_log(f"✅ - Paire mise à jour : {pair['uid']}", "debug")
                else:
                    ignored += 1
                    #self.logger.send_log(f"ℹ️ - Aucune modification nécessaire : {pair['uid']}", "debug")

            self.logger.send_log(f"📦 Résultat — Ajouts : {inserted}, Mises à jour : {updated}, Inchangées : {ignored}", "info")
            return True

        except Exception as e:
            self.logger.send_log(f"❌ - Erreur update_market_pairs : {e}", "error")
            return False



    def delete_missing_market_pairs(self, current_uids):
        """
        Supprime de la collection 'market_pairs' toutes les paires dont le 'uid' n'est pas dans 'current_uids'.
        """
        try:
            collection = self.db["market_pairs"]

            result = collection.delete_many({
                "uid": {"$nin": current_uids}
            })

            if result.deleted_count > 0:
                self.logger.send_log(f"🗑️ - {result.deleted_count} paires supprimées (obsolètes)", "info")

            return True

        except Exception as e:
            self.logger.send_log(f"❌ - Erreur delete_missing_market_pairs : {e}", "error")
            return False


    def get_best_indicator_params(self, horizon: str, uid: str, horizon_hour: int):
        """
        Récupère le document avec le meilleur 'combined_score' dans la collection 'indicators_params',
        créé dans les 10 + horizon_hour derniers jours. Si aucun document n'est trouvé pour l'UID donné,
        retourne le meilleur document pour uid='default' sur le même horizon.
        """
        try:
            collection = self.db["indicators_params"]

            # Fenêtre temporelle dynamique
            days_delta = 70 + (horizon_hour)
            time_threshold = datetime.utcnow() - timedelta(days=days_delta)

            # Recherche du meilleur document pour l'UID donné
            query = {
                "uid": uid,
                "horizon": horizon,
                "created_at": {"$gte": time_threshold}
            }

            best_params = collection.find(query).sort("final_score", -1).limit(1)
            best_params = list(best_params)

            if best_params:
                return best_params[0]

            # Recherche de secours pour uid = 'default'
            fallback_query = {
                "uid": "default",
                "horizon": horizon
            }

            fallback = collection.find(fallback_query)
            fallback = list(fallback)

            if fallback:
                self.logger.send_log(
                    f"ℹ️ - {uid} - {horizon} - Aucun paramètre d'indicateur trouvé, utilisation de 'default'.",
                    "warning"
                )
                return fallback[0]

            # Aucun document trouvé
            self.logger.send_log(
                f"⚠️ - {uid} - {horizon} - Aucun paramètre d'indicateur trouvé pour {horizon} (même pour 'default').",
                "error"
            )
            return None

        except Exception as e:
            self.logger.send_log(
                f"❌ - {uid} - {horizon} - Erreur lors de la récupération des paramètres d'indicateur pour {horizon} : {e}",
                "error"
            )
            return None

    def get_best_hyperparams(self, horizon: str, uid: str, horizon_hour: int):
        """
        Récupère le document avec le meilleur 'combined_score' dans la collection 'hyperparams',
        créé dans les 10 + horizon_hour derniers jours. Si aucun document n'est trouvé pour l'UID donné,
        retourne le meilleur document pour uid='default' sur le même horizon.
        """
        try:
            collection = self.db["hyperparams"]

            # Fenêtre temporelle dynamique
            days_delta = 70 + horizon_hour
            time_threshold = datetime.utcnow() - timedelta(days=days_delta)

            # Recherche principale pour l'uid donné
            query = {
                "uid": uid,
                "horizon": horizon,
                "created_at": {"$gte": time_threshold},
                "final_score": {"$exists": True}
            }

            best_params = list(collection.find(query).sort("final_score", -1).limit(1))

            if best_params:
                return best_params[0]
            

            # Fallback sur uid = "default"
            fallback_query = {
                "uid": "default",
                "horizon": horizon
            }

            fallback = list(collection.find(fallback_query))

            if fallback:
                self.logger.send_log(
                    f"ℹ️ - {uid} - {horizon} - Aucun hyperparamètre trouvé, fallback sur 'default'.",
                    "warning"
                )
                return fallback[0]

            # Aucun document du tout
            self.logger.send_log(
                f"⚠️ - {uid} - {horizon} - Aucun hyperparamètre trouvé pour {horizon} (même pour 'default').",
                "error"
            )
            return None

        except Exception as e:
            self.logger.send_log(
                f"❌ - {uid} - {horizon} - Erreur lors de la récupération des hyperparamètres (hyperparams) pour {horizon} : {e}",
                "error"
            )
            return None
        
    def get_hyperparams(self, horizon: str, uid: str, horizon_hour: int):
        """
        Récupère le document avec le meilleur 'combined_score' dans la collection 'hyperparams',
        créé dans les 10 + horizon_hour derniers jours. Si aucun document n'est trouvé pour l'UID donné,
        retourne le meilleur document pour uid='default' sur le même horizon.
        """
        try:
            collection = self.db["hyperparams"]

            # Fenêtre temporelle dynamique
            days_delta = 70 + horizon_hour
            time_threshold = datetime.utcnow() - timedelta(days=days_delta)

            # Recherche principale pour l'uid donné
            query = {
                "uid": uid,
                "horizon": horizon,
                "created_at": {"$gte": time_threshold},
                "final_score": {"$exists": True}
            }

            best_params = list(collection.find(query).sort("final_score", -1).limit(10))

            if best_params:
                return best_params
            

            # Recherche principale pour l'uid donné
            query = {
                "horizon": horizon,
                "final_score": {"$exists": True}
            }

            best_other_params = list(collection.find(query).sort("final_score", -1).limit(10))

            if best_other_params:
                return best_other_params
            

            # Fallback sur uid = "default"
            fallback_query = {
                "uid": "default",
                "horizon": horizon
            }

            fallback = list(collection.find(fallback_query))

            if fallback:
                self.logger.send_log(
                    f"ℹ️ - {uid} - {horizon} - Aucun hyperparamètre trouvé, fallback sur 'default'.",
                    "warning"
                )
                return fallback

            # Aucun document du tout
            self.logger.send_log(
                f"⚠️ - {uid} - {horizon} - Aucun hyperparamètre trouvé pour {horizon} (même pour 'default').",
                "error"
            )
            return None

        except Exception as e:
            self.logger.send_log(
                f"❌ - {uid} - {horizon} - Erreur lors de la récupération des hyperparamètres (hyperparams) pour {horizon} : {e}",
                "error"
            )
            return None




    def store_candlesticks(self, data):
        """Insère chaque nouvelle entrée comme un document unique dans MongoDB
        et injecte l’_id généré dans chaque document du tableau `data`."""
        if isinstance(data, list) and data:
            try:
                collection = self.db["candlesticks"]
                result = collection.insert_many(data, ordered=False)
                # result.inserted_ids est une liste d’ObjectId dans le même ordre que `data`
                for idx, inserted_id in enumerate(result.inserted_ids):
                    data[idx]["_id"] = inserted_id
                return data

            except Exception as e:
                self.logger.send_log(f"❌ Erreur lors de l'insertion des candlesticks : {e}", "error")
                return None
        else:
            self.logger.send_log("⚠️ store_candlesticks appelé avec une donnée vide ou invalide", "warning")
            return None



    def get_last_candlestick(self, uid):
        """Récupère le dernier candlestick pour une paire donnée (uid)"""
        try:
            collection = self.db["candlesticks"]
            data = collection.find_one(
                {"uid": uid},
                {"_id": 0, "closeTime": 1, "highPrice":1, "lowPrice": 1, "lastPrice": 1},
                sort=[("created_at", -1)]
            )
            return data
        except Exception as e:
            self.logger.send_log(f"❌ Erreur lors de la récupération du dernier candlestick pour {uid} : {e}", "error", service=self.service)
            return None


    def get_candlesticks_without_indicators(self, uid, horizon):
        """
        Récupère les candlesticks sans indicateurs
        """
        collection_name = "candlesticks"
        try:
            collection = self.db[collection_name]

            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=5)

            #self.logger.send_log(f"📡 - {uid} - Récupération des candlesticks sans indicateurs",level="info")

            projection = {
                "_id": 1,
                "openTime": 1,
                "closeTime": 1,
                "highPrice": 1,
                "lowPrice": 1,
                "lastPrice": 1
            }

            cursor = collection.find(
                {
                    "uid": uid,
                    "created_at": {"$lte": end_date, "$gte": start_date},
                    "$or": [
                        {f"indicators_ready.{horizon}": False},
                        {f"indicators_ready.{horizon}": "N/A"},
                        {f"indicators_ready.{horizon}": {"$exists": False}}
                    ]
                },
                projection
            ).sort("created_at", 1)


            data = list(cursor)

            if not data:
                self.logger.send_log(f"⚠️ - {uid} - {horizon} - Aucune candlestick sans indicateur trouvée entre {start_date.isoformat()} et {end_date.isoformat()}",level="warning")
                return []

            return data

        except Exception as e:
            self.logger.send_log(
                f"❌ - {uid} - {horizon} - Erreur lors de la récupération depuis MongoDB : {e}",
                level="error"
            )
            return []


    def get_candlesticks(self, uid, until_date, from_date=None):
        """
        Récupère les prix depuis MongoDB pour un UID donné entre from_date et until_date.
        Optimisé pour limiter la charge mémoire/CPU et MongoDB.
        """
        collection_name = "candlesticks"
        batch_size = 1000
        try:
            end_date = until_date
            start_date = from_date or (end_date - timedelta(days=16))

            collection = self.db[collection_name]

            projection = {
                "_id": 0,
                "updated_at": 0,
                "openPrice": 0,
                "openTime": 0,
                "closeTime": 0,
                "count": 0,
                "uid": 0,
                "indicator_id": 0,
                "prediction_id": 0,
                "indicators_ready": 0,
                "prediction_ready": 0
            }

            cursor = collection.find(
                {
                    "uid": uid,
                    "created_at": {
                        "$lte": end_date,
                        "$gte": start_date
                    }
                },
                projection=projection,
                batch_size=batch_size
            ).sort("created_at", 1)

            data = list(cursor)

            if not data:
                self.logger.send_log(
                    f"⚠️ - {uid} - Aucune donnée entre {start_date.isoformat()} et {end_date.isoformat()}",
                    level="warning"
                )
                return []

            return data

        except Exception as e:
            self.logger.send_log(
                f"❌ - {uid} - Erreur MongoDB entre {start_date.isoformat()} et {until_date.isoformat()} : {e}",
                level="error"
            )
            return []



    def get_candlesticks(self, uid, until_date, from_date=None):
        """
        Récupère les prix depuis MongoDB pour un UID donné jusqu'à la date spécifiée.
        Retourne une liste vide si une erreur survient ou s'il n'y a pas assez de données.
        """
        collection_name = "candlesticks"
        try:
            end_date = until_date
            if from_date is None:
                start_date = end_date - timedelta(days=16)
            else :
                start_date = from_date

            #self.logger.send_log(f"📡 - {uid} - Récupération des données de {start_date.isoformat()} à {until_date.isoformat()}",level="info")

            collection = self.db[collection_name]

            projection = {
                "_id": 0,
                "updated_at": 0,
                "openPrice": 0,
                "openTime": 0,
                "closeTime": 0,
                "count": 0,
                "uid": 0,
                "indicator_id": 0,
                "prediction_id": 0,
                "indicators_ready": 0,
                "prediction_ready": 0
            }

            cursor = collection.find(
                {"uid": uid, "created_at": {"$lte": end_date, "$gte": start_date}},
                projection
            ).sort("created_at", 1)

            data = list(cursor)

            if not data:
                self.logger.send_log(
                    f"⚠️ - {uid} - Aucune donnée trouvée entre {start_date.isoformat()} et {end_date.isoformat()}",
                    level="warning"
                )
                return []

            return data

        except Exception as e:
            self.logger.send_log(
                f"❌ - {uid} - Erreur lors de la récupération depuis MongoDB : {e}",
                level="error"
            )
            return []


    def store_indicator(self, data, uid, until_date, horizon):
        """
        Stocke les données d'indicateurs dans MongoDB et retourne l'_id inséré.
        """
        collection_name = "indicators"

        try:
            collection = self.db[collection_name]

            # Ajout des métadonnées
            data["uid"] = uid
            data["created_at"] = until_date
            data["updated_at"] = datetime.utcnow()
            if isinstance(data["candlestick_id"], str):
                data["candlestick_id"] = ObjectId(data["candlestick_id"])

            existing = self.db["indicators"].find_one({
                "candlestick_id": data["candlestick_id"],
                "horizon": horizon
            })
            if existing:
                self.logger.send_log(f"⚠️ - {uid} - Indicateur déjà existant pour ce candlestick({data['candlestick_id']}). Skip.", "warning")
                return None

            # Insertion du document
            result = collection.insert_one(data)

            return result.inserted_id  # ✅ Retourne l'_id du document

        except Exception as e:
            self.logger.send_log(
                f"❌ - {uid} - Erreur lors de l'écriture des données dans MongoDB : {e}",
                level="error"
            )
            return None  # ⛔ Retourne None en cas d'erreur


    def update_candlestick(self, candlestick_id, uid, horizon, value=False):
        """
        Met à jour un document candlestick en ajoutant le champ 'indicator_id'.
        Accepte "N/A" comme valeur spéciale pour indiquer l'absence d'indicateur.
        """
        try:
            collection = self.db["candlesticks"]

            # 🔹 Convertir candlestick_id si c'est une chaîne
            if isinstance(candlestick_id, str):
                candlestick_id = ObjectId(candlestick_id)

            result = collection.update_one(
                {"_id": candlestick_id},
                {
                    "$set": {
                        f"indicators_ready.{horizon}": value,
                        "updated_at": datetime.utcnow()
                    }
                }
            )

            if result.modified_count == 1:
                #self.logger.send_log(f"✅ - {uid} - Candlestick {candlestick_id} mis à jour avec indicator_id {indicator_id}", "info")
                return True
            else:
                self.logger.send_log(f"⚠️ - {uid} - Aucun document mis à jour pour l'ID {candlestick_id}", "warning")
                return False

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur lors de la mise à jour du candlestick {candlestick_id} : {e}", "error")
            return False





    def create_indicatorparams(self, horizon, uid, data):
        """
        Crée un nouveau document d'hyperparamètres optimisés dans MongoDB.
        Échoue si un document avec le même uid et horizon existe déjà.
        """
        try:
            collection = self.db["indicators_params"]

            # Préparation du document à insérer
            document = {
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                **data
            }

            result = collection.insert_one(document)
            if not result.acknowledged:
                self.logger.send_log(f"❌ - {uid} - L'insertion n'a pas été confirmée !", "error")
            else:
                self.logger.send_log(f"🆕 - {uid} - {horizon} - Paramètres d'indicateurs créés.", "info")
            return True

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur lors de l'insertion des hyperparamètres pour {horizon} : {e}", "error")
            return False


    def create_hyperparams(self, horizon, uid, data):
        """
        Crée ou met à jour un document d'hyperparamètres optimisés dans MongoDB.
        """
        try:
            collection = self.db["hyperparams"]

            document = {
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                **data
            }

            result = collection.insert_one(document)
            if not result.acknowledged:
                self.logger.send_log(f"❌ - {uid} - L'insertion n'a pas été confirmée !", "error")
            else:
                self.logger.send_log(f"🆕 - {uid} - {horizon} - Hyperparamètres créés.", "info")
            return True

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur lors de la création/mise à jour des hyperparamètres pour {horizon} : {e}", "error")
            return False


    def update_hyperparams(self, model, uid, evaluation, best_params, used_features, shap_df):
        """
        Met à jour un document d'hyperparamètres existant basé sur le champ 'model'.
        Ne crée pas de nouveau document si le modèle n'existe pas déjà.
        """
        try:
            collection = self.db["optimized_hyperparams"]

            result = collection.update_one(
                {"model": model},  # 🔑 clé de recherche
                {
                    "$set": {
                        "uid": uid,
                        "evaluation": evaluation,
                        "best_params": best_params,
                        "used_features": used_features,
                        "score_features": shap_df,
                        "updated_at": datetime.utcnow()
                    }
                },
                upsert=False  # ❌ Pas de création si non trouvé
            )

            if result.matched_count == 0:
                self.logger.send_log(f"⚠️ - {uid} - {model['name']} — Aucun document trouvé. Mise à jour ignorée.", "warning")
                return False

            if result.modified_count > 0:
                self.logger.send_log(f"✅ - {uid} - {model['name']} — Hyperparamètres mis à jour.", "info")
            else:
                self.logger.send_log(f"ℹ️ - {uid} - {model['name']} — Aucune modification nécessaire.", "debug")

            return True

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - {model['name']} — Erreur lors de la mise à jour des hyperparamètres : {e}", "error")
            return False


    def load_training_data(self, symbol, horizon, date_threshold):
        """
        Charge les données d'entraînement depuis MongoDB pour un symbole et un horizon donné.
        """
        try:
            collection = self.db["indicators"]
            cursor = collection.find(
                {
                    "uid": symbol,
                    "horizon": horizon["name"],
                    "created_at": {"$gte": date_threshold}
                },
                {
                    "_id": 0,
                    "updated_at": 0,
                    "uid": 0,
                    "candlestick_id": 0,
                    "prediction_id": 0,
                    "horizon": 0
                }
            ).sort("created_at", 1)

            data = list(cursor)

            #self.logger.send_log(f"✅ - {symbol} - {len(data)} lignes chargées pour l’horizon {horizon.get('name')}.", "info")
            return data

        except Exception as e:
            self.logger.send_log(f"❌ - {symbol} - {horizon.get('name')} - Erreur lors du chargement des données : {e}", "error")
            return []


    def update_model_evaluation(self, model_name, symbol, evaluation_results, used_features):
        """
        Met à jour l'évaluation du modèle et les features utilisés dans MongoDB.
        """
        try:
            collection = self.db["optimized_hyperparams"]

            update_result = collection.update_one(
                {"model.full_name": model_name},
                {
                    "$set": {
                        "current_model_evaluation": evaluation_results,
                        "used_features": used_features,
                        "last_trained_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow()
                    }
                }
            )

            if update_result.modified_count == 0:
                self.logger.send_log(f"⚠️ - {symbol} - {model_name} - Aucune mise à jour effectuée (document introuvable ?)","warning")
                return False
            else:
                #self.logger.send_log(f"✅ - {symbol} - {model_name} - Évaluation mise à jour avec succès.","info")
                return True

        except Exception as e:
            self.logger.send_log(f"❌ - {symbol} - {model_name} - Erreur lors de la mise à jour MongoDB : {e}","error")
            return False



    def get_missing_predictions(self, uid, horizon):
        """
        Récupère les candlesticks pour lesquels la prédiction n’a pas encore été faite
        (prediction_ready.{horizon} absent ou != True), et pour lesquels les indicateurs sont prêts.
        """
        created_after = datetime.utcnow() - timedelta(days=2)

        try:
            query = {
                "uid": uid,
                "$or": [
                    {f"prediction_ready.{horizon}": {"$exists": False}},
                    {f"prediction_ready.{horizon}": {"$ne": True}}
                ],
                "created_at": {"$gte": created_after}
            }

            # Simple find au lieu d'un aggregation
            results = list(self.db["candlesticks"].find(query).sort("created_at", 1))

            return results

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur dans get_missing_predictions (find) : {e}", "error")
            return []


    def get_prediction_to_update(self, uid):
        seven_days_ago = datetime.utcnow() - timedelta(days=7)

        try:
            pipeline = [
                {
                    "$match": {
                        "uid": uid,
                        "indicator_id": { "$exists": True, "$ne": "N/A" },
                        "created_at": {"$gte": seven_days_ago}
                    }
                },
                {
                    "$lookup": {
                        "from": "indicators",
                        "let": { "ind_id": "$indicator_id" },
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": { "$eq": ["$_id", "$$ind_id"] }
                                }
                            }
                        ],
                        "as": "indicators"
                    }
                },
                {
                    "$unwind": {
                        "path": "$indicators",
                        "preserveNullAndEmptyArrays": False
                    }
                },
                {
                    "$sort": {
                        "created_at": 1
                    }
                }
            ]

            results = list(self.db["candlesticks"].aggregate(pipeline))

            #self.logger.send_log(f"✅ - {uid} - {len(results)} candlesticks à updater récupérés via aggregation.", "info")
            return results

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur dans get_prediction_to_update (aggregation) : {e}", "error")
            return []


    def store_prediction(self, prediction_data):
        """
        Insère une nouvelle prédiction dans la collection `predictions`.
        Ne remplace pas les documents existants.
        """
        try:
            collection = self.db["predictions"]
            prediction_data["predicted_at"] = datetime.utcnow()
            prediction_data["updated_at"] = datetime.utcnow()

            result = collection.insert_one(prediction_data)
            prediction_id = result.inserted_id

            #self.logger.send_log(f"✅ - {prediction_data['uid']} - Prédiction insérée avec _id={prediction_id}", "info")
            return prediction_id

        except Exception as e:
            self.logger.send_log(
                f"❌ - {prediction_data.get('uid')} - Erreur insertion prédiction : {e}", "error"
            )
            return None




    def update_candlestick_prediction(self, candlestick_id, uid, horizon):
        """
        Met à jour le champ 'prediction_id' dans la collection 'candlesticks'
        pour le document correspondant à 'candlestick_id'.
        Returns:
            bool: True si la mise à jour a réussi, False sinon
        """
        try:
            collection = self.db["candlesticks"]

            # Convertir les ID si besoin
            if isinstance(candlestick_id, str):
                candlestick_id = ObjectId(candlestick_id)

            result = collection.update_one(
                {"_id": candlestick_id},
                {
                    "$set": {
                        f"prediction_ready.{horizon}": True,
                        "updated_at": datetime.utcnow()
                    }
                }
            )


            if result.modified_count < 1:
                self.logger.send_log(f"⚠️ - {uid} - Aucune modification appliquée sur le candlestick {candlestick_id}", "warning")
            return True

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur lors de la mise à jour du candlestick {candlestick_id} : {e}", "error")
            return False


    def get_unsent_predictions(self, limit=500):
        """
        Récupère les prédictions qui n'ont pas encore été envoyées à Grafana.
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=2)

        try:
            collection = self.db["predictions"]
            query = {
                "$and": [
                    {
                        "$or": [
                            {"sent_to_grafana": {"$exists": False}},
                            {"sent_to_grafana": False}
                        ]
                    },
                    {
                        "created_at": {
                            "$gte": start_date,
                            "$lte": end_date
                        }
                    }
                ]
            }
            results = list(collection.find(query).sort("prediction_time", 1).limit(limit))

            if not results:
                self.logger.send_log("ℹ️ - Aucune prédiction non envoyée trouvée dans la base.", "info")
                return []

            #self.logger.send_log(f"📤 - {len(results)} prédictions à envoyer trouvées.", "info")
            return results

        except Exception as e:
            self.logger.send_log(f"❌ - Erreur lors de la récupération des prédictions non envoyées : {e}", "error")
            return []


    def update_prediction_sent_flag(self, prediction_id, sent=True):
        """
        Met à jour le champ 'sent_to_grafana' d'une prédiction avec l'ID donné.
        Returns:
            bool: True si la mise à jour a réussi, False sinon.
        """
        try:
            collection = self.db["predictions"]
            if isinstance(prediction_id, str):
                prediction_id = ObjectId(prediction_id)

            update = {
                "$set": {
                    "sent_to_grafana": sent
                }
            }

            result = collection.update_one({"_id": prediction_id}, update)
            if result.modified_count > 0:
                #self.logger.send_log(f"✅ - Prédiction {prediction_id} marquée comme {'envoyée' if sent else 'non envoyée'}.", "info")
                return True
            else:
                self.logger.send_log(f"⚠️ - Aucune mise à jour effectuée pour la prédiction {prediction_id}.", "warning")
                return False

        except Exception as e:
            self.logger.send_log(f"❌ - Erreur lors de la mise à jour de 'sent_to_grafana' pour la prédiction {prediction_id} : {e}", "error")
            return False


    def store_news_data(self, news_data):
        """
        Met à jour ou insère une prédiction dans la collection `predictions`.
        Si une prédiction avec le même `candlestick_id` existe, elle est mise à jour.
        Sinon, elle est insérée.
        """
        try:
            collection = self.db["news_data"]
            news_data["updated_at"] = datetime.utcnow()

            # Mise à jour ou insertion (upsert)
            result = collection.insert_one(news_data)

            return result.inserted_id


        except Exception as e:
            self.logger.send_log(
                f"❌ - {news_data.get('uid')} - Erreur stockage news_data : {e}", "error"
            )
            return None






