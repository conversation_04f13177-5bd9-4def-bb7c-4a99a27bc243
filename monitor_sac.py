#!/usr/bin/env python3
"""
Script de surveillance pour les processus SAC
"""

import os
import sys
import time
import subprocess
import datetime as dt
import psutil
import argparse
import logging

from common.grafana_utils import GrafanaUtils

# Configurer la journalisation locale
log_dir = "/home/<USER>/cryptobot/logs"
os.makedirs(log_dir, exist_ok=True)
logging.basicConfig(
    filename=f"{log_dir}/monitor_sac.log",
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Modifier la classe logger pour ajouter un fallback
class FallbackLogger:
    def __init__(self, grafana_logger):
        self.grafana_logger = grafana_logger
    
    def send_log(self, event, level, extra_labels=None):
        # Tenter d'envoyer à Grafana
        try:
            self.grafana_logger.send_log(event, level, extra_labels=extra_labels)
        except Exception as e:
            # Fallback vers les logs locaux
            log_msg = f"{event} - {extra_labels if extra_labels else ''}"
            if level == "error":
                logging.error(log_msg)
            elif level == "warning":
                logging.warning(log_msg)
            else:
                logging.info(log_msg)
            logging.error(f"Grafana logging failed: {str(e)}")

# Remplacer le logger
grafana_logger = GrafanaUtils(service="sac_monitor")
logger = FallbackLogger(grafana_logger)

def check_process(process_name):
    """Vérifie si un processus est en cours d'exécution."""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if process_name in ' '.join(proc.info['cmdline']):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return None

def restart_process(process_name, restart_cmd):
    """Redémarre un processus."""
    logger.send_log("restarting_process", "warning", extra_labels={"process": process_name})
    try:
        subprocess.run(restart_cmd, shell=True, check=True)
        time.sleep(5)  # Attendre que le processus démarre
        pid = check_process(process_name)
        if pid:
            logger.send_log("process_restarted", "info", extra_labels={
                "process": process_name, "pid": pid
            })
            return True
        else:
            logger.send_log("restart_failed", "error", extra_labels={"process": process_name})
            return False
    except subprocess.CalledProcessError as e:
        logger.send_log("restart_error", "error", extra_labels={
            "process": process_name, "error": str(e)
        })
        return False

def main():
    parser = argparse.ArgumentParser(description="Surveillance des processus SAC")
    parser.add_argument("--check-only", action="store_true", help="Vérifier sans redémarrer")
    args = parser.parse_args()
    
    processes = [
        {
            "name": "sac_pipeline.py",
            "restart_cmd": "cd /home/<USER>/cryptobot && python -m sac_pipeline > /home/<USER>/cryptobot/logs/sac_pipeline.log 2>&1 &"
        },
        {
            "name": "sac_export_features.py",
            "restart_cmd": "cd /home/<USER>/cryptobot && python -m sac_export_features > /home/<USER>/cryptobot/logs/sac_export.log 2>&1 &"
        },
        {
            "name": "sac_update_stops.py",
            "restart_cmd": "cd /home/<USER>/cryptobot && python -m sac_update_stops > /home/<USER>/cryptobot/logs/sac_update_stops.log 2>&1 &"
        }
    ]
    
    status = {
        "running": [],
        "stopped": [],
        "restarted": []
    }
    
    for proc in processes:
        pid = check_process(proc["name"])
        if pid:
            logger.send_log("process_running", "info", extra_labels={
                "process": proc["name"], "pid": pid
            })
            status["running"].append(proc["name"])
        else:
            logger.send_log("process_not_running", "warning", extra_labels={"process": proc["name"]})
            status["stopped"].append(proc["name"])
            
            if not args.check_only:
                if restart_process(proc["name"], proc["restart_cmd"]):
                    status["restarted"].append(proc["name"])
    
    logger.send_log("monitor_summary", "info", extra_labels={
        "running": len(status["running"]),
        "stopped": len(status["stopped"]),
        "restarted": len(status["restarted"]),
        "check_only": args.check_only
    })
    
    return 0 if not status["stopped"] or status["restarted"] else 1

if __name__ == "__main__":
    sys.exit(main())
