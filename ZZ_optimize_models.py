from flask import Flask, request, jsonify
import pandas as pd
import numpy as np
import xgboost as xgb
import talib
import threading
import time
import requests
import math
import optuna
import shap
import os
import csv
import copy
from datetime import datetime, timedelta, timezone
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, mean_squared_error
from sklearn.preprocessing import StandardScaler
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from common.indicators_utils import IndicatorsUtils
from common.models_utils import ModelsUtils


SERVICE = "optimize_models"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
indicator = IndicatorsUtils(service=SERVICE)
models = ModelsUtils(service=SERVICE)

NB_THREADS = 5
N_TRIALS = 100


app = Flask(__name__)






def suggest_xgboost_params(trial):
    """
    Suggère des hyperparamètres XGBoost pour une régression via Optuna.
    """
    params = {
        "n_estimators": trial.suggest_int("n_estimators", 300, 1200),
        "max_depth": trial.suggest_int("max_depth", 3, 10),
        "learning_rate": trial.suggest_float("learning_rate", 0.005, 0.05, log=True),
        "subsample": trial.suggest_float("subsample", 0.6, 1.0),
        "colsample_bytree": trial.suggest_float("colsample_bytree", 0.5, 1.0),
        "colsample_bylevel": trial.suggest_float("colsample_bylevel", 0.5, 1.0),
        "reg_lambda": trial.suggest_float("reg_lambda", 1e-3, 10.0, log=True),  # L2
        "reg_alpha": trial.suggest_float("reg_alpha", 1e-3, 5.0, log=True),     # L1
        "min_child_weight": trial.suggest_float("min_child_weight", 1.0, 20.0),
        "gamma": trial.suggest_float("gamma", 0.0, 5.0),
        "max_delta_step": trial.suggest_float("max_delta_step", 0.0, 10.0),
        "grow_policy": trial.suggest_categorical("grow_policy", ["depthwise", "lossguide"]),
        "tree_method": "hist",
        "sampling_method": "uniform",
        "eval_metric": "rmse",
        "random_state": 42,
        "n_jobs": 2,
        "early_stopping_rounds": 50,
    }
    return params


def suggest_data_depth(trial, horizon_hour, interval_min=5, min_ratio=2.0, max_ratio=6.0, 
                       shift=None, indicator_periods=None):
    """
    Propose une profondeur d'entraînement en nombre de points,
    avec marge de sécurité liée au shift et à la taille des indicateurs.
    """
    min_depth_hours = int(horizon_hour * min_ratio)
    max_depth_hours = int(horizon_hour * max_ratio)

    # Suggestion Optuna (en heures)
    depth_hours = trial.suggest_int("depth_hours", min_depth_hours, max_depth_hours)
    depth_points = int((depth_hours * 60) / interval_min)

    # Sécurité : on rajoute les points qu'on perd au début (à cause du shift)
    if shift is not None and indicator_periods is not None:
        max_period = max(indicator_periods.values())
        buffer = shift + max_period + 10  # marge de 10 points de sécurité
        depth_points += buffer

    return depth_points




def start_evaluate_indicator_params_classification(trial, horizon, uid, data, indicator_params, safe_shift_points):
    horizon_hour = horizon["horizon_hour"]
    shift_value = horizon["shift_value"]

    params = suggest_xgboost_params(trial)
    params["eval_metric"] = "logloss"
    params["objective"] = "binary:logistic"

    depth = suggest_data_depth(trial, horizon_hour, shift=shift_value, indicator_periods=indicator_params)

    try:
        df = indicator.generate_training_sample(data.copy(), indicator_params, uid, shift_value, horizon_hour, depth, safe_shift_points)
    except Exception as e:
        logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Erreur features : {e}", "error")
        raise optuna.TrialPruned()

    # Vérification colonne log_return
    if "log_return" not in df.columns:
        logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Colonne 'log_return' manquante", "error")
        raise optuna.TrialPruned()

    # Création target binaire
    SEUIL_POURCENT = horizon_hour * ( 0.001 / 3)
    seuil_log = np.log(1 + SEUIL_POURCENT)
    logger.send_log(f" - {uid} - {horizon['name']} - Seuil = {SEUIL_POURCENT}", "debug")
    df["target"] = (df["log_return"] > seuil_log).astype(int)

    logger.send_log(
        f"🔎 - {uid} - {horizon['name']} - Classification - log_return min={df['log_return'].min():.4f}, max={df['log_return'].max():.4f}, target distribution={df['target'].value_counts(normalize=True).to_dict()}",
        "debug"
    )

    ratio_pos = df["target"].mean()
    if ratio_pos > 0 and ratio_pos < 1:
        scale_pos_weight = (1 - ratio_pos) / ratio_pos
        params["scale_pos_weight"] = scale_pos_weight

    if df["target"].nunique() < 2:
        logger.send_log(f"⚠️ - {uid} - Classification - {horizon['name']} - Target constante : {df['target'].unique().tolist()}", "warning")
        raise optuna.TrialPruned()

    # Suppression sécurisée des colonnes inutiles
    cols_to_drop = [
        "timestamp", "future_log_price", "highPrice", "lowPrice",
        "lastPrice", "past_base_price", "log_return"
    ]
    df.drop(columns=[col for col in cols_to_drop if col in df.columns], inplace=True)

    y = df["target"]
    X = df.drop(columns=["target"])

    n_samples = len(X)
    n_splits = min(3, n_samples - 1)
    if n_splits < 2:
        logger.send_log(f"⚠️ - {uid} - Classification - {horizon['name']} - Pas assez de données (n={n_samples})", "warning")
        raise optuna.TrialPruned()

    # TimeSeriesSplit avec filtrage
    tscv = TimeSeriesSplit(n_splits=n_splits)
    valid_splits = []
    for train_index, val_index in tscv.split(X):
        y_val_split = y.iloc[val_index]
        if y_val_split.nunique() < 2:
            logger.send_log(
                f"⚠️ - {uid} - Classification - {horizon['name']} - Fold ignoré (target constante : {y_val_split.unique().tolist()})",
                "warning"
            )
            continue
        valid_splits.append((train_index, val_index))

    if len(valid_splits) == 0:
        logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Aucun fold TimeSeriesSplit valide", "error")
        raise optuna.TrialPruned()

    scores = []
    all_metrics = []

    for train_index, val_index in valid_splits:
        X_train, X_val = X.iloc[train_index], X.iloc[val_index]
        y_train, y_val = y.iloc[train_index], y.iloc[val_index]

        model = xgb.XGBClassifier(**params)
        try:
            model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                verbose=False,
            )

            y_val_pred_proba = model.predict_proba(X_val)[:, 1]
            y_val_pred = (y_val_pred_proba >= 0.5).astype(int)

        except Exception as e:
            logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Erreur entraînement : {e}", "error")
            raise optuna.TrialPruned()

        try:
            metrics = models.compute_combined_score_classification(
                y_val, y_val_pred, y_val_pred_proba,
                horizon_hour=horizon_hour
            )
            scores.append(metrics["combined_score"])
            all_metrics.append(metrics)

        except Exception as e:
            logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Erreur métriques : {e}", "error")
            raise optuna.TrialPruned()

    # Moyenne robuste
    avg_metrics = {
        k: float(np.nanmean([m[k] for m in all_metrics]))
        for k in all_metrics[0].keys()
    }

    if np.isnan(avg_metrics["combined_score"]) or avg_metrics["combined_score"] > 0.90:
        logger.send_log(
            f"⚠️ - {uid} - Classification - {horizon['name']} - Combined score trop élevé ({avg_metrics['combined_score']:.3f})", "warning"
        )
        raise optuna.TrialPruned()

    # SHAP sécurisé
    try:
        important_features, shap_df = models.get_important_features_from_shap(model, X)
        ratio_features = len(important_features) / X.shape[1]
        if ratio_features < 0.1:
            logger.send_log(
                f"⚠️ - {uid} - Classification - {horizon['name']} - Trop peu de features importantes (ratio={ratio_features:.2f})",
                "warning"
            )
            raise optuna.TrialPruned()
    except Exception as e:
        logger.send_log(f"❌ - {uid} - Classification - {horizon['name']} - Erreur SHAP : {e}", "error")
        raise optuna.TrialPruned()

    # Résultat du trial
    trial.set_user_attr("best_model", model)
    trial.set_user_attr("depth", depth)
    trial.set_user_attr("indicator_params", params)
    trial.set_user_attr("used_features", X)
    trial.set_user_attr("evaluation", avg_metrics)
    trial.set_user_attr("combined_score", avg_metrics["combined_score"])

    logger.send_log(f"✅ - {uid} - Classification - {horizon['name']} - Combined Score = {avg_metrics['combined_score']:.3f}", "info")
    return avg_metrics["combined_score"]  # Optuna minimise









def start_evaluate_indicator_params(trial, horizon, uid, data, indicator_params, safe_shift_points):
    horizon_hour = horizon["horizon_hour"]
    shift_value = horizon["shift_value"]

    params = suggest_xgboost_params(trial)

    depth = suggest_data_depth(trial, horizon_hour)


    # Génération des features via indicateurs
    try:
        df = indicator.generate_training_sample(data.copy(), indicator_params, uid, shift_value, horizon_hour, depth, safe_shift_points)
    except Exception as e:
        logger.send_log(f"❌ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Erreur dans generate_training_sample : {e}", "error")
        raise optuna.TrialPruned()

    #logger.send_log(f"⚠️ - {uid} - {horizon['name']} - length df = {len(df)}", "warning")
    # Vérification target
    if "log_return" not in df.columns:
        logger.send_log(f"❌ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Données invalides, 'log_return' manquant", "error")
        raise optuna.TrialPruned()
    
    # Retrait colonnes inutiles
    df.drop(columns=["timestamp", "future_log_price", "highPrice", "lowPrice", "lastPrice", "past_base_price"], inplace=True, errors="ignore")

    y = df["log_return"]
    X = df.drop(columns=["log_return"])

    xgb_model = None  # Initialisation sécurisée avant la boucle

    n_samples = len(X)
    n_splits = min(3, n_samples - 1)  # au moins 2 samples nécessaires

    if n_splits < 2:
        logger.send_log(f"⚠️ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Données insuffisantes pour TimeSeriesSplit (samples={n_samples})", "warning")
        raise optuna.TrialPruned()

    tscv = TimeSeriesSplit(n_splits=n_splits)

    combined_scores = []
    all_metrics = []

    for train_index, val_index in tscv.split(X):
        X_train, X_val = X.iloc[train_index], X.iloc[val_index]
        y_train_raw, y_val_raw = y.iloc[train_index], y.iloc[val_index]

        # 👉 Standardisation locale de la target
        scaler = StandardScaler()
        y_train_scaled = scaler.fit_transform(y_train_raw.values.reshape(-1, 1)).flatten()
        y_val_scaled = scaler.transform(y_val_raw.values.reshape(-1, 1)).flatten()


        xgb_model = xgb.XGBRegressor(**params)

        try:
            xgb_model.fit(
                X_train, y_train_scaled,
                eval_set=[(X_train, y_train_scaled), (X_val, y_val_scaled)],
                verbose=False
            )

            # Prédictions dans l’espace standardisé
            y_train_pred_scaled = xgb_model.predict(X_train)
            y_val_pred_scaled = xgb_model.predict(X_val)

            # 🔁 Inversion pour revenir à l’échelle réelle
            y_train_pred = scaler.inverse_transform(y_train_pred_scaled.reshape(-1, 1)).flatten()
            y_val_pred = scaler.inverse_transform(y_val_pred_scaled.reshape(-1, 1)).flatten()

        except Exception as e:
            logger.send_log(f"❌ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Erreur entraînement XGBoost : {e}", "error")
            raise optuna.TrialPruned()

        try:
            metrics = models.compute_combined_score(
                y_train_pred, y_train_raw,  # ⚠️ Comparaison dans l'échelle réelle
                y_val_pred, y_val_raw
            )

            if not isinstance(metrics, dict) or "combined_score" not in metrics:
                raise ValueError("Combined score manquant ou invalide dans les métriques.")

            combined_scores.append(metrics["combined_score"])
            all_metrics.append(metrics)

        except Exception as e:
            logger.send_log(f"❌ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Erreur dans compute_combined_score : {e}", "error")
            raise optuna.TrialPruned()

    average_combined_score = np.mean(combined_scores)

    # Moyenne des métriques
    avg_metrics = {
        k: float(np.mean([m[k] for m in all_metrics]))
        for k in all_metrics[0].keys()
    }

    
    # ⛔ Prune si modèle ne fait pas mieux qu’une prédiction constante (faible corrélation ET mauvaise direction)
    if avg_metrics["correlation_val"] < 0.1 and avg_metrics["directional_accuracy"] < 0.6:
        logger.send_log(f"⚠️ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Corrélation très faible ET mauvaise direction ({avg_metrics}), pruning", "warning")
        raise optuna.TrialPruned()

    # ⚠️ Prune si sur-apprentissage détecté (forte corrélation train, mais mauvaise val)
    if avg_metrics["correlation_train"] > 0.6 and avg_metrics["correlation_val"] < 0.0:
        logger.send_log(f"⚠️ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Overfit détecté (corrélation train > 0.6 et val < 0)", "warning")
        raise optuna.TrialPruned()

    # ❌ Prune si score combiné trop mauvais
    if average_combined_score > 1.3:
        logger.send_log(f"⚠️ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Combined score trop élevé ({average_combined_score}), pruning", "warning")
        raise optuna.TrialPruned()
    
    # ⚠️ Pruning si trop peu de features utiles (selon SHAP)
    important_features, shap_importance_df = models.get_important_features_from_shap(xgb_model, X)
    ratio_features = len(important_features) / X.shape[1]
    if ratio_features < 0.1:
        logger.send_log(f"⚠️ - {uid} - Regression - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Seulement {len(important_features)} / {X.shape[1]} features utiles (ratio={ratio_features:.2f}), pruning","warning")
        raise optuna.TrialPruned()
    

    # Vérifie que xgb_model est bien défini avant de stocker dans Optuna
    if xgb_model is not None:
        trial.set_user_attr("best_model", xgb_model)
    else:
        logger.send_log(f"⚠️ - {uid} - Regression - {horizon['name']} - Aucun modèle entraîné avec succès !", "warning")
        raise optuna.TrialPruned()

    # Stockage dans Optuna
    trial.set_user_attr("depth", depth)  # prédits, inversés
    trial.set_user_attr("indicator_params", params)
    trial.set_user_attr("used_features", X)
    trial.set_user_attr("combined_score", average_combined_score)
    trial.set_user_attr("evaluation", avg_metrics)
    trial.set_user_attr("y_test", y_val_raw.tolist())  # réels
    trial.set_user_attr("y_pred", y_val_pred.tolist())  # prédits, inversés

    if "timestamp" in X_val.columns:
        trial.set_user_attr("timestamps", [ts.isoformat() for ts in pd.to_datetime(X_val["timestamp"])])

    
    logger.send_log(f"📊 - {uid} - {horizon['name']} - Trial #{trial.number + 1} / {N_TRIALS} - Combined score = {average_combined_score}", "info")
    #logger.send_log(f"ℹ️ - {uid} - {horizon['name']} - Combined score du trial = {average_combined_score}", "info")
    #logger.send_log(f"🔎 - {uid} - {horizon['name']} - Params du trial réussi : {params}", "debug")
    #save_trial_to_csv(uid, horizon['name'], avg_metrics, params)
    return average_combined_score



def optimize_regression(uid, model, candlesticks, indicator_params):
    type = "regression"
    model_name = f"xgboost_model_{uid}_{model['name']}_{type}"
    model['full_name'] = model_name

    best_previous_params = mongo.get_hyperparams(model, uid, type, model['horizon_hour'])
    try:
        study = optuna.create_study(
            directions=["minimize"],
            study_name="indicator_multiobj",  
            sampler=optuna.samplers.TPESampler(n_startup_trials=10, multivariate=True)
        )

        if best_previous_params:
            # ✅ Vérifie si les paramètres sont récents (< 6h)
            updated_at = best_previous_params.get("updated_at")
            if updated_at and updated_at > datetime.utcnow() - timedelta(hours=3):
                logger.send_log(f"⏱️ - {uid} - Regression - {model['name']} - Paramètres déjà optimisés récemment ({updated_at}). Sortie anticipée.","info")
                return False 
            #logger.send_log(f"🔄 - {uid} - Meilleurs paramètres précédents récupérés : {previous_best_params}","info")
            study.enqueue_trial(best_previous_params["best_params"])


        study.optimize(
            lambda trial: start_evaluate_indicator_params(trial, model, uid, candlesticks, indicator_params["indicator_params"], indicator_params['safe_shift_points']),
            n_trials=N_TRIALS
        )

        # ✅ Vérifie si aucun trial n’a réussi
        if not study.best_trials:
            logger.send_log(f"❌ - {uid} - Regression - {model['name']} - Aucun trial n'a été validé avec succès", "error")
            return False  # On passe au modèle suivant

        # Choix du meilleur trial
        best_trial = sorted(
            study.best_trials,
            key=lambda t: t.values[0]
        )[0]

        

        # ⬇️ Récupération dans des variables
        indicator_params = best_trial.user_attrs.get("indicator_params")
        depth = best_trial.user_attrs.get("depth")
        evaluation = best_trial.user_attrs.get("evaluation")
        used_features = best_trial.user_attrs.get("used_features")
        best_model = best_trial.user_attrs.get("best_model")
        best_params = best_trial.params

        logger.send_log(f"🏆 - {uid} - Regression - {model['name']} - Meilleur score ={evaluation['combined_score']}", "info")

        try:
            important_features, shap_df = models.get_important_features_from_shap(best_model, used_features, threshold=0.001)
            shap_dict = dict(zip(shap_df["feature"], shap_df["mean_abs_shap"]))

        except Exception as e:
            logger.send_log(f"⚠️ - {uid} - Regression - {model['name']} - Erreur lors de get_important_features_from_shap : {e}","error")
            return False
        
        
        mongo.create_hyperparams(model, uid, evaluation, best_params, important_features, shap_dict, depth)
        data = {
            "uid": uid,
            "horizon": model['name'],
            "type": "regression",
            "data_depth": depth,
            "indicator_params": indicator_params,
            "evaluation": evaluation,
            "score_features": shap_dict
        }
        logger.send_raw_data_log(data, metric="models_params")

    except Exception as e:
            logger.send_log(f"⚠️ - {uid} - Regression - {model['name']} - Erreur lors de l'optimisation en régression : {e}","error")
            return False
    


def optimize_classification(uid, model, candlesticks, indicator_params):
    type = "classification"
    model_name = f"xgboost_model_{uid}_{model['name']}_{type}"
    model['full_name'] = model_name

    

    best_previous_params = mongo.get_hyperparams(model, uid, type, model['horizon_hour'])
    try:
        study = optuna.create_study(
            directions=["minimize"],
            study_name="indicator_multiobj",  
            sampler=optuna.samplers.TPESampler(n_startup_trials=10, multivariate=True)
        )

        if best_previous_params:
            # ✅ Vérifie si les paramètres sont récents (< 6h)
            updated_at = best_previous_params.get("updated_at")
            if updated_at and updated_at > datetime.utcnow() - timedelta(hours=3):
                logger.send_log(f"⏱️ - {uid} - {type} - {model['name']} - Paramètres déjà optimisés récemment ({updated_at}). Sortie anticipée.","info")
                return False 
            #logger.send_log(f"🔄 - {uid} - Meilleurs paramètres précédents récupérés : {previous_best_params}","info")
            study.enqueue_trial(best_previous_params["best_params"])


        study.optimize(
            lambda trial: start_evaluate_indicator_params_classification(trial, model, uid, candlesticks, indicator_params["indicator_params"], indicator_params['safe_shift_points']),
            n_trials=N_TRIALS
        )

        # ✅ Vérifie si aucun trial n’a réussi
        if not study.best_trials:
            logger.send_log(f"❌ - {uid} - {type} - {model['name']} - Aucun trial n'a été validé avec succès", "error")
            return False  # On passe au modèle suivant

        # Choix du meilleur trial
        best_trial = sorted(
            study.best_trials,
            key=lambda t: t.values[0]
        )[0]

        

        # ⬇️ Récupération dans des variables
        indicator_params = best_trial.user_attrs.get("indicator_params")
        depth = best_trial.user_attrs.get("depth")
        evaluation = best_trial.user_attrs.get("evaluation")
        used_features = best_trial.user_attrs.get("used_features")
        best_model = best_trial.user_attrs.get("best_model")
        best_params = best_trial.params

        logger.send_log(f"🏆 - {uid} - {type} - {model['name']} - Meilleur score ={evaluation['roc_auc']}", "info")

        try:
            important_features, shap_df = models.get_important_features_from_shap(best_model, used_features, threshold=0.001)
            shap_dict = dict(zip(shap_df["feature"], shap_df["mean_abs_shap"]))

        except Exception as e:
            logger.send_log(f"⚠️ - {uid} - {type} - {model['name']} - Erreur lors de get_important_features_from_shap : {e}","error")
            return False
        
        
        mongo.create_hyperparams(model, uid, evaluation, best_params, important_features, shap_dict, depth)
        data = {
            "uid": uid,
            "horizon": model['name'],
            "type": "regression",
            "data_depth": depth,
            "indicator_params": indicator_params,
            "evaluation": evaluation,
            "score_features": shap_dict
        }
        logger.send_raw_data_log(data, metric="models_params")

    except Exception as e:
            logger.send_log(f"⚠️ - {uid} - {type} - {model['name']} - Erreur lors de l'optimisation en classification : {e}","error")
            return False





def process_crypto(uid):
    models_data = models.get_horizon()
    for horizon in models_data:
        model = copy.deepcopy(horizon)  # ⬅️ deep copy ici
        
        try:
            until_date = datetime.utcnow() #- timedelta(minutes=minutes_depth)
            
            candlesticks = mongo.get_candlesticks(uid, until_date)

            indicator_params = mongo.get_indicatorsparams(model["name"], uid, model["horizon_hour"])
            if indicator_params is None:
                #logger.send_log(f"⚠️ - {uid} - {model['name']} - Aucun paramètre d'indicateur disponible. Annulation.","warning")
                continue

            
            

            optimize_regression(uid, model, candlesticks, indicator_params)


            optimize_classification(uid, model, candlesticks, indicator_params)

    


        except Exception as e:
            logger.send_log(f"❌ - Erreur lors d'une tentative d'optimisation via Optuna : {e}", "error")
            continue

    return True

            



def optimize_features_for_all():
    trading_uids = mongo.get_trading_pairs()
    max_workers = 1  # À adapter selon ton CPU

    if not trading_uids:
        logger.send_log("❌ Aucune crypto en trading trouvée.", "error")
        return

    logger.send_log(f"📌 Lance de l'optimisation pour {len(trading_uids)} cryptos...", "info")

    futures = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for uid in trading_uids:
            futures.append(executor.submit(process_crypto, uid))

        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                logger.send_log(f"⚠️ Erreur pendant le calcul pour une crypto : {e}", "error")

    logger.send_log("✅ Optimisations terminés.", "info")
    return True




def run_parallel_optimizations():
    """
    Exécute l'optimisation des paramètres des modèles pour chaque horizon pour chaque crypto en mode parallèle.
    """
    logger.send_log(f"🚀 Lancement des optimisations en parallèle","info")

    # 🔥 Exécuter les optimisations en parallèle avec ThreadPoolExecutor
    try:
        optimize_features_for_all()
    except Exception as e:
        logger.send_log(f"❌ - Erreur lors de l'optimisation en parrallèle : {e}", "error")


    logger.send_log("✅ Toutes les optimisations sont terminées !","info")
    return True



def main():
    INTERVAL = 5
    error_count = 0

    while True:
        try:
            logger.send_log("🚀 Lancement d'un nouveau cycle d'optimisation des paramètres des modèles...", "info")
            success = run_parallel_optimizations()

            if success:
                logger.send_log(f"✅ Cycle terminé. Prochain dans {INTERVAL} sec...", "info")
                error_count = 0
            else:
                logger.send_log("⚠️ Aucune optimisation réalisée.", "warning")

        except Exception as e:
            error_count += 1
            logger.send_log(f"❌ Erreur dans `main()` : {e} (tentative {error_count})", "error")
            if error_count >= 5:
                logger.send_log("🚨 Trop d'erreurs, arrêt forcé.", "critical")
                break

        time.sleep(60)

    logger.send_log("👋 Arrêt du programme terminé proprement.", "info")

if __name__ == "__main__":
    main()
