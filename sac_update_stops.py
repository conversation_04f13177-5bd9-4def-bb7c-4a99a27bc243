#!/usr/bin/env python3
# sac_update_stops.py - <PERSON>ript dédié à la mise à jour des trailing stops

import time
import datetime as dt
import argparse
import pathlib
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# Configuration
UPDATE_INTERVAL = 60  # Secondes entre chaque mise à jour
TRADE_SIGNALS_COLLECTION = "trade_signals"

# Logger
logger = GrafanaUtils(service="trailing_stop")

def parse_args():
    """Parse les arguments de ligne de commande."""
    parser = argparse.ArgumentParser(description="Mise à jour des trailing stops")
    parser.add_argument("--uid", help="UID spécifique à traiter")
    parser.add_argument("--interval", type=int, default=UPDATE_INTERVAL, 
                        help="Intervalle de mise à jour en secondes")
    parser.add_argument("--once", action="store_true", 
                        help="Exécuter une seule fois puis quitter")
    return parser.parse_args()

def get_current_price(mongo, uid):
    """Récupère le prix actuel pour un UID donné."""
    try:
        # Méthode 1: Utiliser le dernier signal
        latest_signal = mongo.db[TRADE_SIGNALS_COLLECTION].find_one(
            {"uid": uid},
            sort=[("created_at", -1)]
        )
        
        if latest_signal and "price" in latest_signal:
            return latest_signal["price"]
        
        # Méthode 2: Utiliser les features récentes
        latest_feature = mongo.db["forecast_features"].find_one(
            {"uid": uid},
            sort=[("created_at", -1)]
        )
        
        if latest_feature:
            for price_field in ["close", "lastPrice", "last_price", "last"]:
                if price_field in latest_feature and latest_feature[price_field]:
                    return float(latest_feature[price_field])
        
        # Méthode 3: Utiliser une collection dédiée aux prix
        latest_price = mongo.db["latest_prices"].find_one({"uid": uid})
        if latest_price and "price" in latest_price:
            return float(latest_price["price"])
        
        logger.send_log("price_not_found", "warning", extra_labels={"uid": uid})
        return None
        
    except Exception as e:
        logger.send_log("price_fetch_error", "warning", extra_labels={
            "uid": uid,
            "error": str(e)
        })
        return None

def update_trailing_stops(mongo, uid=None):
    """Met à jour les trailing stops en fonction des prix actuels."""
    try:
        # Construire la requête pour récupérer les signaux actifs
        query = {
            "signal": {"$in": ["BUY", "SELL"]},
            "trailing_stop": {"$exists": True},
            "closed_at": {"$exists": False}
        }
        if uid:
            query["uid"] = uid
        
        # Récupérer tous les signaux actifs avec trailing stop
        active_signals = list(mongo.db[TRADE_SIGNALS_COLLECTION].find(query))
        
        if not active_signals:
            return 0
            
        logger.send_log("updating_trailing_stops", "info", extra_labels={
            "active_signals": len(active_signals),
            "uid_filter": uid if uid else "all"
        })
        
        updates_count = 0
        triggers_count = 0
        
        for signal in active_signals:
            try:
                signal_uid = signal.get("uid")
                entry_price = signal.get("price")
                entry_signal = signal.get("signal")
                current_stop = signal.get("trailing_stop")
                trailing_pct = signal.get("trailing_pct", 0.015)  # 1.5% par défaut
                max_favorable_price = signal.get("max_favorable_price", entry_price)
                
                if not all([signal_uid, entry_price, entry_signal, current_stop]):
                    continue
                
                # Récupérer le prix actuel
                current_price = get_current_price(mongo, signal_uid)
                if not current_price:
                    continue
                
                # Vérifier si le stop est déclenché
                stop_triggered = False
                if entry_signal == "BUY" and current_price <= current_stop:
                    stop_triggered = True
                elif entry_signal == "SELL" and current_price >= current_stop:
                    stop_triggered = True
                
                if stop_triggered:
                    # Calculer le gain/perte
                    if entry_signal == "BUY":
                        gain = (current_stop / entry_price) - 1
                    else:  # SELL
                        gain = 1 - (current_stop / entry_price)
                    
                    # Marquer le signal comme fermé
                    mongo.db[TRADE_SIGNALS_COLLECTION].update_one(
                        {"_id": signal["_id"]},
                        {"$set": {
                            "closed_at": dt.datetime.utcnow(),
                            "close_price": current_stop,
                            "close_reason": "trailing_stop",
                            "final_gain": round(gain, 4)
                        }}
                    )
                    
                    logger.send_log("trailing_stop_triggered", "info", extra_labels={
                        "uid": signal_uid,
                        "entry_price": round(entry_price, 2),
                        "stop_price": round(current_stop, 2),
                        "gain_pct": round(gain * 100, 2),
                        "signal": entry_signal
                    })
                    
                    triggers_count += 1
                    continue
                
                # Mettre à jour le prix le plus favorable
                new_max_price = max_favorable_price
                if entry_signal == "BUY" and current_price > max_favorable_price:
                    new_max_price = current_price
                elif entry_signal == "SELL" and current_price < max_favorable_price:
                    new_max_price = current_price
                
                # Calculer le nouveau niveau de stop si le prix le plus favorable a changé
                new_stop = current_stop
                if new_max_price != max_favorable_price:
                    if entry_signal == "BUY":
                        new_stop = new_max_price * (1 - trailing_pct)
                    else:  # SELL
                        new_stop = new_max_price * (1 + trailing_pct)
                    
                    # Ne jamais reculer le stop (toujours protéger les gains)
                    if entry_signal == "BUY":
                        new_stop = max(new_stop, current_stop)
                    else:  # SELL
                        new_stop = min(new_stop, current_stop)
                
                # Mettre à jour le stop si nécessaire
                if new_stop != current_stop or new_max_price != max_favorable_price:
                    mongo.db[TRADE_SIGNALS_COLLECTION].update_one(
                        {"_id": signal["_id"]},
                        {"$set": {
                            "trailing_stop": round(new_stop, 2),
                            "max_favorable_price": new_max_price,
                            "stop_updated_at": dt.datetime.utcnow(),
                            "current_gain": round((current_price / entry_price - 1) * 100, 2) if entry_signal == "BUY" else round((1 - current_price / entry_price) * 100, 2)
                        }}
                    )
                    
                    logger.send_log("trailing_stop_updated", "debug", extra_labels={
                        "uid": signal_uid,
                        "previous_stop": round(current_stop, 2),
                        "new_stop": round(new_stop, 2),
                        "current_price": round(current_price, 2),
                        "max_price": round(new_max_price, 2),
                        "signal": entry_signal
                    })
                    
                    updates_count += 1
                    
            except Exception as e:
                logger.send_log("signal_processing_error", "warning", extra_labels={
                    "uid": signal.get("uid", "unknown"),
                    "error": str(e)
                })
        
        if updates_count > 0 or triggers_count > 0:
            logger.send_log("trailing_stops_summary", "info", extra_labels={
                "updates": updates_count,
                "triggers": triggers_count,
                "total_signals": len(active_signals)
            })
            
        return updates_count + triggers_count
                
    except Exception as e:
        logger.send_log("update_trailing_stops_error", "error", extra_labels={
            "error": str(e),
            "uid": uid if uid else "all"
        })
        return 0

def main():
    """Fonction principale."""
    args = parse_args()
    
    # Connexion à MongoDB
    mongo = MongoUtils()
    mongo.connect(service="trailing_stop_updater")
    
    logger.send_log("service_started", "info", extra_labels={
        "interval": args.interval,
        "once": args.once,
        "uid": args.uid if args.uid else "all"
    })
    
    try:
        if args.once:
            # Exécuter une seule fois
            count = update_trailing_stops(mongo, args.uid)
            logger.send_log("one_time_execution", "info", extra_labels={
                "updates": count,
                "uid": args.uid if args.uid else "all"
            })
        else:
            # Mode service continu
            while True:
                try:
                    update_trailing_stops(mongo, args.uid)
                except Exception as e:
                    logger.send_log("update_cycle_error", "error", extra_labels={"error": str(e)})
                
                time.sleep(args.interval)
    except KeyboardInterrupt:
        logger.send_log("service_stopped", "info", extra_labels={"reason": "keyboard_interrupt"})
    except Exception as e:
        logger.send_log("service_error", "error", extra_labels={"error": str(e)})
    finally:
        mongo.disconnect()

if __name__ == "__main__":
    main()
