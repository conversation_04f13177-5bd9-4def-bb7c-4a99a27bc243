#!/usr/bin/env python3
"""
audit_forecasts.py – audit + enrichment des features toutes les 5 minutes
"""

from __future__ import annotations

import sys, time, re, json, traceback, pathlib, os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict
import  threading

import numpy as np
import pandas as pd
from scipy.stats import spearmanr
from sklearn.metrics import (
    mean_absolute_error,
    mean_absolute_percentage_error,
    mean_squared_error,
)
from pymongo.errors import DuplicateKeyError

# ── path local ──────────────────────────────────────────────────────────────
ROOT = pathlib.Path(__file__).resolve().parent
for p in (ROOT, ROOT.parent):
    sys.path.insert(0, str(p))

from common.mongo_utils import MongoUtils          # util maison
from common.grafana_utils import GrafanaUtils      # Ajouter cette ligne

# Initialiser le logger global
logger = GrafanaUtils(service="audit_forecasts")

# Ajouter un log de démarrage explicite
print(f"[{datetime.now(timezone.utc).isoformat()}] DÉMARRAGE AUDIT_FORECASTS.PY")
logger.send_log("script_started", "info", extra_labels={
    "pid": os.getpid(),
    "time": datetime.now(timezone.utc).isoformat()
})

def start_keepalive_thread():
    """Démarre un thread qui envoie périodiquement un signal de vie à Grafana."""
    def send_keepalive():
        while True:
            try:
                # Envoyer un signal de vie toutes les 5 minutes
                safe_log("keepalive_heartbeat", "info", {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "pid": os.getpid()
                }, force=True)
                time.sleep(300)  # 5 minutes
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Erreur dans le thread keepalive: {e}")
                time.sleep(60)  # Attendre 1 minute en cas d'erreur
    
    # Démarrer le thread
    keepalive_thread = threading.Thread(target=send_keepalive, daemon=True)
    keepalive_thread.start()
    print(f"[{datetime.now(timezone.utc).isoformat()}] Thread keepalive démarré")
    return keepalive_thread

keepalive_thread = start_keepalive_thread()

def safe_log(message, level="info", extra_labels=None, force=False):
    """Fonction de log sécurisée qui limite les appels à Grafana."""
    # Toujours afficher dans la console
    timestamp = datetime.now(timezone.utc).isoformat()
    print(f"[{timestamp}] {message}")
    
    # Limiter les logs envoyés à Grafana
    if force or level in ["error", "warning"]:
        # Limiter les labels pour éviter les erreurs 429
        limited_labels = {}
        if extra_labels:
            # Sélectionner uniquement les labels essentiels
            essential_keys = ["uid", "error", "cycle"]
            for key in essential_keys:
                if key in extra_labels:
                    limited_labels[key] = extra_labels[key]
        
        # Envoyer le log à Grafana
        try:
            logger.send_log(message, level, extra_labels=limited_labels)
        except Exception as e:
            print(f"[{timestamp}] Erreur d'envoi de log à Grafana: {e}")

# Liste de cryptos par défaut si aucune n'est trouvée
CRYPTO_TEST = ["BTCEUR", "ETHEUR", "BNBEUR", "XRPEUR", "ADAEUR", "DOGEEUR", "SOLEUR"]

# ─────────────────── Diebold-Mariano (impl. locale) ─────────────────────────
def dm_test(e1, e2, h: int = 1, crit: str = "MSE"):
    """
    Test de Diebold-Mariano pour comparer deux séries d'erreurs de prévision.
    Retourne (stat, p-value) du test DM.
    """
    from scipy.stats import norm
    e1, e2 = np.asarray(e1), np.asarray(e2)
    
    # Validation des entrées
    if len(e1) != len(e2):
        raise ValueError("vectors length mismatch")
    if len(e1) < h + 1:
        raise ValueError(f"Not enough data points for horizon h={h}")
        
    # Calcul de la différence selon le critère
    if   crit == "MSE":  d = e1**2 - e2**2
    elif crit == "MAD":  d = np.abs(e1) - np.abs(e2)
    elif crit == "MAPE": d = np.abs(e1)/(np.abs(e2) + 1e-8) - 1
    else: raise ValueError("crit ∉ {MSE,MAD,MAPE}")
    
    # Protection contre les valeurs extrêmes
    if np.isnan(d).any() or np.isinf(d).any():
        d = np.nan_to_num(d, nan=0.0, posinf=1e6, neginf=-1e6)
        
    # Calcul de la statistique DM avec protection contre division par zéro
    var_d = d.var(ddof=1)
    if var_d <= 1e-10:
        return 0.0, 1.0  # Pas de différence significative
        
    dm_stat = d.mean() / np.sqrt((var_d + 1e-10) / len(d))
    return dm_stat, 2 * norm.cdf(-abs(dm_stat))

# ═════════════════════════ class AuditForecasts ═════════════════════════════
class AuditForecasts:
    # news / on-chain
    RSS_KEEP = [
        "mean_sentiment", "trend_encoded",
        "impact_low", "impact_moderate", "impact_high",
        "mean_geo_score", "max_geo_score",
    ]
    ONCHAIN_KEEP = [
        "netflow", "netflow_ratio", "netflow_z_score",
        "flow_sentiment_score",
        "whale_activity", "whale_dominance_pct",
        "inflow_spike_detected", "outflow_spike_detected",
        "inflow_count", "outflow_count",
    ]
    # nouveaux signaux
    ORDERFLOW_KEEP = [
        "bid_qty_1", "ask_qty_1", "bid_px_1", "ask_px_1",
        "bid_qty_5", "ask_qty_5",
    ]
    VOLIV_KEEP = ["iv_30d", "iv_60d"]

    # ---------------------------------------------------------------------
    def __init__(self, uid: str, mongo: MongoUtils = None):
        self.uid = uid
        self.mongo = mongo or MongoUtils()
        # Utiliser le logger global si disponible, sinon utiliser celui de mongo
        self.logger = logger if 'logger' in globals() else self.mongo.logger
        self.df_pred = pd.DataFrame()
        self.df_px = pd.DataFrame()
        self._on_df = pd.DataFrame()
        self._rss_df = pd.DataFrame()
        self._of_df = pd.DataFrame()
        self._iv_df = pd.DataFrame()
        self._on_times = np.array([])
        self._rss_times = np.array([])
        self._of_times = np.array([])
        self._iv_times = np.array([])
        
        # Validation de l'UID
        if not re.match(r"^[A-Z]{2,6}(EUR|USD|USDT)$", uid):
            self.logger.send_log("invalid_uid_format", "warning", 
                                extra_labels={"uid": uid})

    # ────────────────────── chargements source ────────────────────────────
    def load_candles(self):
        """Charge les données de prix."""
        try:
            cur = self.mongo.db["candlesticks"].find(
                {"uid": self.uid},
                {"_id": 0, "closeTime": 1, "lastPrice": 1}
            )
            df = pd.DataFrame(list(cur))
            if df.empty:
                raise RuntimeError(f"Aucun prix trouvé pour {self.uid}")
                
            df.closeTime = pd.to_datetime(df.closeTime, utc=True)
            self.df_px = df.rename(columns={"lastPrice": "price"}).set_index("closeTime").sort_index()
            
            # Supprimer ce log de debug qui n'est pas essentiel
            # self.logger.send_log("candles_loaded", "debug", 
            #                     extra_labels={"uid": self.uid, "count": len(df)})
        except Exception as e:
            # Conserver les logs d'erreur
            self.logger.send_log("candles_load_error", "error", 
                                extra_labels={"uid": self.uid, "error": str(e)})
            raise

    def load_predictions(self):
        cur = self.mongo.db["predictions"].find(
            {"uid": self.uid},
            {"_id": 0, "horizon": 1, "future_price_estimated": 1,
             "current_price": 1, "predicted_for_time": 1},
        )
        df = pd.DataFrame(list(cur))
        if df.empty:
            raise RuntimeError("Aucune prédiction chargée")

        df = df[df.future_price_estimated.notna()]
        df.predicted_for_time = pd.to_datetime(df.predicted_for_time, utc=True)
        df["horizon_hours"]   = df.horizon.str.rstrip("h").astype(int)
        df["price_now"]       = df.current_price
        df["price_pred"]      = df.future_price_estimated
        df["forecast_error"]  = df.price_pred - df.price_now
        self.df_pred = df.sort_values(["predicted_for_time", "horizon_hours"])

    # ------------------- loaders RSS / on-chain / order-flow / IV ----------
    def load_news_status(self):
        if self.df_pred.empty: return
        since = self.df_pred.predicted_for_time.min() - timedelta(minutes=30)
        until = self.df_pred.predicted_for_time.max() + timedelta(minutes=5)
        cur = self.mongo.db["news_status"].find(
            {"crypto": self.uid, "window_start": {"$gte": since, "$lte": until}},
            {"_id": 0, "window_start": 1, **{k: 1 for k in self.RSS_KEEP}},
        )
        df = pd.DataFrame(list(cur))
        if df.empty: return
        df.window_start = pd.to_datetime(df.window_start, utc=True)
        self._rss_df    = df.set_index("window_start").sort_index()
        self._rss_times = self._rss_df.index.values

    def load_onchain(self):
        if self.df_pred.empty: return
        asset = re.match(r"([A-Z]{2,6})", self.uid).group(1)
        since = self.df_pred.predicted_for_time.min() - timedelta(minutes=30)
        until = self.df_pred.predicted_for_time.max() + timedelta(minutes=5)
        cur = self.mongo.db["exchange_flows"].find(
            {"symbol": asset, "openTime": {"$gte": since, "$lte": until}},
            {"_id": 0, "openTime": 1, **{k: 1 for k in self.ONCHAIN_KEEP}},
        )
        df = pd.DataFrame(list(cur))
        if df.empty: return
        df.openTime   = pd.to_datetime(df.openTime, utc=True)
        self._on_df   = df.set_index("openTime").sort_index()
        self._on_times = self._on_df.index.values

    def load_orderflow(self):
        since = self.run_at - timedelta(hours=2)
        until = self.run_at + timedelta(minutes=5)
        cur = self.mongo.db["orderbook_snap"].find(
            {"uid": self.uid, "ts": {"$gte": since, "$lte": until}},
            {"_id": 0, "ts": 1, **{k: 1 for k in self.ORDERFLOW_KEEP}},
        )
        df = pd.DataFrame(list(cur))
        if df.empty: return
        df.ts       = pd.to_datetime(df.ts, utc=True)
        self._of_df = df.set_index("ts").sort_index()
        self._of_times = self._of_df.index.values

    def load_iv(self):
        since = self.run_at - timedelta(hours=48)
        cur = self.mongo.db["options_iv"].find(
            {"uid": self.uid, "ts": {"$gte": since}},
            {"_id": 0, "ts": 1, **{k: 1 for k in self.VOLIV_KEEP}},
        )
        df = pd.DataFrame(list(cur))
        if df.empty: return
        df.ts       = pd.to_datetime(df.ts, utc=True)
        self._iv_df = df.set_index("ts").sort_index()
        self._iv_times = self._iv_df.index.values

    def load_rss(self):
        """Alias pour load_news_status."""
        return self.load_news_status()  # Utilise la méthode existante

    def load_volatility(self):
        """Charge les données de volatilité."""
        if self.df_pred.empty: return
        since = self.df_pred.predicted_for_time.min() - timedelta(minutes=30)
        until = self.df_pred.predicted_for_time.max() + timedelta(minutes=5)
        cur = self.mongo.db["volatility_data"].find(
            {"uid": self.uid, "ts": {"$gte": since, "$lte": until}},
            {"_id": 0, "ts": 1, **{k: 1 for k in self.VOLIV_KEEP}},
        )
        df = pd.DataFrame(list(cur))
        if df.empty: return
        df.ts = pd.to_datetime(df.ts, utc=True)
        self._iv_df = df.set_index("ts").sort_index()
        self._iv_times = self._iv_df.index.values

    # --------------------------------------------------------------------
    def _latest_row(self, times, df, ts, cols):
        if times is None or df is None:
            return {k: None for k in cols}
        pos = times.searchsorted(ts.to_datetime64(), side="right") - 1
        if pos < 0:
            return {k: None for k in cols}
        return df.iloc[pos].to_dict()

    def _rss_for_ts(self, ts):       return self._latest_row(self._rss_times, self._rss_df, ts, self.RSS_KEEP)
    def _onchain_for_ts(self, ts):   return self._latest_row(self._on_times, self._on_df, ts, self.ONCHAIN_KEEP)
    def _orderflow_for_ts(self, ts): return self._latest_row(self._of_times, self._of_df, ts, self.ORDERFLOW_KEEP)
    def _iv_for_ts(self, ts):        return self._latest_row(self._iv_times, self._iv_df, ts, self.VOLIV_KEEP)

    # --------------------------------------------------------------------
    def _attach_real_price(self):
        """Attache les prix réels aux prédictions."""
        try:
            if self.df_px.empty:
                raise RuntimeError("Prix non chargés, appelez load_candles() d'abord")
            
            fut = (
                self.df_px.reset_index()
                .rename(columns={"closeTime": "predicted_for_time", "price": "price_real"})
            )
        
            # Vérification des données avant merge
            if self.df_pred.empty:
                raise RuntimeError("Aucune prédiction chargée")
            
            # Conversion des types de dates pour assurer la compatibilité
            self.df_pred["predicted_for_time"] = pd.to_datetime(self.df_pred["predicted_for_time"], utc=True)
            fut["predicted_for_time"] = pd.to_datetime(fut["predicted_for_time"], utc=True)
        
            # Vérification des doublons dans les données de prix
            if fut["predicted_for_time"].duplicated().any():
                # Log uniquement en cas de problème
                self.logger.send_log("duplicate_price_times", "warning", 
                                    extra_labels={"uid": self.uid})
                fut = fut.drop_duplicates("predicted_for_time", keep="last")
        
            # Merge avec tolérance et vérification du résultat
            merged = pd.merge_asof(
                self.df_pred.sort_values("predicted_for_time"), 
                fut.sort_values("predicted_for_time"),
                on="predicted_for_time",
                direction="nearest",
                tolerance=pd.Timedelta("30m")
            )
        
            if merged.empty:
                raise RuntimeError("Merge a produit un DataFrame vide")
            
            # Vérification des valeurs manquantes après merge
            missing_count = merged["price_real"].isna().sum()
            if missing_count > 0:
                # Log uniquement en cas de problème
                self.logger.send_log("missing_real_prices", "warning", 
                                    extra_labels={"uid": self.uid, "count": missing_count})
                            
            self.df_pred = merged.dropna(subset=["price_real"])
        
            if self.df_pred.empty:
                raise RuntimeError("Aucune paire prédiction / prix réel")
            
            # Supprimer ce log de debug qui n'est pas essentiel
            # self.logger.send_log("real_prices_attached", "debug", 
            #                     extra_labels={"uid": self.uid, 
            #                                 "count": len(self.df_pred),
            #                                 "horizons": self.df_pred["horizon_hours"].unique().tolist()})
            return True
        except Exception as e:
            # Conserver les logs d'erreur
            self.logger.send_log("attach_price_error", "error", 
                                extra_labels={"uid": self.uid, "error": str(e)})
            return False

    # ------------------- métriques forecast ------------------------------
    def _compute_metrics(self):
        """Calcule les métriques de performance des prédictions avec une meilleure gestion des outliers."""
        rows = []
        try:
            for h, grp in self.df_pred.groupby("horizon_hours"):
                # Filtrage des données valides
                grp = grp[grp.price_real != 0].copy()
                if len(grp) < 5:  # Minimum de points pour des statistiques fiables
                    self.logger.send_log("insufficient_data", "warning", 
                                        extra_labels={"uid": self.uid, "horizon": h, "count": len(grp)})
                    continue
                    
                # Détection et suppression des outliers extrêmes
                # Utiliser l'IQR (écart interquartile) pour détecter les outliers
                Q1 = grp.price_real.quantile(0.25)
                Q3 = grp.price_real.quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 3 * IQR
                upper_bound = Q3 + 3 * IQR
                
                # Filtrer les outliers extrêmes
                filtered_grp = grp[(grp.price_real >= lower_bound) & (grp.price_real <= upper_bound)]
                
                # Si trop de données sont filtrées, utiliser les données originales
                if len(filtered_grp) < len(grp) * 0.8:
                    self.logger.send_log("too_many_outliers", "warning", 
                                        extra_labels={"uid": self.uid, "horizon": h, 
                                                    "original": len(grp), "filtered": len(filtered_grp)})
                else:
                    grp = filtered_grp
                    
                # Calcul des métriques avec protection contre les erreurs
                record = {"horizon_h": int(h)}
                
                try:
                    # Utilisation de métriques robustes aux outliers
                    # Median Absolute Percentage Error au lieu de MAPE
                    abs_pct_errors = np.abs((grp.price_real - grp.price_pred) / grp.price_real)
                    record["MdAPE"] = np.median(abs_pct_errors)  # Median APE
                    
                    # Limiter le MAPE à 1.0 (100%) pour éviter les valeurs aberrantes
                    mape = mean_absolute_percentage_error(grp.price_real, grp.price_pred)
                    record["MAPE"] = min(mape, 1.0)  # Plafonner à 100%
                except Exception as e:
                    self.logger.send_log("mape_error", "warning", 
                                        extra_labels={"uid": self.uid, "horizon": h, "error": str(e)})
                    record["MdAPE"] = None
                    record["MAPE"] = None
                    
                try:
                    record["MAE"] = mean_absolute_error(grp.price_real, grp.price_pred)
                except Exception as e:
                    record["MAE"] = None
                    
                try:
                    record["RMSE"] = np.sqrt(mean_squared_error(grp.price_real, grp.price_pred))
                except Exception as e:
                    record["RMSE"] = None
                    
                try:
                    # Calcul de la précision directionnelle avec winsorisation
                    # pour réduire l'impact des petites variations de prix
                    price_change = grp.price_real - grp.price_now
                    pred_change = grp.price_pred - grp.price_now
                    
                    # Ignorer les très petites variations (bruit)
                    min_change_threshold = grp.price_now.mean() * 0.0005  # 0.05% du prix moyen
                    valid_changes = np.abs(price_change) > min_change_threshold
                    
                    if valid_changes.sum() > 5:
                        record["DirAcc"] = (np.sign(pred_change[valid_changes]) == 
                                          np.sign(price_change[valid_changes])).mean()
                    else:
                        record["DirAcc"] = (np.sign(pred_change) == np.sign(price_change)).mean()
                except Exception as e:
                    record["DirAcc"] = None
                    
                try:
                    # Utilisation de la corrélation de rang (plus robuste aux outliers)
                    record["IC"] = spearmanr(
                                np.log(grp.price_pred / grp.price_now),
                                np.log(grp.price_real / grp.price_now)
                            )[0]
                except Exception as e:
                    record["IC"] = None
                    
                record["DM_stat"] = None
                record["p_value"] = None
                
                # Test DM si suffisamment de données
                if len(grp) > h:
                    try:
                        # Utilisation de MAD (plus robuste aux outliers) pour le test DM
                        dm, pv = dm_test(
                            np.abs(grp.price_pred - grp.price_real),
                            np.abs(grp.price_now  - grp.price_real), h, crit="MAD")
                        record.update(DM_stat=dm, p_value=pv)
                    except Exception as e:
                        self.logger.send_log("dm_test_error", "warning", 
                                            extra_labels={"uid": self.uid, "horizon": h, "error": str(e)})
                
                rows.append(record)
                
            # Création du DataFrame final
            result = pd.DataFrame(rows).set_index("horizon_h").round(4) if rows else pd.DataFrame()
            
            # Log des résultats
            if not result.empty:
                for h, row in result.iterrows():
                    self.logger.send_log("metrics_computed", "debug", 
                                        extra_labels={"uid": self.uid, "horizon": h, 
                                                    "DirAcc": row.get("DirAcc"), 
                                                    "MAPE": row.get("MAPE"),
                                                    "MdAPE": row.get("MdAPE")})
            return result
            
        except Exception as e:
            self.logger.send_log("compute_metrics_error", "error", 
                                extra_labels={"uid": self.uid, "error": str(e)})
            return pd.DataFrame()

    # --------------------------------------------------------------------
    def _store_features(self, audit_df: pd.DataFrame):
        """Stocke les features en utilisant les méthodes de MongoUtils."""
        print(f"[{datetime.now(timezone.utc).isoformat()}] Début du stockage des features pour {self.uid}")
        self.logger.send_log("store_features_started", "debug", extra_labels={"uid": self.uid})
        
        try:
            metrics = audit_df.to_dict("index")
            feature_count = 0
            
            print(f"[{datetime.now(timezone.utc).isoformat()}] Conversion des métriques en dictionnaire pour {self.uid}")
            self.logger.send_log("metrics_converted", "debug", extra_labels={"uid": self.uid})
            
            # Vérifier la taille du DataFrame de prédictions
            pred_count = len(self.df_pred)
            print(f"[{datetime.now(timezone.utc).isoformat()}] Nombre de prédictions à traiter pour {self.uid}: {pred_count}")
            self.logger.send_log("prediction_count", "debug", extra_labels={"uid": self.uid, "count": pred_count})
            
            # Limiter le nombre de prédictions si trop grand
            if pred_count > 100:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Trop de prédictions pour {self.uid}, limitation à 100")
                self.logger.send_log("limiting_predictions", "warning", extra_labels={"uid": self.uid, "original_count": pred_count})
                self.df_pred = self.df_pred.iloc[:100]
                pred_count = 100
            
            # Préparer un lot de documents à insérer
            batch_docs = []
            
            for i, (_, row) in enumerate(self.df_pred.iterrows()):
                # Log pour chaque 10 prédictions traitées
                if i % 10 == 0:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Traitement de la prédiction {i+1}/{pred_count} pour {self.uid}")
                    self.logger.send_log("processing_prediction", "debug", extra_labels={"uid": self.uid, "index": i+1, "total": pred_count})
                
                try:
                    ts = row.predicted_for_time
                    h  = int(row.horizon_hours)
                    
                    # Vérifier si les métriques existent pour cet horizon
                    if h not in metrics:
                        print(f"[{datetime.now(timezone.utc).isoformat()}] Pas de métriques pour l'horizon {h} pour {self.uid}")
                        self.logger.send_log("no_metrics_for_horizon", "warning", extra_labels={"uid": self.uid, "horizon": h})
                        continue
                    
                    base = {
                        "uid": self.uid, "horizon_h": h, "run_at": ts,
                        "expected_ret": (row.price_pred / row.price_now - 1)
                                         if row.price_now else None,
                        **metrics.get(h, {}),
                    }
                    
                    # Récupérer les données supplémentaires
                    rss_data = self._rss_for_ts(ts)
                    onchain_data = self._onchain_for_ts(ts)
                    orderflow_data = self._orderflow_for_ts(ts)
                    iv_data = self._iv_for_ts(ts)
                    
                    doc = {
                        **base,
                        **rss_data, **onchain_data,
                        **orderflow_data, **iv_data,
                        "created_at": datetime.now(timezone.utc),
                    }
                    
                    # NaN -> None
                    doc = {k: (None if isinstance(v, float) and np.isnan(v) else v)
                           for k, v in doc.items()}
                    
                    # Ajouter un identifiant unique pour l'upsert
                    doc["_id"] = f"{self.uid}_{ts.isoformat()}_{h}"
                    
                    # Ajouter au lot
                    batch_docs.append(doc)
                    feature_count += 1
                    
                    # Insérer par lots de 20 documents
                    if len(batch_docs) >= 20:
                        try:
                            # Utiliser insert_many avec ordered=False pour continuer en cas d'erreur
                            result = self.mongo.db["forecast_features"].insert_many(
                                batch_docs, 
                                ordered=False
                            )
                            print(f"[{datetime.now(timezone.utc).isoformat()}] Lot de {len(batch_docs)} features inséré pour {self.uid}")
                            
                            # Supprimer ce log de debug qui n'est pas essentiel
                            # self.logger.send_log("batch_inserted", "debug", extra_labels={
                            #     "uid": self.uid, 
                            #     "count": len(batch_docs)
                            # })
                            batch_docs = []  # Réinitialiser le lot
                        except Exception as e:
                            print(f"[{datetime.now(timezone.utc).isoformat()}] Erreur lors de l'insertion du lot pour {self.uid}: {e}")
                            # Conserver les logs d'erreur
                            self.logger.send_log("batch_insert_error", "warning", extra_labels={
                                "uid": self.uid, 
                                "error": str(e)
                            })
                            # Continuer avec un nouveau lot
                            batch_docs = []
                
                except Exception as e:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Erreur lors du traitement d'une prédiction pour {self.uid}: {e}")
                    self.logger.send_log("prediction_processing_error", "warning", extra_labels={
                        "uid": self.uid, "index": i, "error": str(e)
                    })
            
            # Insérer le dernier lot s'il reste des documents
            if batch_docs:
                try:
                    result = self.mongo.db["forecast_features"].insert_many(
                        batch_docs, 
                        ordered=False
                    )
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Dernier lot de {len(batch_docs)} features inséré pour {self.uid}")
                    self.logger.send_log("final_batch_inserted", "debug", extra_labels={
                        "uid": self.uid, 
                        "count": len(batch_docs)
                    })
                except Exception as e:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Erreur lors de l'insertion du dernier lot pour {self.uid}: {e}")
                    self.logger.send_log("final_batch_insert_error", "warning", extra_labels={
                        "uid": self.uid, 
                        "error": str(e)
                    })
            
            print(f"[{datetime.now(timezone.utc).isoformat()}] Fin du stockage des features pour {self.uid}: {feature_count} features stockées")
            self.logger.send_log("store_features_completed", "debug", extra_labels={
                "uid": self.uid, "feature_count": feature_count
            })
            
            return True
        
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Erreur générale lors du stockage des features pour {self.uid}: {e}")
            self.logger.send_log("store_features_error", "error", extra_labels={
                "uid": self.uid, "error": str(e), "traceback": traceback.format_exc()
            })
            return False

    # --------------------------------------------------------------------
    def run(self):
        """Exécute l'audit complet avec gestion d'erreurs améliorée."""
        try:
            # Vérifier si l'UID existe
            candle_count = self.mongo.db["candlesticks"].count_documents({"uid": self.uid})
            if candle_count == 0:
                self.logger.send_log("no_candles_for_audit", "warning", extra_labels={"uid": self.uid})
                return False
            
            # Charger les données
            print(f"[{datetime.now(timezone.utc).isoformat()}] Chargement des données pour {self.uid}")
            self.logger.send_log("loading_data", "debug", extra_labels={"uid": self.uid})
            
            self.load_candles()
            if self.df_px.empty:
                self.logger.send_log("empty_price_data", "warning", extra_labels={"uid": self.uid})
                return False
            
            self.load_predictions()
            if self.df_pred.empty:
                self.logger.send_log("empty_prediction_data", "warning", extra_labels={"uid": self.uid})
                return False
            
            # Attacher les prix réels
            print(f"[{datetime.now(timezone.utc).isoformat()}] Attachement des prix réels pour {self.uid}")
            self.logger.send_log("attaching_real_prices", "debug", extra_labels={"uid": self.uid})
            
            if not self._attach_real_price():
                self.logger.send_log("attach_price_failed", "error", extra_labels={"uid": self.uid})
                return False
            
            # Charger les données supplémentaires si disponibles
            print(f"[{datetime.now(timezone.utc).isoformat()}] Chargement des données supplémentaires pour {self.uid}")
            # Supprimer ce log de debug qui n'est pas essentiel
            # self.logger.send_log("loading_additional_data", "debug", extra_labels={"uid": self.uid})

            try:
                self.load_onchain()
            except Exception as e:
                # Conserver les logs d'erreur
                self.logger.send_log("onchain_load_error", "warning", extra_labels={"uid": self.uid, "error": str(e)})

            try:
                self.load_news_status()
            except Exception as e:
                # Conserver les logs d'erreur
                self.logger.send_log("news_load_error", "warning", extra_labels={"uid": self.uid, "error": str(e)})

            try:
                self.load_orderflow()
            except Exception as e:
                # Conserver les logs d'erreur
                self.logger.send_log("orderflow_load_error", "warning", extra_labels={"uid": self.uid, "error": str(e)})

            try:
                self.load_volatility()
            except Exception as e:
                # Conserver les logs d'erreur
                self.logger.send_log("volatility_load_error", "warning", extra_labels={"uid": self.uid, "error": str(e)})

            # Calculer les métriques
            print(f"[{datetime.now(timezone.utc).isoformat()}] Calcul des métriques pour {self.uid}")
            self.logger.send_log("computing_metrics", "debug", extra_labels={"uid": self.uid})
            
            metrics_by_horizon = self._compute_metrics()
            if metrics_by_horizon.empty:
                self.logger.send_log("empty_metrics", "warning", extra_labels={"uid": self.uid})
                return False
            
            # Sauvegarder les résultats
            print(f"[{datetime.now(timezone.utc).isoformat()}] Sauvegarde des résultats pour {self.uid}")
            self.logger.send_log("saving_results", "debug", extra_labels={"uid": self.uid})
            
            audit_doc = {
                "uid": self.uid,
                "run_at": datetime.now(timezone.utc),  # Ajouter une valeur non-null ici
                "created_at": datetime.now(timezone.utc),
                "rows": [{"horizon_h": h, **row.to_dict()} for h, row in metrics_by_horizon.iterrows()]
            }
            
            try:
                # Vérifier si un document avec le même uid existe déjà
                existing = self.mongo.db["forecast_audit"].find_one({"uid": self.uid, "run_at": None})
                if existing:
                    # Mettre à jour le document existant
                    self.mongo.db["forecast_audit"].update_one(
                        {"_id": existing["_id"]},
                        {"$set": audit_doc}
                    )
                else:
                    # Insérer un nouveau document
                    self.mongo.db["forecast_audit"].insert_one(audit_doc)
                
                print(f"[{datetime.now(timezone.utc).isoformat()}] Stockage des features pour {self.uid}")
                self.logger.send_log("storing_features", "debug", extra_labels={"uid": self.uid})
                self._store_features(metrics_by_horizon)
                
                print(f"[{datetime.now(timezone.utc).isoformat()}] Audit terminé pour {self.uid}")
                self.logger.send_log("audit_complete", "info", extra_labels={
                    "uid": self.uid,
                    "horizons": len(metrics_by_horizon)
                })
                return True
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Erreur lors de la sauvegarde pour {self.uid}: {e}")
                self.logger.send_log("save_audit_error", "error", extra_labels={
                    "uid": self.uid,
                    "error": str(e)
                })
                return False
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Erreur générale pour {self.uid}: {e}")
            self.logger.send_log("general_audit_error", "error", extra_labels={
                "uid": self.uid,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            return False

    # Ajouter la propriété run_at
    @property
    def run_at(self):
        """Retourne l'heure d'exécution actuelle."""
        return datetime.now(timezone.utc)

    # Renommer compute_metrics pour correspondre à l'appel
    def compute_metrics(self):
        """Alias pour _compute_metrics."""
        return self._compute_metrics()

    # Ajouter la méthode save_metrics
    def save_metrics(self, metrics_df):
        """Sauvegarde les métriques calculées."""
        if metrics_df.empty:
            self.logger.send_log("empty_metrics", "warning", extra_labels={"uid": self.uid})
            return False
        
        # Convertir en dictionnaire pour MongoDB
        metrics = metrics_df.to_dict("index")
        
        # Créer le document d'audit
        audit_doc = {
            "uid": self.uid,
            "created_at": datetime.now(timezone.utc),
            "rows": [{"horizon_h": h, **row} for h, row in metrics.items()]
        }
        
        try:
            # Sauvegarder dans MongoDB
            self.mongo.db["forecast_audit"].insert_one(audit_doc)
            
            # Stocker également les features
            self._store_features(metrics_df)
            
            self.logger.send_log("metrics_saved", "info", extra_labels={
                "uid": self.uid,
                "horizons": len(metrics)
            })
            return True
        except Exception as e:
            self.logger.send_log("save_metrics_error", "error", extra_labels={
                "uid": self.uid,
                "error": str(e)
            })
            return False

# ═════════════════════════════ loop daemon ════════════════════════════════
def _next_wait(now: datetime) -> float:
    """Secondes jusqu'au prochain multiple de 5 minutes."""
    wait = (5 - (now.minute % 5)) * 60 - now.second - now.microsecond / 1e6
    wait = wait if wait > 0 else 300 + wait
    
    # Ajouter des logs plus détaillés
    print(f"[{now.isoformat()}] Attente de {wait:.2f} secondes jusqu'au prochain cycle")
    logger.send_log("next_wait_calculated", "info", extra_labels={
        "current_time": now.isoformat(),
        "wait_seconds": round(wait, 2),
        "next_run": (now + timedelta(seconds=wait)).isoformat()
    })
    return wait

def main_loop():
    """Boucle principale d'exécution."""
    mongo = MongoUtils(); mongo.connect(service="audit_forecasts")
    cycle_count = 0
    
    # Log de démarrage explicite
    print(f"[{datetime.now(timezone.utc).isoformat()}] DÉMARRAGE DE MAIN_LOOP()")
    safe_log("main_loop_started", "info", force=True)
    
    while True:  # Cette boucle devrait être infinie
        try:
            cycle_count += 1
            t0 = time.time()
            cycle_start = datetime.now(timezone.utc)
            
            print(f"[{cycle_start.isoformat()}] DÉBUT DU CYCLE {cycle_count}")
            safe_log("audit_cycle_started", "info", {
                "cycle": cycle_count,
                "time": cycle_start.isoformat()
            }, force=True)
            
            # Récupérer tous les actifs disponibles
            assets = set()
            
            # 1. Depuis forecast_features
            assets.update(mongo.db["forecast_features"].distinct("uid"))
            
            # 2. Depuis les modèles existants
            model_dir = pathlib.Path("/home/<USER>/cryptobot/models")
            if model_dir.exists():
                for model_path in model_dir.glob("sac_*.zip"):
                    parts = model_path.stem.split("_")
                    if len(parts) >= 2:
                        assets.add(parts[1])
            
            # 3. Fallback sur la liste par défaut
            if not assets:
                assets = set(CRYPTO_TEST)
            
            # Filtrer les UIDs invalides
            valid_assets = [asset for asset in assets if re.match(r"^[A-Z]{2,6}(EUR|USD|USDT)$", asset)]
            
            print(f"[{datetime.now(timezone.utc).isoformat()}] Actifs à auditer: {len(valid_assets)}")
            logger.send_log("assets_to_audit", "info", extra_labels={
                "count": len(valid_assets),
                "assets": valid_assets[:15]  # Limiter à 15 pour éviter des logs trop grands
            })
            
            assets_processed = 0
            assets_success = 0
            assets_failed = 0
            
            for uid in valid_assets:
                try:
                    # Réduire la verbosité - log uniquement dans la console
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Audit de {uid}")
                    
                    # Ne pas envoyer de log à Grafana pour chaque actif
                    # logger.send_log("auditing_asset", "debug", extra_labels={"uid": uid})
                    
                    success = AuditForecasts(uid, mongo).run()
                    assets_processed += 1
                    
                    if success:
                        assets_success += 1
                        print(f"[{datetime.now(timezone.utc).isoformat()}] Audit réussi pour {uid}")
                        # Garder uniquement les logs d'erreur dans Grafana
                        # logger.send_log("asset_audit_success", "debug", extra_labels={"uid": uid})
                    else:
                        assets_failed += 1
                        print(f"[{datetime.now(timezone.utc).isoformat()}] Audit échoué pour {uid}")
                        # Conserver les logs d'erreur
                        logger.send_log("asset_audit_failed", "warning", extra_labels={"uid": uid})
                except RuntimeError as e:
                    assets_processed += 1
                    assets_failed += 1
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Erreur pour {uid}: {e}")
                    # Conserver les logs d'erreur
                    logger.send_log("asset_audit_error", "warning", extra_labels={"uid": uid, "error": str(e)})
                except Exception as e:
                    assets_processed += 1
                    assets_failed += 1
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Exception pour {uid}: {e}")
                    # Conserver les logs d'erreur
                    logger.send_log("asset_audit_exception", "error", extra_labels={
                        "uid": uid, "error": str(e)
                        # Réduire la taille du traceback
                        # "traceback": traceback.format_exc()
                    })
            
            cycle_end = datetime.now(timezone.utc)
            elapsed = time.time() - t0
            
            print(f"[{cycle_end.isoformat()}] FIN DU CYCLE {cycle_count} (durée: {elapsed:.2f}s)")
            print(f"[{cycle_end.isoformat()}] Actifs traités: {assets_processed}, Réussis: {assets_success}, Échoués: {assets_failed}")
            safe_log("audit_cycle_completed", "info", {
                "cycle": cycle_count,
                "duration_seconds": round(elapsed, 2),
                "assets_processed": assets_processed,
                "assets_success": assets_success,
                "assets_failed": assets_failed
            }, force=True)
            
            # Calculer le temps d'attente
            wait_time = _next_wait(datetime.now(timezone.utc))

            # Soustraire le temps d'exécution
            elapsed = time.time() - t0
            wait_time = max(1, wait_time - elapsed)

            next_run = datetime.now(timezone.utc) + timedelta(seconds=wait_time)
            print(f"[{datetime.now(timezone.utc).isoformat()}] ATTENTE JUSQU'À {next_run.isoformat()} ({wait_time:.2f}s)")

            # Réduire la verbosité des logs Grafana
            # logger.send_log("waiting_for_next_cycle", "info", extra_labels={
            #     "cycle": cycle_count,
            #     "wait_seconds": round(wait_time, 2),
            #     "next_run": next_run.isoformat()
            # })

            # Ajouter un log juste avant le sleep
            print(f"[{datetime.now(timezone.utc).isoformat()}] DÉBUT DU SLEEP ({wait_time:.2f}s)")
            safe_log("sleep_started", "info", {
                "cycle": cycle_count,
                "wait_seconds": round(wait_time, 2)
            }, force=True)
            
            time.sleep(wait_time)  # Cette ligne est cruciale
            
            print(f"[{datetime.now(timezone.utc).isoformat()}] FIN DU SLEEP")
            safe_log("sleep_ended", "info", {
                "cycle": cycle_count
            }, force=True)
        except Exception as e:
            # Capturer toutes les exceptions pour éviter que la boucle ne se termine
            print(f"[{datetime.now(timezone.utc).isoformat()}] ERREUR DANS LE CYCLE: {e}")
            traceback.print_exc()
            safe_log("cycle_error", "error", {
                "cycle": cycle_count,
                "error": str(e)
            })
            # Attendre avant de réessayer
            print(f"[{datetime.now(timezone.utc).isoformat()}] ATTENTE DE 60 SECONDES AVANT DE RÉESSAYER")
            safe_log("retry_wait", "info", {
                "cycle": cycle_count,
                "wait_seconds": 60
            }, force=True)
            time.sleep(60)  # Attendre 1 minute avant de réessayer
            print(f"[{datetime.now(timezone.utc).isoformat()}] FIN DE L'ATTENTE, REPRISE DU CYCLE")
            safe_log("retry_wait_ended", "info", {
                "cycle": cycle_count
            }, force=True)

# Point d'entrée principal
if __name__ == "__main__":
    try:
        # Vérifier les arguments
        if len(sys.argv) > 1:
            uid = sys.argv[1]
            print(f"[{datetime.now(timezone.utc).isoformat()}] EXÉCUTION POUR {uid}")
            mongo = MongoUtils(); mongo.connect(service="audit_forecasts")
            AuditForecasts(uid, mongo).run()
            print(f"[{datetime.now(timezone.utc).isoformat()}] EXÉCUTION TERMINÉE POUR {uid}")
        else:
            # Mode daemon (boucle infinie)
            print(f"[{datetime.now(timezone.utc).isoformat()}] EXÉCUTION DE MAIN_LOOP()")
            main_loop()
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] ERREUR FATALE: {e}")
        traceback.print_exc()
        sys.exit(1)
