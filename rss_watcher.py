# Ajout au script RSS : agrégation intégrée dans le watcher avec fallback et vérification de doublons + ajouts pour apprentissage ML
import os
os.environ.update(
    OMP_NUM_THREADS="1",
    MKL_NUM_THREADS="1",
    OPENBLAS_NUM_THREADS="1",
    NUMEXPR_NUM_THREADS="1",
    VECLIB_MAXIMUM_THREADS="1",
    GOMP_CPU_AFFINITY="0"
)
import sys
import re
import json
import time
import numpy as np
import requests
import spacy
import torch
import gc
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForSequenceClassification

torch.set_num_threads(1)
torch.set_num_interop_threads(1)

FINBERT_NAME = "yiyanghkust/finbert-tone"
ML_MODEL_NAME = "cardiffnlp/twitter-xlm-roberta-base-sentiment"
GEO_MODEL_NAME = "joeddav/xlm-roberta-large-xnli"

SERVICE = "news_taker"
logger = GrafanaUtils(service="news_taker_log")  # pour les logs techniques
simple_logger = GrafanaUtils(service="raw_data")  # pour les logs RSS uniquement
try:
    nlp = spacy.load("xx_ent_wiki_sm")
except Exception as e:
    logger.send_log(f"❌ Erreur Spacy : {e}", "error")
    sys.exit(1)

try:
    FINBERT_TOKENIZER = AutoTokenizer.from_pretrained(FINBERT_NAME)
    FINBERT_MODEL = AutoModelForSequenceClassification.from_pretrained(FINBERT_NAME, output_hidden_states=True)
    FINBERT_MODEL.eval()
except Exception as e:
    logger.send_log(f"❌ Erreur chargement FINBERT : {e}", "error")
    sys.exit(1)

try:
    XLMR_TOKENIZER = AutoTokenizer.from_pretrained(ML_MODEL_NAME)
    XLMR_MODEL = AutoModelForSequenceClassification.from_pretrained(ML_MODEL_NAME, output_hidden_states=True)
    XLMR_MODEL.eval()
except Exception as e:
    logger.send_log(f"❌ Erreur chargement XLM-R : {e}", "error")
    sys.exit(1)

try:
    GEO_TOKENIZER = XLMRobertaTokenizer.from_pretrained(GEO_MODEL_NAME)
    GEO_MODEL = XLMRobertaForSequenceClassification.from_pretrained(GEO_MODEL_NAME)
    GEO_MODEL.eval()
except Exception as e:
    logger.send_log(f"❌ Erreur chargement GEO_MODEL : {e}", "error")
    sys.exit(1)

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
    'Accept': 'application/rss+xml, application/xml;q=0.9, */*;q=0.8'
}
COINGECKO_CACHE_FILE = os.path.join(os.path.dirname(__file__), "coingecko_cryptos.json")
logger = GrafanaUtils(service="news_taker_log")  # pour les logs techniques
simple_logger = GrafanaUtils(service="raw_data")  # pour les logs RSS uniquement
print("📍 Script RSS_WATCHER lancé")
logger.send_log("📍 Script RSS_WATCHER lancé", "info")
logger.send_log(f"📦 Modèle FINBERT : {FINBERT_NAME}", "debug")
logger.send_log(f"📦 Modèle XLM-R : {ML_MODEL_NAME}", "debug")
logger.send_log(f"📦 Modèle GEO_MODEL : {GEO_MODEL_NAME}", "debug")

def impact_emoji(level):
    return {"low": "🟢", "moderate": "🟡", "high": "🔴"}.get(level, "❓")

def sentiment_emoji(sentiment):
    return {"positive": "😊", "neutral": "😐", "negative": "😠"}.get(sentiment, "❓")  # pour les logs techniques
mongo = MongoUtils(logger=logger)
mongo.connect(service=SERVICE)


geo_labels = [
    # Politique monétaire & inflation
    "interest rate hike", "interest rate cut", "monetary tightening", "monetary easing",
    "quantitative easing", "quantitative tightening", "liquidity injection", "inflation control",
    "high inflation", "hyperinflation", "deflation", "stagflation",
    
    # Banque centrale & devises
    "central bank policy", "federal reserve", "ECB announcement", "currency devaluation",
    "dollar strength", "fiat instability",

    # Politique fiscale & économie (nouvelles propositions)
    "fiscal stimulus",        # e.g. plan de relance budgétaire
    "economic recovery plan", # e.g. suite à un choc (pandémie, crise)
    "tax reform",             # ex. réduction/augmentation massive d’impôts
    "debt ceiling",           # blocage ou relèvement du plafond de la dette
    "government shutdown",    # ex. aux USA, paralysie du gouvernement
    "job market",             # usage générique, si un article parle beaucoup de chômage/emploi
    "unemployment",           # focus sur taux de chômage
    "gdp growth",             # croissance du PIB

    # Crypto & blockchain (nouveaux labels)
    "crypto regulation", "crypto ban", "crypto tax", "CBDC launch", "stablecoin regulation",
    "crypto ETF approval", "crypto trading ban", "crypto lawsuit", "exchange shutdown",
    "stablecoin crackdown", # renforce l’aspect répression vs stablecoins
    "ICO regulation",
    "NFT mania",
    "NFT ban",
    "metaverse hype",
    "L2 adoption",           # adoption layer2 (Arbitrum, Optimism, etc.)
    
    # Cyberrisques & blockchain
    "blockchain innovation", "smart contract exploit", "defi protocol failure",
    "crypto exchange hack", "DeFi aggregator hack",

    # Risques financiers & boursiers
    "stock market crash", "credit crunch", "bank insolvency", "sovereign default", "financial contagion",

    # Risques géopolitiques
    "trade war", "military conflict", "economic sanctions", "embargo", "geopolitical instability",
    "political election",   # e.g. présidentielles majeures
    "referendum",           # Brexit-like
    "coup d etat",          # renversement de gouvernement

    # Thème neutre / fallback
    "neutral"
]

def get_embedding_mean(embeddings):
    if not embeddings:
        return []
    emb_array = np.array(embeddings)
    return emb_array.mean(axis=0).tolist()

def get_full_article_text_with_playwright(url):
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context()
            page = context.new_page()
            page.goto(url, timeout=20000)
            page.wait_for_timeout(5000)
            content = page.locator("article").inner_text()
            browser.close()
            return content
    except Exception as e:
        logger.send_log(f"[Playwright] ❌ Failed: {e}", "error")
        return ""

def resolve_real_article_url_bitrss(bitrss_url):
    try:
        response = requests.get(bitrss_url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.text, "html.parser")
        link_tag = soup.find("div", class_="news-url").find("a")
        if link_tag and link_tag.has_attr("href"):
            return link_tag["href"]
    except Exception as e:
        logger.send_log(f"[BitRSS] ❌ Impossible de résoudre le lien réel : {e}", "warning")
    return bitrss_url  # fallback

def extract_cryptos_spacy(text):
    # 1) Analyse NLP multilingue
    doc = nlp(text)

    # 2) On prépare un set global pour accumuler les cryptos détectées
    cleaned_cryptos = set()

    # 3) Regex pour détecter des tickers style $BTC, $ETH ou tout bloc de 2 à 6 lettres majuscules
    ticker_pattern = re.compile(r'\$?[A-Z]{2,6}\b')

    # 4) Extraction par regex
    regex_tickers = set()
    for match in ticker_pattern.findall(text):
        # Retire le $ éventuel, convertit en uppercase
        clean_ticker = match.replace('$', '').upper()
        # Exemple de filtrage minimal (évite 'AND', 'THE', etc. si tu le souhaites)
        if len(clean_ticker) >= 2:
            regex_tickers.add(clean_ticker)

    # 5) On ajoute ces tickers dans le set 'cleaned_cryptos'
    for rt in regex_tickers:
        cleaned_cryptos.add(rt.lower())

    # 6) Détection d'entités Spacy (ORG, PRODUCT)
    raw_entities = [ent.text for ent in doc.ents if ent.label_ in ["ORG", "PRODUCT"]]

    for raw in raw_entities:
        parts = raw.replace("\n", " ").replace("\t", " ").split()
        for part in parts:
            clean = part.strip().lower()
            if clean and len(clean) > 1:
                cleaned_cryptos.add(clean)

    # 7) On construit la liste upper-case (ex: 'bitcoin' -> 'BITCOIN')
    detected_names = [c.upper() for c in cleaned_cryptos]

    # 8) Accès aux paires de marché depuis Mongo
    symbol_docs = list(mongo.db["market_pairs"].find({}))

    name_to_symbol = {}
    symbol_map = {}

    for doc_item in symbol_docs:
        from_asset = doc_item["from_asset"]  # ex. BTC
        symbol = doc_item["symbol"]         # ex. BTCEUR
        name_to_symbol.setdefault(from_asset, []).append(symbol)
        symbol_map[symbol] = doc_item

    # 9) On vérifie les symboles matched
    matched_symbols = set()
    matched_labels = set()
    for name in detected_names:
        if name in name_to_symbol:
            matched_symbols.update(name_to_symbol[name])
            matched_labels.add(name)

    # 10) Retourne listes, fallback "GENERAL"
    return list(matched_symbols or ["GENERAL"]), list(matched_labels or ["GENERAL"])


def analyze_sentiment(text):
    text = text.strip()[:1500]
    if len(text) < 10:
        return {
            "sentiment": "neutral",
            "geo_topic": "neutral",
            "geo_score": 0.0,
            "impact_class": "low",
            "predicted_trend": "neutral"
        }

    # Détection de la langue
    try:
        lang = detect(text)
    except Exception:
        lang = "unknown"

    # Choix du modèle/tokenizer en fonction de la langue
    if lang == "en":
        tokenizer, model = FINBERT_TOKENIZER, FINBERT_MODEL
    else:
        tokenizer, model = XLMR_TOKENIZER, XLMR_MODEL

    # --- Analyse de sentiment ---
    sentiment = "neutral"
    outputs = None
    try:
        inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=256)
        with torch.no_grad():
            outputs = model(**inputs)
            probs = F.softmax(outputs.logits, dim=1)
            predicted = torch.argmax(probs, dim=1).item()
            # Indices : 0=negative, 1=neutral, 2=positive
            sentiment = ["negative", "neutral", "positive"][predicted]
    except Exception as e:
        logger.send_log(f"[Sentiment] ⚠️ Erreur analyse : {e}", "warning")

    # --- Analyse GeoBERT ---
    geo_topic = "neutral"
    geo_score = 0.0
    geo_topics = []
    try:
        premise = text[:700]
        geo_inputs = GEO_TOKENIZER(
            [premise] * len(geo_labels),
            [f"This text is about {label}" for label in geo_labels],
            return_tensors="pt", truncation=True, padding=True, max_length=256
        )
        with torch.no_grad():
            logits = GEO_MODEL(**geo_inputs).logits
            g_probs = F.softmax(logits, dim=1)[:, 2]  # probabilité de 'entailment'
            top_indices = torch.topk(g_probs, 3).indices.tolist()
            geo_topics = [geo_labels[i] for i in top_indices]
            geo_topic = geo_labels[top_indices[0]]
            geo_score = round(g_probs[top_indices[0]].item(), 4)
    except Exception as e:
        logger.send_log(f"[GeoBERT] ⚠️ Erreur : {e}", "warning")

    # --- Classification macro (selon geo_topic) ---
    macro_categories = {
        # Politique monétaire & inflation
        "interest rate hike": "monetary_policy",
        "interest rate cut": "monetary_policy",
        "monetary tightening": "monetary_policy",
        "monetary easing": "monetary_policy",
        "quantitative easing": "monetary_policy",
        "quantitative tightening": "monetary_policy",
        "liquidity injection": "monetary_policy",
        "inflation control": "monetary_policy",
        "high inflation": "inflation",
        "hyperinflation": "inflation",
        "deflation": "inflation",
        "stagflation": "inflation",

        # Banque centrale & devises
        "central bank policy": "monetary_policy",
        "federal reserve": "monetary_policy",
        "ECB announcement": "monetary_policy",
        "currency devaluation": "fx_risk",
        "dollar strength": "fx_risk",
        "fiat instability": "fx_risk",

        # Politique fiscale & économie
        "fiscal stimulus": "fiscal_policy",
        "economic recovery plan": "fiscal_policy",
        "tax reform": "fiscal_policy",
        "debt ceiling": "debt_risk",
        "government shutdown": "political",
        "job market": "employment",
        "unemployment": "employment",
        "gdp growth": "economy",

        # Crypto & blockchain
        "crypto regulation": "crypto_policy",
        "crypto ban": "crypto_policy",
        "crypto tax": "crypto_policy",
        "CBDC launch": "crypto_policy",
        "stablecoin regulation": "crypto_policy",
        "crypto ETF approval": "crypto_policy",
        "crypto trading ban": "crypto_policy",
        "crypto lawsuit": "crypto_policy",
        "exchange shutdown": "crypto_policy",
        "stablecoin crackdown": "crypto_policy",
        "ICO regulation": "crypto_policy",
        "NFT mania": "tech",
        "NFT ban": "crypto_policy",
        "metaverse hype": "tech",
        "L2 adoption": "tech",

        # Cyberrisques & blockchain
        "blockchain innovation": "tech",
        "smart contract exploit": "tech",
        "defi protocol failure": "tech",
        "crypto exchange hack": "tech",
        "DeFi aggregator hack": "tech",

        # Risques financiers & boursiers
        "stock market crash": "market_risk",
        "credit crunch": "market_risk",
        "bank insolvency": "market_risk",
        "sovereign default": "debt_risk",
        "financial contagion": "debt_risk",

        # Risques géopolitiques
        "trade war": "geopolitical",
        "military conflict": "geopolitical",
        "economic sanctions": "geopolitical",
        "embargo": "geopolitical",
        "geopolitical instability": "geopolitical",
        "political election": "political",
        "referendum": "political",
        "coup d etat": "political",

        # Neutre / fallback
        "neutral": "other",
    }

    macro_category = macro_categories.get(geo_topic, "other")

    # --- Impact class ---
    impact_class = "low"
    if geo_score > 0.8:
        impact_class = "high"
    elif geo_score > 0.5:
        impact_class = "moderate"

    # --- Predicted trend ---
    predicted_trend = "neutral"
    if sentiment == "positive" and geo_topic in ["monetary easing", "quantitative easing", "liquidity injection"]:
        predicted_trend = "bullish"
    elif sentiment == "negative" and geo_topic in ["interest rate hike", "monetary tightening", "stock market crash"]:
        predicted_trend = "bearish"

    # --- Embedding CLS ---
    embedding = []
    if outputs is not None:
        try:
            # Vérifie si le modèle renvoie bien les hidden_states
            if hasattr(outputs, "hidden_states") and outputs.hidden_states:
                last_hidden = outputs.hidden_states[-1]  # (batch_size, seq_len, hidden_dim)
                cls_vector = last_hidden[:, 0, :].squeeze().detach().cpu().numpy()
                if cls_vector.ndim == 1:
                    embedding = cls_vector.tolist()
                else:
                    # Si batch_size > 1
                    embedding = cls_vector[0].tolist()
        except Exception as e:
            logger.send_log(f"[Embedding] ⚠️ Erreur embedding CLS : {e}", "warning")

    return {
        "sentiment": sentiment,
        "geo_topic": geo_topic,
        "geo_score": geo_score,
        "impact_class": impact_class,
        "predicted_trend": predicted_trend,
        "embedding": embedding,
        "geo_topics": geo_topics,
        "macro_category": macro_category
    }

def process_article(url, source_name, source_type="rss", platform=""):
    logger.send_log(f"[🔗] Processing article: {url} (source_type={source_type}, platform={platform})", "info")
    if mongo.db["news_data"].find_one({"url": url}):
        logger.send_log(f"⏩ Déjà présent : {url}", "info")
        return
    try:
        article = Article(url)
        try:
            article.download()
            article.parse()
        except Exception as e:
            logger.send_log(f"[newspaper3k] ⚠️ Erreur parsing : {e}", "warning")
            return
        full_text = article.text[:1500] if article.text else ""
        if not full_text or len(full_text) < 500:
            full_text = get_full_article_text_with_playwright(url)
        analysis = analyze_sentiment(full_text)
        cryptos, crypto_labels = extract_cryptos_spacy(full_text)
        mongo.db["news_data"].insert_one({
            "embedding": analysis.get("embedding", []),
            "url": url,
            "source": f"{source_name} ({platform})",
            "title": article.title,
            "authors": article.authors,
            "published_date": str(article.publish_date) if article.publish_date else None,
            "content": full_text,
            "sentiment": analysis["sentiment"],
            "geo_analysis": {
                "topic": analysis["geo_topic"],
                "score": analysis["geo_score"],
                "impact_class": analysis["impact_class"],
                "predicted_trend": analysis["predicted_trend"],
                "geo_topics": analysis.get("geo_topics", []),
                "macro_category": analysis.get("macro_category", "other")
            },
            "cryptos": cryptos,
            "crypto_labels": crypto_labels,
            "fetched_at": datetime.now(timezone.utc).isoformat(),
            "created_at": datetime.now(timezone.utc),
            "source_type": source_type,
            "platform": platform
        })
        uid = f"{platform}_{hash(url)}"
        simple_logger.send_raw_data_log({
            "uid": uid,
            "title": article.title,
            "source": f"{source_name} ({platform})",
            "url": url,
            "sentiment": analysis["sentiment"],
            "geo_topic": analysis["geo_topic"],
            "geo_score": analysis["geo_score"],
            "impact_class": analysis["impact_class"],
            "geo_topics": analysis.get("geo_topics", []),
            "macro_category": analysis.get("macro_category", "other"),
            "crypto_labels": crypto_labels,
            "published_at": str(article.publish_date) if article.publish_date else None,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "source_type": source_type,
            "horizon": source_type,
            "platform": platform
        }, metric="rss_sentiment")
        # --- Memory clean-up ---
        del article, full_text, analysis, cryptos, crypto_labels
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
    except Exception as e:
        logger.send_log(f"⚠️ Erreur article : {e}", "error")

def fetch_feed_with_user_agent(url):
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        return feedparser.parse(response.content)
    except Exception as e:
        logger.send_log(f"[!] RSS fetch failed : {e}", "error")
        return None

def fetch_rss_detailed(source):
    logger.send_log(f"🔍 Source: {source['name']} ({source.get('language', 'unknown')})", "info")
    feed = fetch_feed_with_user_agent(source['url'])
    if not feed or feed.bozo:
        logger.send_log(f"❌ RSS invalide : {getattr(feed, 'bozo_exception', 'unknown error')}", "error")
        return
    now = datetime.now(timezone.utc)
    for entry in feed.entries:
        try:
            published = None
            try:
                if hasattr(entry, 'published_parsed') and entry.published_parsed:
                    published = datetime(*entry.published_parsed[:6], tzinfo=timezone.utc)
                elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                    published = datetime(*entry.updated_parsed[:6], tzinfo=timezone.utc)
                elif hasattr(entry, 'published'):
                    published = dateutil_parser.parse(entry.published).astimezone(timezone.utc)
                elif hasattr(entry, 'updated'):
                    published = dateutil_parser.parse(entry.updated).astimezone(timezone.utc)
                elif 'dc:date' in entry:
                    published = dateutil_parser.parse(entry['dc:date']).astimezone(timezone.utc)
            except Exception as e:
                logger.send_log(f"⚠️ Erreur parsing date: {e}", "warning")

            if (now - published).total_seconds() > 300:
                continue

            real_url = resolve_real_article_url_bitrss(entry.link) if "bitrss.com" in entry.link else entry.link
            process_article(real_url, source["name"], source_type="rss", platform="rss")

        except Exception as e:
            logger.send_log(f"⚠️ RSS entry failed : {e}", "error")

def aggregate_recent_news():
    INTERVAL_MINUTES = 5
    now = datetime.utcnow().replace(second=0, microsecond=0)
    minute_bucket = now.minute - (now.minute % INTERVAL_MINUTES)
    window_start = now.replace(minute=minute_bucket)
    window_end = window_start + timedelta(minutes=INTERVAL_MINUTES)

    articles = list(mongo.db["news_data"].find({
        "created_at": {"$gte": window_start, "$lt": now}
    }))

    if not articles:
        logger.send_log("⚠️ Aucun article trouvé dans la fenêtre récente — agrégation annulée.", "warning")
        return

    all_cryptos = set()
    for a in articles:
        all_cryptos.update(a.get("cryptos", []))

    if "general" in all_cryptos:
        crypto_set = mongo.db["news_data"].distinct("cryptos")
        crypto_set = list(set([c for sub in crypto_set for c in (sub if isinstance(sub, list) else []) if c != "general"]))
    else:
        crypto_set = list(all_cryptos)

    for crypto in crypto_set:
        filtered_articles = [
            a for a in articles
            if "general" in a.get("cryptos", []) or crypto in a.get("cryptos", [])
        ]
        if not filtered_articles:
            continue

        sentiments = []
        geo_scores = []
        trends = []
        impacts = {"low": 0, "moderate": 0, "high": 0}
        embeddings = []

        for a in filtered_articles:
            s = a.get("sentiment", "neutral")
            sentiments.append({"positive": 1, "neutral": 0, "negative": -1}.get(s, 0))

            g = a.get("geo_analysis", {})
            geo_scores.append(g.get("score", 0.0))
            trends.append({"bullish": 1, "neutral": 0, "bearish": -1}.get(g.get("predicted_trend", "neutral"), 0))
            impacts[g.get("impact_class", "low")] += 1

            if "embedding" in a:
                embeddings.append(a["embedding"])

        entry = {
            "window_start": window_start,
            "window_end": window_end,
            "crypto": crypto,
            "nb_articles": len(filtered_articles),
            "mean_sentiment": mean(sentiments),
            "var_sentiment": np.var(sentiments),
            "mean_geo_score": mean(geo_scores),
            "max_geo_score": max(geo_scores),
            "trend_encoded": mean(trends),
            "impact_low": impacts["low"],
            "impact_moderate": impacts["moderate"],
            "impact_high": impacts["high"],
            "content_embedding": get_embedding_mean(embeddings),
            "created_at": now
        }

        exists = mongo.db["news_status"].find_one({
            "crypto": crypto,
            "window_start": entry["window_start"]
        })
        if exists:
            logger.send_log(f"⏩ Agrégation déjà existante pour {crypto} @ {window_start.isoformat()} — ignorée.", "info")
            continue

        mongo.db["news_status"].insert_one(entry)
        logger.send_log(f"[+] Features agrégées pour {crypto} @ {window_start.isoformat()}", "debug")

if __name__ == '__main__':
    rss_list_path = os.path.join(os.path.dirname(__file__), 'rss_list.json')
    if not os.path.exists(rss_list_path):
        logger.send_log(f"[⚠️] Fichier rss_list.json introuvable à l'emplacement attendu : {rss_list_path}", "error")
        sys.exit(1)
    with open(rss_list_path, 'r') as file:
        rss_feed_sources = json.load(file)

    while True:
        logger.send_log("🔄 Démarrage du polling de toutes les sources RSS et X...", "info")
        for source in rss_feed_sources:
            logger.send_log(f"🔎 Vérification de la source : {source['name']} ({source.get('type')})", "debug")
            if not source.get("enabled", True):
                continue

            source_type = source.get("type", "rss")

            if source_type == "rss":
                if source['name'].lower() == 'cryptoslate':
                    continue
                fetch_rss_detailed(source)

            elif source_type == "x":
                try:
                    logger.send_log(f"🔍 Source X: {source['name']}", "info")
                    username = source["url"].rstrip("/").split("/")[-1]
                    rsshub_url = f"https://rsshub.app/twitter/user/{username}"
                    logger.send_log(f"🧪 RSSHub URL: {rsshub_url}", "info")
                    feed = feedparser.parse(rsshub_url)
                    if not feed.entries:
                        logger.send_log(f"⚠️ Aucune entrée via /twitter/user/, fallback sur /nitter/user/ pour {source['name']}", "warning")
                        rsshub_url = f"https://rsshub.app/nitter/user/{username}"
                        feed = feedparser.parse(rsshub_url)
                    logger.send_log(f"📃 {len(feed.entries)} entrées reçues pour {source['name']}", "debug")
                    if not feed.entries:
                        logger.send_log(f"⚠️ Toujours aucune entrée pour {source['name']} après fallback", "warning")
                    for item in feed.entries:
                        real_url = item.get("link")
                        if real_url:
                            process_article(real_url, source["name"], source_type="x", platform="twitter")
                except Exception as e:
                    logger.send_log(f"⚠️ RSSHub fetch failed pour {source['name']} : {e}", "error")

        logger.send_log("🚀 Toutes les sources traitées. Lancement de l'agrégation...", "info")
        try:
            aggregate_recent_news()
            logger.send_log("✅ Agrégation terminée avec succès.", "info")
        except Exception as e:
            logger.send_log(f"❌ Échec de l'agrégation : {e}", "error")

        logger.send_log("⏳ Attente de 5 minutes avant le prochain cycle...", "info")
        time.sleep(300)
        
