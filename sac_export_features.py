#!/usr/bin/env python3
# fix_export_features.py - Version corrigée de l'export des features

import os
import sys
import time
import traceback
import pathlib
import argparse
import datetime as dt
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional

from common.mongo_utils import MongoUtils, CRYPTO_TEST
from common.grafana_utils import GrafanaUtils

# Configuration
PARQUET_DIR = pathlib.Path("data/features")
PARQUET_DIR.mkdir(parents=True, exist_ok=True)
LOOKBACK = 2000  # Nombre de bougies à récupérer
MIN_CANDLES = 100  # Nombre minimum de bougies requis

# Logger
logger = GrafanaUtils(service="sac_export")

# Connexion MongoDB
mongo = MongoUtils(logger=logger)
mongo.connect(service="sac_export")

def parse_args():
    """Parse les arguments de la ligne de commande."""
    parser = argparse.ArgumentParser(description="Exporte les features pour les modèles SAC")
    parser.add_argument("--asset", type=str, help="Asset à traiter (ex: BTCEUR)")
    parser.add_argument("--force", action="store_true", help="Force la régénération même si le fichier existe")
    return parser.parse_args()

def calculate_features(candles):
    """Calcule les features à partir des données de bougies."""
    print(f"Calcul des features pour {len(candles)} bougies")
    
    try:
        # Convertir en DataFrame en excluant la colonne _id
        df = pd.DataFrame([{k: v for k, v in c.items() if k != '_id'} for c in candles])
        print(f"DataFrame créé avec {len(df)} lignes et {len(df.columns)} colonnes")
        print(f"Colonnes: {df.columns.tolist()}")
        
        # Vérifier les colonnes requises
        required_cols = ["openTime", "openPrice", "highPrice", "lowPrice", "lastPrice", "volume"]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"Colonnes manquantes: {missing_cols}")
            logger.send_log("missing_columns", "error", extra_labels={"missing": missing_cols})
            return pd.DataFrame()
        
        # Renommer les colonnes
        df = df.rename(columns={
            "openTime": "time",
            "openPrice": "open",
            "highPrice": "high",
            "lowPrice": "low",
            "lastPrice": "close",
            "volume": "volume"
        })
        
        # Ajouter un timestamp run_at
        df["run_at"] = dt.datetime.now(dt.timezone.utc)
        
        # Ajouter les colonnes requises pour l'environnement MultiHorizonEnv
        if "uid" not in df.columns and "symbol" in df.columns:
            df["uid"] = df["symbol"]
        elif "uid" not in df.columns:
            df["uid"] = "unknown"
            
        if "horizon_h" not in df.columns:
            df["horizon_h"] = 3  # Valeur par défaut
            
        if "expected_ret" not in df.columns:
            # Calculer un rendement attendu simple basé sur les données historiques
            if "close" in df.columns and len(df) > 1:
                df["expected_ret"] = df["close"].pct_change().fillna(0)
            else:
                df["expected_ret"] = 0.0
        
        # Convertir les colonnes numériques en float
        numeric_cols = ["open", "high", "low", "close", "volume"]
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Normaliser les features (z-score)
        for col in df.columns:
            if col not in ["time", "run_at", "uid", "horizon_h"] and pd.api.types.is_numeric_dtype(df[col]):
                mean = df[col].mean()
                std = df[col].std()
                if std > 0:
                    df[f"{col}_z"] = (df[col] - mean) / std
                else:
                    df[f"{col}_z"] = 0
        
        # Supprimer les lignes avec des NaN
        original_len = len(df)
        df = df.dropna()
        dropped_rows = original_len - len(df)
        if dropped_rows > 0:
            print(f"{dropped_rows} lignes avec des NaN supprimées")
            logger.send_log("dropped_nan_rows", "warning", extra_labels={"count": dropped_rows})
        
        print(f"Calcul des features terminé: {len(df)} lignes, {len(df.columns)} colonnes")
        return df
    except Exception as e:
        print(f"Erreur lors du calcul des features: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        logger.send_log("feature_calculation_error", "error", extra_labels={
            "error": str(e),
            "traceback": traceback.format_exc()[:500]  # Limiter la taille
        })
        return pd.DataFrame()  # Retourner un DataFrame vide en cas d'erreur

def export_features_for_asset(uid: str, force: bool = False) -> bool:
    """Exporte les features pour un asset spécifique."""
    start_time = time.time()
    print(f"Début de l'export pour {uid} (force={force})")
    logger.send_log("export_asset_started", "info", extra_labels={"uid": uid, "force": force})
    
    try:
        # Vérifier si le fichier existe déjà
        parquet_file = PARQUET_DIR / f"features-{uid}.parquet"
        print(f"Chemin du fichier parquet: {parquet_file}")
        
        # Vérifier si le répertoire existe
        if not PARQUET_DIR.exists():
            print(f"Le répertoire {PARQUET_DIR} n'existe pas, création...")
            PARQUET_DIR.mkdir(parents=True, exist_ok=True)
        
        if parquet_file.exists() and not force:
            print(f"Le fichier {parquet_file} existe déjà et force=False")
            logger.send_log("file_exists", "info", extra_labels={"uid": uid, "path": str(parquet_file)})
            return True
        
        # Récupérer les bougies
        print(f"Récupération des bougies pour {uid}...")
        candles = list(mongo.db["candlesticks"].find(
            {"uid": uid},
            sort=[("openTime", -1)],
            limit=LOOKBACK
        ))
        print(f"Nombre de bougies récupérées: {len(candles)}")
        
        if len(candles) < MIN_CANDLES and not force:
            print(f"Nombre de bougies insuffisant: {len(candles)} < {MIN_CANDLES}")
            logger.send_log("insufficient_candles", "warning", extra_labels={
                "uid": uid, "count": len(candles), "min_required": MIN_CANDLES
            })
            return False
        
        # Calculer les features
        print(f"Calcul des features...")
        df = calculate_features(candles)
        
        if df.empty:
            print(f"DataFrame vide après calcul des features")
            logger.send_log("empty_dataframe", "warning", extra_labels={"uid": uid})
            return False
        
        print(f"Sauvegarde du DataFrame ({len(df)} lignes) en parquet: {parquet_file}")
        # Sauvegarder en parquet
        try:
            df.to_parquet(parquet_file, index=False)
            print(f"Sauvegarde en parquet réussie")
        except Exception as e:
            print(f"Erreur lors de la sauvegarde en parquet: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            logger.send_log("parquet_save_error", "error", extra_labels={
                "uid": uid,
                "error": str(e),
                "traceback": traceback.format_exc()[:500]
            })
            return False
        
        # Vérifier que le fichier a été créé
        if parquet_file.exists():
            file_size = parquet_file.stat().st_size
            print(f"Fichier créé avec succès: {parquet_file} ({file_size} octets)")
            logger.send_log("features_exported", "info", extra_labels={
                "uid": uid,
                "path": str(parquet_file),
                "rows": len(df),
                "size_bytes": file_size
            })
            return True
        else:
            print(f"Échec de la création du fichier: {parquet_file}")
            logger.send_log("file_creation_failed", "error", extra_labels={
                "uid": uid,
                "path": str(parquet_file)
            })
            return False
    except Exception as e:
        print(f"Erreur lors de l'export: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        logger.send_log("export_features_error", "error", extra_labels={
            "uid": uid,
            "error": str(e),
            "traceback": traceback.format_exc()[:500]
        })
        return False

def get_assets_to_process() -> List[str]:
    """Récupère la liste des assets à traiter."""
    try:
        # Récupérer les assets depuis MongoDB
        assets = mongo.db["candlesticks"].distinct("uid")
        valid_uids = [uid for uid in assets if uid and isinstance(uid, str)]
        
        logger.send_log("assets_found", "info", extra_labels={
            "count": len(valid_uids),
            "assets": valid_uids[:10]  # Limiter à 10 pour éviter des logs trop grands
        })
        return valid_uids
    except Exception as e:
        logger.send_log("get_assets_error", "error", extra_labels={"error": str(e)})
        return CRYPTO_TEST

def main():
    logger.send_log("export_started", "info")
    
    args = parse_args()
    
    # Récupérer les assets à traiter
    if args.asset:
        assets = [args.asset]
    else:
        assets = get_assets_to_process()
        
    logger.send_log("assets_to_process", "info", extra_labels={
        "count": len(assets),
        "assets": assets[:10]  # Limiter à 10 pour éviter des logs trop grands
    })
    
    results = {
        "success": [],
        "failed": []
    }
    
    # Traiter chaque asset
    for uid in assets:
        try:
            success = export_features_for_asset(uid, force=args.force)
            if success:
                results["success"].append(uid)
            else:
                results["failed"].append(uid)
        except Exception as e:
            logger.send_log("asset_processing_error", "error", extra_labels={
                "uid": uid,
                "error": str(e)
            })
            results["failed"].append(uid)
    
    # Afficher les résultats
    print(f"\nRésultats de l'export:")
    print(f"  Succès: {len(results['success'])}/{len(assets)}")
    print(f"  Échecs: {len(results['failed'])}/{len(assets)}")
    
    if results["failed"]:
        print(f"\nAssets en échec: {results['failed']}")
    
    logger.send_log("export_completed", "info", extra_labels={
        "success_count": len(results["success"]),
        "failed_count": len(results["failed"]),
        "total_count": len(assets),
        "failed_assets": results["failed"][:10]  # Limiter à 10 pour éviter des logs trop grands
    })
    
    return len(results["failed"]) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
