import json
import subprocess
from common.grafana_utils import GrafanaUtils

SERVICE = "whitelist"
logger = <PERSON>anaUtils(service=SERVICE)

WHITELIST_FILE = "whitelist.json"
PORTS = [27017, 3000, 1880, 3001]
PORTS_OPEN_TO_ALL = [22, 80, 443]



def run(cmd):
    logger.send_log(f"Exécution de la commande : {cmd}", "info")
    try:
        subprocess.run(cmd, shell=True, check=True)
    except subprocess.CalledProcessError as e:
        logger.send_log(f"Erreur lors de l'exécution de la commande '{cmd}' : {e}", "error")

def load_whitelist():
    try:
        with open(WHITELIST_FILE, "r") as f:
            whitelist = json.load(f)
            logger.send_log(f"Fichier whitelist chargé avec succès : {len(whitelist)} IP(s)", "info")
            return whitelist
    except Exception as e:
        logger.send_log(f"Erreur lors de la lecture du fichier {WHITELIST_FILE} : {e}", "error")
        return []

def apply_firewall_rules(whitelist):
    logger.send_log("🔧 Réinitialisation des règles ufw...", "info")
    run("sudo ufw --force reset")
    run("sudo ufw default deny incoming")
    run("sudo ufw default allow outgoing")

    # 🔐 Assure l'accès SSH en toutes circonstances
    run("sudo ufw allow ssh")
    logger.send_log("🔓 Accès SSH explicitement autorisé (port 22)", "info")

    # 🔁 Autoriser le trafic local (loopback)
    run("sudo ufw allow in on lo")
    run("sudo ufw allow out on lo")
    logger.send_log("🔁 Trafic local autorisé sur lo (loopback)", "info")

    # 🔐 Règles IP spécifiques
    for ip in whitelist:
        for port in PORTS:
            run(f"sudo ufw allow from {ip} to any port {port}")
            logger.send_log(f"✅ Règle ajoutée : {ip} → port {port}", "info")

    # 🌍 Règles ouvertes à tout le monde
    for port in PORTS_OPEN_TO_ALL:
        run(f"sudo ufw allow {port}")
        logger.send_log(f"🌍 Port public ouvert à tous : {port}", "info")

    logger.send_log("✅ Activation du pare-feu", "info")
    run("sudo ufw --force enable")


def disable_firewall():
    logger.send_log("🛑 Désactivation complète du pare-feu UFW...", "info")

    # Réinitialiser toutes les règles
    run("sudo ufw --force reset")

    # Accepter tout le trafic entrant et sortant
    run("sudo ufw default allow incoming")
    run("sudo ufw default allow outgoing")

    # Désactiver ufw
    run("sudo ufw --force disable")

    logger.send_log("✅ Pare-feu désactivé et règles supprimées", "info")


def main():
    logger.send_log("🚀 Début de la mise à jour du pare-feu via whitelist.json", "info")
    whitelist = load_whitelist()
    if whitelist:
        try:
            apply_firewall_rules(whitelist)
            logger.send_log("🎯 Mise à jour du pare-feu terminée avec succès", "info")
        except Exception as e:
            disable_firewall()
            logger.send_log(f"❌ Échec de l'application des règles UFW : {e}", "error")
    else:
        disable_firewall()
        logger.send_log("⛔ Aucune règle appliquée : whitelist vide ou erreur de lecture", "warning")

if __name__ == "__main__":
    
    main()
