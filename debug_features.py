#!/usr/bin/env python3
# debug_features.py - Débogage de l'export des features

import os
import sys
import time
import traceback
import pathlib
import datetime as dt
import pandas as pd
import numpy as np
from bson import ObjectId

from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# Configuration
PARQUET_DIR = pathlib.Path("data/features")
PARQUET_DIR.mkdir(parents=True, exist_ok=True)

# Logger
logger = GrafanaUtils(service="debug_features")

# Connexion MongoDB
mongo = MongoUtils(logger=logger)
mongo.connect(service="debug_features")

def debug_features():
    """Débogage de l'export des features."""
    try:
        print("Récupération des bougies...")
        candles = list(mongo.db["candlesticks"].find(
            {"uid": "BTCEUR"},
            sort=[("openTime", -1)],
            limit=10
        ))
        
        print(f"Nombre de bougies récupérées: {len(candles)}")
        
        # Afficher le premier élément pour inspection
        print("\nPremier élément (avant conversion):")
        for k, v in candles[0].items():
            print(f"  {k}: {v} (type: {type(v)})")
        
        # Convertir en DataFrame
        df = pd.DataFrame(candles)
        print(f"\nDataFrame créé avec {len(df)} lignes et {len(df.columns)} colonnes")
        print(f"Colonnes: {df.columns.tolist()}")
        
        # Supprimer la colonne _id
        if '_id' in df.columns:
            print("\nSuppression de la colonne _id...")
            df = df.drop(columns=['_id'])
            print("Colonne _id supprimée")
        
        # Renommer les colonnes
        print("\nRenommage des colonnes...")
        df = df.rename(columns={
            "openTime": "time",
            "openPrice": "open",
            "highPrice": "high",
            "lowPrice": "low",
            "lastPrice": "close",
            "volume": "volume"
        })
        
        # Ajouter un timestamp run_at
        df["run_at"] = dt.datetime.now(dt.timezone.utc)
        
        # Convertir les colonnes numériques en float
        print("\nConversion des colonnes numériques...")
        numeric_cols = ["open", "high", "low", "close", "volume"]
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Normaliser les features (z-score)
        print("\nNormalisation des features...")
        for col in df.columns:
            if col not in ["time", "run_at", "uid"] and pd.api.types.is_numeric_dtype(df[col]):
                mean = df[col].mean()
                std = df[col].std()
                if std > 0:
                    df[f"{col}_z"] = (df[col] - mean) / std
                else:
                    df[f"{col}_z"] = 0
        
        # Supprimer les lignes avec des NaN
        original_len = len(df)
        df = df.dropna()
        dropped_rows = original_len - len(df)
        if dropped_rows > 0:
            print(f"\n{dropped_rows} lignes avec des NaN supprimées")
        
        print(f"\nCalcul des features terminé: {len(df)} lignes, {len(df.columns)} colonnes")
        
        # Sauvegarder en parquet
        parquet_file = PARQUET_DIR / "features-BTCEUR-debug.parquet"
        print(f"\nSauvegarde du DataFrame en parquet: {parquet_file}")
        
        df.to_parquet(parquet_file, index=False)
        
        # Vérifier que le fichier a été créé
        if parquet_file.exists():
            file_size = parquet_file.stat().st_size
            print(f"Fichier créé avec succès: {parquet_file} ({file_size} octets)")
            return True
        else:
            print(f"Échec de la création du fichier: {parquet_file}")
            return False
    except Exception as e:
        print(f"Erreur lors du débogage: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    debug_features()