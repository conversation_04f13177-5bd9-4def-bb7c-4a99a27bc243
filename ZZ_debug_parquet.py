import pathlib
import pandas as pd

# Dossier des parquets (avec chemin complet)
PARQUET_DIR = pathlib.Path("/home/<USER>/cryptobot/data/features")

parquet_files = sorted(PARQUET_DIR.glob("features-*.parquet"))

summary = []

print(f"Audit des fichiers parquet dans {PARQUET_DIR}...\n")
for pf in parquet_files:
    uid = pf.stem.replace("features-", "")
    try:
        df = pd.read_parquet(pf)
        if "time" not in df.columns or "close" not in df.columns:
            print(f"⛔ {uid}: Colonnes manquantes !")
            continue
        last_row = df.iloc[-1]
        summary.append({
            "asset": uid,
            "last_time": str(last_row["time"]),
            "last_close": float(last_row["close"]),
            "nb_rows": len(df)
        })
    except Exception as e:
        print(f"⚠️ Erreur lecture {pf.name}: {e}")

if summary:
    print(f"{'Asset':<12} {'Last Time':<22} {'Last Close':<15} {'Rows'}")
    print("-"*55)
    for row in summary:
        print(f"{row['asset']:<12} {row['last_time']:<22} {row['last_close']:<15.2f} {row['nb_rows']}")
else:
    print("Aucun fichier traité.")

# Bonus : détection d'assets "figés"
from collections import Counter
last_times = [row["last_time"] for row in summary]
duplicates = [item for item, count in Counter(last_times).items() if count > 1]

if duplicates:
    print("\nAssets avec même last_time (potentiel freeze) :")
    for row in summary:
        if row["last_time"] in duplicates:
            print(f"  {row['asset']} -> {row['last_time']} ({row['last_close']})")
