import time
from datetime import datetime, timedelta, timezone
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils
from common.indicators_utils import IndicatorsUtils
from common.models_utils import ModelsUtils


SERVICE = "train_models"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)
indicator = IndicatorsUtils(service=SERVICE)
models = ModelsUtils(service=SERVICE)




# 🔧 Initialisation dans chaque process enfant
def init_worker():
    global logger, mongo, models, indicator
    logger = GrafanaUtils(service=SERVICE)
    mongo = MongoUtils()
    mongo.connect(service=SERVICE)
    models = ModelsUtils(service=SERVICE)
    indicator = IndicatorsUtils(service=SERVICE)



def process_crypto(uid, horizon):
    """
    Lance l'entraînement du modèle pour une crypto donnée et un horizon spécifique.
    """
    base_log = f"{uid} - {horizon['name']}"
    safe_shift_points = 0

    # Récupération des candlesticks
    try :
        until_date = datetime.utcnow()
        candlesticks = mongo.get_candlesticks(uid, until_date)
        if len(candlesticks) == 0:
            logger.send_log(f"⚠️ - {base_log} - Aucun candlesticks trouvés","error")
            return None
    except Exception as e:
        logger.send_log(f"⚠️ - {base_log} - Erreur lors de get_candelsticks : {e}","error")
        return None

    # 1. Récupération des hyperparamètres du modèle
    hyperparams = mongo.get_hyperparams(horizon["name"], uid, horizon["horizon_hour"])
    if not hyperparams:
        logger.send_log(f"⚠️ Aucun hyperparamètre trouvé pour {base_log}", "warning")
        return
    


    best_loss = None
    best_metrics = 0
    best_loss_metrics = None
    best_hyperparams = None
    # Pour chacun des hyperparams trouvés
    for hyperparam in hyperparams:
        # 2. Récupération des paramètres d’indicateurs optimisés
        indicator_params = hyperparam['indicator_params']
        logger.send_log(f"⚠️ indicator_params = {indicator_params}", "warning")
        if not indicator_params:
            logger.send_log(f"⚠️ Aucun paramètre d’indicateur trouvé pour {base_log}", "warning")
            return

        # 3. Préparation du jeu de données pour l’entraînement
        try:
            df = indicator.generate_training_sample(
                candlesticks, indicator_params, uid, horizon["shift_value"],
                horizon["horizon_hour"], "max", safe_shift_points=safe_shift_points
                )
        except Exception as e:
            logger.send_log(f"❌ Erreur préparation données pour {base_log} : {e}", "error")
            return

        if df is None or df.empty:
            logger.send_log(f"⚠️ DataFrame vide pour {base_log}, entraînement annulé", "warning")
            return

        # 4. Entraînement du modèle de régression avec améliorations

        # Configuration du modèle haute performance
        hyperparam['best_params']['model_type'] = 'high_performance'  # Utiliser le modèle haute performance
        #hyperparam['best_params']['epochs'] = 100  # Plus d'époques pour convergence
        #hyperparam['best_params']['batch_size'] = 32  # Batch size plus petit pour stabilité
        hyperparam['best_params']['patience'] = 15  # Plus de patience

        # Fonctions de perte optimisées (ordre de priorité)
        loss_function = [
            "anti_suppression",      # Nouvelle fonction anti-suppression
            "balanced_reward",       # Fonction avec récompenses
            "correlation_mae_amplitude",  # Fonction équilibrée
            "corr_mae_amp_deriv",   # Avec dérivées
            "aggressive_loss",       # Agressive pour horizons longs
            "adaptive_dual_objective",  # Adaptative
            "correlation_huber",    # Robuste
            "corr_mae_amp_bias",    # Avec biais
            "directional_loss",
            "amplitude_directional",
            "dual_objective",
            "pearson_da_amplitude",
            "amplitude_directional_reversal"
        ]
        

        for loss in loss_function:
            hyperparam['best_params']['loss_type'] = loss

            try:
                logger.send_log(f"✅ - {base_log} - Entrainement demandé avec hyperparamètre ({hyperparam['_id']}) et loss type = {loss}", "info")
                metrics = models.train_regression_model(
                    uid=uid,
                    horizon=horizon,
                    df=df,
                    hyperparams=hyperparam['best_params'],
                    save_model=False
                )

                if metrics:
                    # Récupération du score combiné des métriques
                    combined_score = metrics.get('combined_score', 0)
                    correlation = metrics.get('pearson_corr', 0)

                    logger.send_log(f"✅ - {base_log} - Entraînement réussi avec {loss} - Score: {combined_score:.3f}, Corr: {correlation:.3f}", "info")

                    # Sélection du meilleur modèle basé sur le score combiné
                    if best_loss is None or combined_score > best_metrics:
                        best_metrics = combined_score
                        best_loss = loss
                        best_loss_metrics = metrics
                        best_hyperparams = hyperparam
            except Exception as e:
                logger.send_log(f"❌ Erreur entraînement modèle pour {base_log} : {e}", "error")
                return


    # Entraine le modèle avec les meilleurs hyperparams possible
    if best_loss:
        logger.send_log(f"✅ - {base_log} - Meilleur loss function : {best_loss} avec un score de {best_metrics:.3f}", "info")

        # Log des métriques détaillées du meilleur modèle
        if best_loss_metrics:
            logger.send_log(f"📊 - {base_log} - Métriques détaillées du meilleur modèle:", "info")
            logger.send_log(f"📊 - {base_log} - Corrélation: {best_loss_metrics.get('pearson_corr', 0):.4f}", "info")
            logger.send_log(f"📊 - {base_log} - MAE: {best_loss_metrics.get('mae', 0):.6f}", "info")
            logger.send_log(f"📊 - {base_log} - RMSE: {best_loss_metrics.get('rmse', 0):.6f}", "info")
            logger.send_log(f"📊 - {base_log} - Directional Accuracy: {best_loss_metrics.get('trend_directional_accuracy', 0):.4f}", "info")
            logger.send_log(f"📊 - {base_log} - Amplitude Loss: {best_loss_metrics.get('amplitude_loss', 0):.6f}", "info")

        df = indicator.generate_training_sample(
            candlesticks, best_hyperparams['indicator_params'], uid, horizon["shift_value"],
            horizon["horizon_hour"], "max", safe_shift_points=safe_shift_points
            )

        best_hyperparams['best_params']['loss_type'] = best_loss

        # Entraînement final avec sauvegarde
        final_metrics = models.train_regression_model(
            uid=uid,
            horizon=horizon,
            df=df,
            hyperparams=best_hyperparams['best_params'],
            save_model=True
        )

        if final_metrics:
            logger.send_log(f"💾 - {base_log} - Modèle final sauvegardé avec succès", "info")


        del df

    #logger.send_log(f"✅ Modèle entraîné avec succès pour {base_log}", "info")



# 👷 Worker d'entraînement d’un modèle pour une crypto et un horizon
def train_worker(uid, horizon):
    init_worker()
    base_log = f"{uid} - {horizon['name']}"
    try:
        process_crypto(uid, horizon)
        #logger.send_log(f"✅ Entraînement terminé : {base_log}", "info")
        return f"✅ Entraînement terminé : {base_log}"
    except Exception as e:
        #logger.send_log(f"❌ Erreur entraînement {base_log} : {e}", "error")
        return f"❌ Erreur entraînement {base_log} : {e}"



# ⚙️ Lancement de l’entraînement pour toutes les cryptos et horizons
def train_models_for_all(trading_uids, horizons, max_workers=1):
    logger.send_log("🚀 Lancement des entraînements de modèles", "info")
    logger.send_log(f"📌 {len(trading_uids)} cryptos × {len(horizons)} horizons à entraîner...", "info")

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(train_worker, uid, horizon) for uid in trading_uids for horizon in horizons]

        for future in as_completed(futures):
            msg = future.result()
            logger.send_log(msg, "info" if "✅" in msg else "error")

    logger.send_log("🏁 Tous les modèles ont été entraînés.", "info")
    return True


# 🔁 Exécution d’un cycle complet d'entraînement (séquentiel côté master)
def run_training_cycle():
    trading_uids = mongo.get_trading_pairs()

    if not trading_uids:
        logger.send_log("⚠️ Aucune crypto à entraîner !", "warning")
        return False

    horizons = mongo.get_all_horizons()

    if not horizons:
        logger.send_log("⚠️ Aucun horizon trouvé en DB !", "warning")
        return False

    try:
        train_models_for_all(trading_uids, horizons, max_workers=1)
    except Exception as e:
        logger.send_log(f"❌ Erreur durant le cycle d'entraînement : {e}", "error")

    logger.send_log("✅ Cycle d'entraînement terminé.", "info")
    return True


# 🔁 Boucle principale qui répète les entraînements
def main():
    INTERVAL = 60  # secondes
    error_count = 0

    while True:
        try:
            logger.send_log("🔁 Nouveau cycle d'entraînement des modèles...", "info")
            success = run_training_cycle()

            if success:
                logger.send_log(f"✅ Cycle terminé. Prochain lancement dans {INTERVAL} sec...", "info")
                error_count = 0
            else:
                logger.send_log("⚠️ Aucun entraînement effectué.", "warning")

        except Exception as e:
            error_count += 1
            logger.send_log(f"❌ Erreur dans `main()` : {e} (tentative {error_count})", "error")
            if error_count >= 5:
                logger.send_log("🚨 Trop d'erreurs consécutives. Arrêt du script.", "critical")
                break

        time.sleep(INTERVAL)

    logger.send_log("👋 Script d'entraînement arrêté proprement.", "info")


# 🧠 Point d'entrée principal
if __name__ == "__main__":
    main()
