import requests
import json
import time
from datetime import datetime
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils


SERVICE = "market_pairs"
logger = GrafanaUtils(service=SERVICE)
mongo = MongoUtils()
mongo.connect(service=SERVICE)

BINANCE_URL = "https://api.binance.com/api/v3/exchangeInfo?permissions=SPOT"
#FETCH_INTERVAL = 60 * 60  # 1 heure en secondes


def extract_pairs(data):
    symbols = data.get("symbols", [])
    priority_list = ["EUR", "EURI", "AEUR", "USDC", "USDT", "BUSD", "BTC", "BNB", "ETH"]

    # Construction du dictionnaire symbol -> données
    symbol_map = {entry["symbol"]: entry for entry in symbols}

    # Fonction pour construire un symbole enrichi
    def build_symbol_data(entry):
        return {
            "uid": entry["symbol"],
            "symbol": entry["symbol"],
            "status": entry["status"],
            "from_asset": entry["baseAsset"],
            "to_asset": entry["quoteAsset"],
            "filters": entry.get("filters", []),
            "order_types": entry.get("orderTypes", [])
        }

    # Étape 1 : enrichir tous les symboles
    updated_list = [build_symbol_data(entry) for entry in symbols]

    # Étape 2 : détecter les doublons sur le from_asset et garder le meilleur selon la priorité
    sorted_list = []
    for coin in updated_list:
        duplicated_assets = [c for c in updated_list if c["from_asset"] == coin["from_asset"]]
        duplicated_assets.sort(
            key=lambda x: priority_list.index(x["to_asset"]) if x["to_asset"] in priority_list else len(priority_list)
        )
        sorted_list.append(duplicated_assets[0])

    # Étape 3 : supprimer les doublons de symboles
    seen = set()
    reduced_list = []
    for item in sorted_list:
        if item["symbol"] not in seen:
            seen.add(item["symbol"])
            reduced_list.append(item)

    return reduced_list




def fetch_active_pairs():
    try:
        response = requests.get(BINANCE_URL)
        response.raise_for_status()
        data = response.json()

        active_symbols = extract_pairs(data)
        mongo.update_market_pairs(active_symbols)

        logger.send_log(f"✅ - {len(active_symbols)} paires updatées", "info")

        # Nettoyage des anciennes paires non présentes dans l'extraction actuelle
        uids = [pair["uid"] for pair in active_symbols]
        mongo.delete_missing_market_pairs(uids)
        
    except Exception as e:
        logger.send_log(f"❌ - Erreur pendant la récupération des paires : {e}", "error")

if __name__ == "__main__":
    fetch_active_pairs()
    mongo.disconnect()