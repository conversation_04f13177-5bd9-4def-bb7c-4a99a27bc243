import time
import math
import requests
from collections import defaultdict
from datetime import datetime, timezone, timedelta
from common.mongo_utils import MongoUtils
from common.grafana_utils import GrafanaUtils

# --- Configuration ---
SERVICE = "onchain_taker"
MEMPOOL_API_BASE = "https://mempool.space/api"
SYMBOL = "BTC"
WHALE_THRESHOLD_BTC = 100
BLOCKS_TO_FETCH = 2016  # 2 semaines de blocs
SLEEP_BETWEEN_CALLS = 0.2  # Pause pour éviter le rate-limit

# --- Initialisations ---
logger = GrafanaUtils(service="onchain_taker_log")
mongo = MongoUtils(logger=logger)
mongo.connect(service=SERVICE)

print("[INFO] Script Backfill Full Month lancé (via blocs mempool.space)")
logger.send_log("Script Backfill Full Month lancé (via blocs mempool.space)", "info")

# --- Fonctions Utilitaires ---
def get_current_height():
    try:
        response = requests.get(f"{MEMPOOL_API_BASE}/blocks", timeout=15)
        response.raise_for_status()
        return response.json()[0]["height"]
    except Exception as e:
        logger.send_log(f"❌ Erreur récupération height courant : {e}", "error")
        return None

def fetch_block_hash(height):
    try:
        response = requests.get(f"{MEMPOOL_API_BASE}/block-height/{height}", timeout=15)
        response.raise_for_status()
        return response.text.strip()
    except Exception as e:
        logger.send_log(f"❌ Erreur récupération hash pour height {height} : {e}", "error")
        return None

def fetch_all_block_transactions(block_hash):
    transactions = []
    page = 0
    while True:
        try:
            url = f"{MEMPOOL_API_BASE}/block/{block_hash}/txs" if page == 0 else f"{MEMPOOL_API_BASE}/block/{block_hash}/txs/{page}"
            response = requests.get(url, timeout=20)
            response.raise_for_status()
            txs = response.json()

            if not txs:
                break

            transactions.extend(txs)

            if len(txs) < 25:
                break  # Fin de pagination

            page += 1
            time.sleep(SLEEP_BETWEEN_CALLS)

        except Exception as e:
            logger.send_log(f"❌ Erreur récupération transactions page {page} pour bloc {block_hash} : {e}", "error")
            break

    logger.send_log(f"[DEBUG] {len(transactions)} txs récupérées pour bloc {block_hash}", "debug")
    return transactions

def safe_sum_prevout_value(vins):
    total = 0
    for vin in vins or []:
        if vin and isinstance(vin, dict):
            prevout = vin.get("prevout")
            if prevout and isinstance(prevout, dict):
                value = prevout.get("value", 0)
                if isinstance(value, (int, float)):
                    total += value
    return total

def safe_sum_vout_value(vouts):
    total = 0
    for vout in vouts or []:
        if vout and isinstance(vout, dict):
            value = vout.get("value", 0)
            if isinstance(value, (int, float)):
                total += value
    return total

def align_to_5min(timestamp):
    dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
    minute = (dt.minute // 5) * 5
    open_time = dt.replace(minute=minute, second=0, microsecond=0)
    close_time = open_time + timedelta(minutes=5) - timedelta(milliseconds=1)
    return open_time, close_time

# --- Main ---
def backfill_data():
    current_height = get_current_height()
    if not current_height:
        logger.send_log("❌ Impossible d'obtenir le height courant", "error")
        return

    start_height = current_height - BLOCKS_TO_FETCH
    logger.send_log(f"🔎 Récupération des blocs {start_height} -> {current_height}", "info")

    aggregation = defaultdict(lambda: {
        "inflows": 0,
        "outflows": 0,
        "inflow_count": 0,
        "outflow_count": 0,
        "inflow_values": [],
        "outflow_values": [],
        "whale_inflows": 0,
        "whale_outflows": 0,
        "whale_activity": 0
    })

    for height in range(start_height, current_height + 1):
        block_hash = fetch_block_hash(height)
        if not block_hash:
            continue

        transactions = fetch_all_block_transactions(block_hash)

        for tx in transactions:
            timestamp = tx.get("status", {}).get("block_time")
            if not timestamp:
                continue

            open_time, close_time = align_to_5min(timestamp)
            bucket_key = open_time.strftime("%Y-%m-%d %H:%M:%S UTC")
            total_output = safe_sum_vout_value(tx.get("vout", [])) / 1e8
            total_input = safe_sum_prevout_value(tx.get("vin", [])) / 1e8
            data = aggregation[bucket_key]

            if total_output >= total_input:
                data["inflows"] += total_output
                data["inflow_count"] += 1
                data["inflow_values"].append(total_output)
                if total_output >= WHALE_THRESHOLD_BTC:
                    data["whale_inflows"] += total_output
                    data["whale_activity"] += 1
            else:
                data["outflows"] += total_input
                data["outflow_count"] += 1
                data["outflow_values"].append(total_input)
                if total_input >= WHALE_THRESHOLD_BTC:
                    data["whale_outflows"] += total_input
                    data["whale_activity"] += 1

    logger.send_log(f"📊 {len(aggregation)} bougies de 5 minutes prêtes à insérer", "info")

    for bucket_key, data in aggregation.items():
        inflows = data["inflows"]
        outflows = data["outflows"]
        total_flow = inflows + outflows
        netflow = inflows - outflows
        netflow_ratio = (netflow / total_flow) if total_flow else 0
        whale_dominance_pct = (data["whale_activity"] / (data["inflow_count"] + data["outflow_count"])) * 100 if (data["inflow_count"] + data["outflow_count"]) else 0

        document = {
            "timestamp": bucket_key,
            "openTime": datetime.strptime(bucket_key, "%Y-%m-%d %H:%M:%S UTC"),
            "closeTime": datetime.strptime(bucket_key, "%Y-%m-%d %H:%M:%S UTC") + timedelta(minutes=5) - timedelta(milliseconds=1),
            "symbol": SYMBOL,
            "fetched_at": datetime.now(timezone.utc),
            "blockchain": "bitcoin",
            "bucket": bucket_key,
            "avg_inflow_value": (sum(data["inflow_values"]) / len(data["inflow_values"])) if data["inflow_values"] else 0,
            "avg_outflow_value": (sum(data["outflow_values"]) / len(data["outflow_values"])) if data["outflow_values"] else 0,
            "flow_sentiment_score": 0,
            "inflow_count": data["inflow_count"],
            "inflows": inflows,
            "median_inflow_value": sorted(data["inflow_values"])[len(data["inflow_values"])//2] if data["inflow_values"] else 0,
            "median_outflow_value": sorted(data["outflow_values"])[len(data["outflow_values"])//2] if data["outflow_values"] else 0,
            "netflow": netflow,
            "netflow_ratio": netflow_ratio,
            "outflow_count": data["outflow_count"],
            "outflows": outflows,
            "total_flow": total_flow,
            "whale_activity": data["whale_activity"],
            "whale_dominance_pct": whale_dominance_pct,
            "whale_inflows": data["whale_inflows"],
            "whale_outflows": data["whale_outflows"],
            "whale_netflow": data["whale_inflows"] - data["whale_outflows"]
        }

        try:
            mongo.db["on_chain_datas"].insert_one(document)
            logger.send_log(f"✅ Bougie {bucket_key} insérée", "info")
        except Exception as e:
            if "E11000" in str(e):
                logger.send_log(f"⏩ Doublon ignoré {bucket_key}", "debug")
            else:
                logger.send_log(f"❌ Erreur insertion Mongo : {e}", "error")

# --- Entrée script ---
if __name__ == "__main__":
    backfill_data()
